import { QueryClient } from '@tanstack/react-query';

// Create a client
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // Default configuration options for queries
      refetchOnWindowFocus: false, // Don't refetch when window regains focus
      staleTime: 1000 * 60 * 5, // 5 minutes - data remains fresh for this long
      gcTime: 1000 * 60 * 30, // 30 minutes - data remains in cache this long (formerly cacheTime)
      retry: 1, // Retry failed requests just once
    },
  },
}); 