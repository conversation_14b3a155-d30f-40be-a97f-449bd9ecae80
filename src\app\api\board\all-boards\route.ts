import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import type { Database } from '@/lib/database.types';

import { getAuthenticatedUser } from '@/utils/authUtils';

export const runtime = 'nodejs';     // make sure this stays a Node.js function
export const maxDuration = 20;       // seconds – pick anything ≤ plan limit
// Define response types
interface BoardResponse {
  id: string;
  boardName: string;
  createdAt: string;
  updatedAt: string;
  elementsCount: number;
  connectionsCount: number;
  isOwner: boolean;
  previewImageUrl: string | null;
  sharedBy?: string;
  permissionLevel?: string;
}

// Helper to extract relative path from various URL formats
function extractRelativePath(url: string | null): string | null {
  if (!url) return null;
  try {
    if (!url.startsWith('http')) return url.includes('/') ? url : null;
    
    const parsedUrl = new URL(url);
    const pathSegments = parsedUrl.pathname.split('/');
    const bucketName = 'images';
    const bucketIndex = pathSegments.indexOf(bucketName);

    if (bucketIndex !== -1 && bucketIndex < pathSegments.length - 1) {
      return pathSegments.slice(bucketIndex + 1).join('/');
    }

    return null;
  } catch (error) {
    console.error(`Error parsing URL: ${url}`, error);
    return null;
  }
}

// Get signed URL for an image (owner access)
async function getSignedUrl(supabase: any, filePath: string | null): Promise<string | null> {
  if (!filePath) return null;
  
  try {
    const { data, error } = await supabase
      .storage
      .from('images')
      .createSignedUrl(filePath, 3600);
      
    if (error) {
      console.error(`Error generating signed URL: ${error.message}`);
      return null;
    }
    
    return data?.signedUrl || null;
  } catch (error) {
    console.error('Error generating signed URL:', error);
    return null;
  }
}

// Helper function to call the edge function to get a public signed URL for shared boards
async function getPublicSignedUrl(boardId: string, urlInput: string | null, userId: string): Promise<string | null> {
  const relativePath = extractRelativePath(urlInput);
  if (!relativePath) {
    console.warn(`Skipping edge function call due to invalid path: ${urlInput}`);
    return null;
  }

  const edgeFunctionUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
    ? `${process.env.NEXT_PUBLIC_SUPABASE_URL}/functions/v1/get-public-image-url`
    : null;

  if (!edgeFunctionUrl) {
    console.error("Edge function URL (get-public-image-url) is not configured.");
    return null;
  }

  const anonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
  if (!anonKey) {
    console.error("Supabase Anon Key (NEXT_PUBLIC_SUPABASE_ANON_KEY) is not configured.");
    return null;
  }

  try {
    const response = await fetch(edgeFunctionUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${anonKey}`,
        'apikey': anonKey
      },
      body: JSON.stringify({ boardId, filePath: relativePath, userId }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: 'Failed to parse edge function error response' }));
      console.error(`Edge function call failed for board ${boardId}, path ${relativePath}:`, errorData.error || response.statusText);
      return null;
    }

    const data = await response.json();
    return data.signedUrl || null;
  } catch (error) {
    console.error(`Error calling edge function for board ${boardId}, path ${relativePath}:`, error);
    return null;
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '12', 10);
    const offset = parseInt(searchParams.get('offset') || '0', 10);
    
    const { user, error: authError, supabase } = await getAuthenticatedUser();

    if (authError || !user) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    const userId = user.id;

    // 2. Fetch user's boards with preview and counts in a single query
    const { data: ownedBoardsData, error: ownedBoardsError } = await supabase
      .from('boards')
      .select(`
        id,
        board_name,
        created_at,
        updated_at,
        preview_image_url,
        elements:elements(count),
        connections:connections(count)
      `)
      .eq('user_id', userId)
      .order('updated_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (ownedBoardsError) {
      return NextResponse.json({ message: 'Failed to fetch owned boards', error: ownedBoardsError.message }, { status: 500 });
    }

    // 3. Fetch shared boards 
    const { data: sharedBoardsInfo, error: sharedInfoError } = await supabase
      .from('board_shares')
      .select(`
        board_id,
        permission_level,
        boards!inner(
          id,
          board_name,
          created_at,
          updated_at,
          preview_image_url,
          user_id,
          elements:elements(count),
          connections:connections(count)
        )
      `)
      .eq('user_id', userId);

    if (sharedInfoError) {
      return NextResponse.json({ message: 'Failed to fetch shared boards info', error: sharedInfoError.message }, { status: 500 });
    }

    // 4. Process owned boards with regular signed URLs
    const processOwnedBoards = async () => {
      const processedBoards = await Promise.all(ownedBoardsData.map(async (board) => {
        const filePath = extractRelativePath(board.preview_image_url);
        const signedUrl = await getSignedUrl(supabase, filePath);
        return { 
          id: board.id,
          boardName: board.board_name,
          createdAt: board.created_at,
          updatedAt: board.updated_at,
          elementsCount: board.elements[0]?.count ?? 0,
          connectionsCount: board.connections[0]?.count ?? 0,
          isOwner: true,
          previewImageUrl: signedUrl || board.preview_image_url
        };
      }));
      
      return processedBoards;
    };

    // 5. Process shared boards with edge function for public access
    const processSharedBoards = async () => {
      const processedBoards = await Promise.all(sharedBoardsInfo.map(async (info) => {
        // For shared boards, we need to use the edge function to get a properly authorized signed URL
        let previewUrl = null;
        
        if (info.boards.preview_image_url) {
          previewUrl = await getPublicSignedUrl(
            info.boards.id,
            info.boards.preview_image_url, 
            userId
          );
        }
        
        return {
          id: info.boards.id,
          boardName: info.boards.board_name,
          createdAt: info.boards.created_at,
          updatedAt: info.boards.updated_at, 
          elementsCount: info.boards.elements[0]?.count ?? 0,
          connectionsCount: info.boards.connections[0]?.count ?? 0,
          isOwner: false,
          previewImageUrl: previewUrl || info.boards.preview_image_url,
          permissionLevel: info.permission_level
        };
      }));
      
      return processedBoards;
    };

    // 6. Process both board types
    const [formattedUserBoards, formattedSharedBoards] = await Promise.all([
      processOwnedBoards(),
      processSharedBoards()
    ]);

    // 7. Get total counts for pagination
    const { count: totalUserBoards, error: countError } = await supabase
      .from('boards')
      .select('id', { count: 'exact', head: true })
      .eq('user_id', userId);

    const { count: totalSharedBoards, error: sharedCountError } = await supabase
      .from('board_shares')
      .select('board_id', { count: 'exact', head: true })
      .eq('user_id', userId);

    return NextResponse.json({
      userBoards: formattedUserBoards,
      sharedBoards: formattedSharedBoards,
      pagination: {
        totalUserBoards: totalUserBoards || 0,
        totalSharedBoards: totalSharedBoards || 0,
        limit,
        offset
      }
    }, { status: 200 });

  } catch (error: any) {
    return NextResponse.json({ message: 'An unexpected error occurred', error: error.message }, { status: 500 });
  }
} 