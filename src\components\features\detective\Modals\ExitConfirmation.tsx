import React from 'react';
import { X } from 'lucide-react';
import { ExitConfirmationModalProps } from '../../../../types';

/**
 * Modal for confirming exit with unsaved changes
 */
const ExitConfirmation: React.FC<ExitConfirmationModalProps> = ({
  isOpen,
  onClose,
  onSaveAndExit,
  onExitWithoutSaving,
  destination
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50">
      <div className="bg-noir-100 rounded-lg w-full max-w-md p-4 shadow-xl">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-white text-lg font-medium">Save Changes?</h2>
          <button 
            onClick={onClose}
            className="text-white/70 hover:text-white"
            aria-label="Close"
          >
            <X size={20} />
          </button>
        </div>
        
        <p className="text-white/80 mb-6">
          Do you want to save your detective board before leaving?
        </p>
        
        <div className="flex justify-end space-x-3">
          <button
            onClick={onClose}
            className="px-4 py-2 text-white/70 hover:text-white"
          >
            Cancel
          </button>
          <button
            onClick={onExitWithoutSaving}
            className="px-4 py-2 bg-noir-50 text-white rounded hover:bg-noir-100"
          >
            Discard
          </button>
          <button
            onClick={onSaveAndExit}
            className="px-4 py-2 bg-noir-accent text-white rounded hover:bg-opacity-90"
          >
            Save
          </button>
        </div>
      </div>
    </div>
  );
};

export default ExitConfirmation; 