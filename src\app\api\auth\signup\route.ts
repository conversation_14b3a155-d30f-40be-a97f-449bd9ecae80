import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import type { Database } from '@/lib/database.types'; // Ensure this path is correct
import { randomUUID } from 'crypto';
// --- Import createClient --- (No longer needed for admin client)
// import { createClient } from '@supabase/supabase-js';

// Generate a random 6-character alphanumeric code (excluding easily confused chars like 0/O, 1/I/L)
function generateVerificationCode(): string {
    const characters = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789';
    let code = '';
    for (let i = 0; i < 6; i++) {
      code += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    return code;
}

// --- Add Supabase Admin Client Initialization --- (Removing this section)
// Use service role for operations that need to bypass RLS (like inserting profile row)
// const supabaseAdmin = createClient<Database>(
// process.env.NEXT_PUBLIC_SUPABASE_URL!,
// process.env.SUPABASE_SERVICE_ROLE_KEY! // Ensure this is set in your env
// );

export async function POST(request: NextRequest) {
  const { email, password, username } = await request.json();

  // --- Basic Input Validation ---
  if (!email || !password || !username) {
    return NextResponse.json({ message: 'Email, password, and username are required' }, { status: 400 });
  }
  if (password.length < 6) { // Example: Enforce minimum password length
    return NextResponse.json({ message: 'Password must be at least 6 characters long' }, { status: 400 });
  }
  // Add other validation as needed (e.g., username format)

  // --- Use Route Handler Client ONLY for signUp (to handle cookies/auth context) ---
  const cookieStore = cookies();
  // Rename to supabase for consistency and use for all DB operations
  const supabase = createRouteHandlerClient<Database>({ cookies: () => cookieStore });

  try {
    // --- 1. Sign up user with Supabase Auth using Route Handler Client ---
    // Store username in metadata for potential future use, though public.users is primary
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email: email,
      password: password,
      options: {
        data: {
          username: username, // Store username in Supabase Auth metadata
        },
        // emailRedirectTo: `${request.nextUrl.origin}/auth/callback`, // Optional: If using Supabase link verification too
      },
    });

    if (authError) {
        console.error("Supabase Auth Sign Up Error:", authError);
        // Provide specific messages for common errors
        if (authError.message.includes("User already registered")) {
            return NextResponse.json({ message: 'Email already in use' }, { status: 409 }); // 409 Conflict
        }
        return NextResponse.json({ message: authError.message || 'Failed to create user' }, { status: 400 });
    }

    // Check if user object exists (it should if no error, but good practice)
    if (!authData.user) {
        console.error("Supabase Auth Sign Up Error: User data missing despite no error.");
        return NextResponse.json({ message: 'Failed to create user data' }, { status: 500 });
    }

    const userId = authData.user.id;

    // --- 2. Create entry in public.users ---
    // Note: We do NOT store the password here. is_verified defaults to false.
    const { error: profileError } = await supabase
        .from('users')
        .insert({
            id: userId, // Use the ID from Supabase Auth
            email: email,
            username: username,
            is_verified: false, // Explicitly set custom verification status
            updated_at: new Date().toISOString(), // Required by type, ideally DB default
            // created_at should be handled by DB defaults if configured
        });

    if (profileError) {
        console.error("Error creating public user profile:", profileError);
        // Handle potential duplicate username/email if constraints exist on public.users
        if (profileError.code === '23505') { // Unique violation
             return NextResponse.json({ message: 'Username or email already exists in profile table.' }, { status: 409 });
        }
        // Consider cleanup: If profile creation fails, should we delete the auth.users entry?
        // This is complex. For now, log error and return failure.
        return NextResponse.json({ message: 'Failed to create user profile' }, { status: 500 });
    }

    // --- 3. Create entry in public.verification_codes ---
    const verificationCode = generateVerificationCode();
    const expiresAt = new Date(Date.now() + 30 * 60 * 1000); // 30 minutes expiry
    const sentAt = new Date();
    // --- Generate UUID for the id column ---
    const verificationCodeId = randomUUID();

    const { error: codeError } = await supabase
        .from('verification_codes')
        .insert({
            id: verificationCodeId, // Add the generated ID
            user_id: userId,
            code: verificationCode,
            sent_at: sentAt.toISOString(),
            expires_at: expiresAt.toISOString(),
        });

     if (codeError) {
        console.error("Error creating verification code:", codeError);
        // Log error and return failure. Consider cleanup?
        return NextResponse.json({ message: 'Failed to create verification code' }, { status: 500 });
    }

    // --- 4. Trigger email sending ---
    // Get origin for the API call URL
    const origin = request.headers.get('origin') || process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'; // Use header, fallback to env var or default

    try {
        const emailResponse = await fetch(`${origin}/api/send-verification-email`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ email: email, code: verificationCode }) // Pass email and generated code
        });

        if (!emailResponse.ok) {
          // Log the error from the email API, but don't necessarily fail the whole signup
          const errorBody = await emailResponse.text();
          console.error(`Failed to send verification email. Status: ${emailResponse.status}, Body: ${errorBody}`);
          // Proceed to return success, but maybe log this failure more permanently
        } else {
          console.log(`Verification email initiated for ${email}`);
        }
    } catch (emailApiError) {
       console.error('Error calling email sending API:', emailApiError);
       // Log the error, but proceed with the success response
    }
    // --- End email sending ---


    // --- Success Response ---
    // Indicate that the user was created but needs custom verification
    return NextResponse.json({
      message: 'Signup successful. Please check your email for a verification code.',
      userId: userId,
      needsVerification: true, // Custom verification flag
      verificationSentAt: sentAt.toISOString() // Let client know when code was generated
    }, { status: 201 }); // 201 Created

  } catch (error) {
    console.error('Signup Handler Error:', error);
    return NextResponse.json({ message: 'An unexpected error occurred during signup.' }, { status: 500 });
  }
} 