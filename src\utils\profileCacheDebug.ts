import { ProfileCache } from './profileCache';

/**
 * Debug utilities for profile cache - helps identify account switching issues
 * Only use in development environment
 */
export class ProfileCacheDebug {
  private static isEnabled = process.env.NODE_ENV === 'development';

  /**
   * Log current cache state
   */
  static logCacheState(): void {
    if (!this.isEnabled) return;

    console.group('🔍 Profile Cache Debug State');
    
    try {
      const userIds = ProfileCache.getCachedUserIds();
      console.log('📋 Cached User IDs:', userIds);

      userIds.forEach(userId => {
        const profile = ProfileCache.getCachedProfile(userId);
        const completionStatus = ProfileCache.isProfileComplete(userId);
        
        console.group(`👤 User: ${userId}`);
        console.log('Profile:', profile);
        console.log('Completion Status:', completionStatus);
        console.log('Profile ID Match:', profile?.id === userId ? '✅' : '❌');
        console.groupEnd();
      });

      const validation = ProfileCache.validateCacheIntegrity();
      console.log('🔍 Cache Integrity:', validation.isValid ? '✅ Valid' : '❌ Invalid');
      if (!validation.isValid) {
        console.warn('⚠️ Cache Issues:', validation.issues);
      }

    } catch (error) {
      console.error('❌ Error logging cache state:', error);
    }
    
    console.groupEnd();
  }

  /**
   * Monitor profile cache operations
   */
  static enableCacheMonitoring(): void {
    if (!this.isEnabled) return;

    // Override ProfileCache methods to add logging
    const originalSetCachedProfile = ProfileCache.setCachedProfile;
    const originalGetCachedProfile = ProfileCache.getCachedProfile;
    const originalFetchProfile = ProfileCache.fetchProfile;
    const originalClearUserCache = ProfileCache.clearUserCache;
    const originalHandleAccountSwitch = ProfileCache.handleAccountSwitch;

    ProfileCache.setCachedProfile = function(profile, etag) {
      console.log('📝 Cache SET:', { userId: profile.id, username: profile.username, etag });
      return originalSetCachedProfile.call(this, profile, etag);
    };

    ProfileCache.getCachedProfile = function(userId) {
      const result = originalGetCachedProfile.call(this, userId);
      console.log('📖 Cache GET:', { 
        userId, 
        found: !!result, 
        profileId: result?.id,
        match: result?.id === userId ? '✅' : '❌'
      });
      return result;
    };

    ProfileCache.fetchProfile = async function(userId) {
      console.log('🌐 Profile FETCH started:', { userId });
      try {
        const result = await originalFetchProfile.call(this, userId);
        console.log('🌐 Profile FETCH completed:', { 
          userId, 
          success: !!result,
          profileId: result?.id,
          match: result?.id === userId ? '✅' : '❌'
        });
        return result;
      } catch (error) {
        console.error('🌐 Profile FETCH failed:', { userId, error: error.message });
        throw error;
      }
    };

    ProfileCache.clearUserCache = function(userId) {
      console.log('🗑️ Cache CLEAR:', { userId });
      return originalClearUserCache.call(this, userId);
    };

    ProfileCache.handleAccountSwitch = function(newUserId, previousUserId) {
      console.log('🔄 Account SWITCH:', { from: previousUserId, to: newUserId });
      return originalHandleAccountSwitch.call(this, newUserId, previousUserId);
    };

    console.log('🔍 Profile cache monitoring enabled');
  }

  /**
   * Check for potential account switching issues
   */
  static checkForAccountSwitchIssues(currentUserId: string): {
    hasIssues: boolean;
    issues: string[];
    recommendations: string[];
  } {
    const issues: string[] = [];
    const recommendations: string[] = [];

    try {
      // Check if there are cached profiles for other users
      const cachedUserIds = ProfileCache.getCachedUserIds();
      const otherUsers = cachedUserIds.filter(id => id !== currentUserId);

      if (otherUsers.length > 0) {
        issues.push(`Found cached data for ${otherUsers.length} other user(s): ${otherUsers.join(', ')}`);
        recommendations.push('Clear cache when switching accounts');
      }

      // Check current user's cached profile
      const currentProfile = ProfileCache.getCachedProfile(currentUserId);
      if (currentProfile && currentProfile.id !== currentUserId) {
        issues.push(`Current user's cached profile has wrong ID: expected ${currentUserId}, got ${currentProfile.id}`);
        recommendations.push('Clear corrupted cache immediately');
      }

      // Validate overall cache integrity
      const validation = ProfileCache.validateCacheIntegrity();
      if (!validation.isValid) {
        issues.push(...validation.issues);
        recommendations.push('Run cache integrity validation and cleanup');
      }

    } catch (error) {
      issues.push(`Error checking for account switch issues: ${error}`);
    }

    return {
      hasIssues: issues.length > 0,
      issues,
      recommendations
    };
  }

  /**
   * Simulate account switching for testing
   */
  static simulateAccountSwitch(fromUserId: string, toUserId: string): void {
    if (!this.isEnabled) return;

    console.group('🧪 Simulating Account Switch');
    console.log(`From: ${fromUserId} → To: ${toUserId}`);

    // Log state before
    console.log('Before switch:');
    this.logCacheState();

    // Simulate the switch
    ProfileCache.handleAccountSwitch(toUserId, fromUserId);

    // Log state after
    console.log('After switch:');
    this.logCacheState();

    console.groupEnd();
  }

  /**
   * Generate cache report for debugging
   */
  static generateCacheReport(): string {
    const report = [];
    report.push('=== Profile Cache Debug Report ===');
    report.push(`Timestamp: ${new Date().toISOString()}`);
    report.push('');

    try {
      const userIds = ProfileCache.getCachedUserIds();
      report.push(`Cached Users: ${userIds.length}`);
      
      userIds.forEach(userId => {
        const profile = ProfileCache.getCachedProfile(userId);
        const completionStatus = ProfileCache.isProfileComplete(userId);
        
        report.push(`\nUser ID: ${userId}`);
        report.push(`  Profile ID: ${profile?.id || 'N/A'}`);
        report.push(`  Username: ${profile?.username || 'N/A'}`);
        report.push(`  Verified: ${profile?.is_verified || false}`);
        report.push(`  Completion Status: ${completionStatus}`);
        report.push(`  ID Match: ${profile?.id === userId ? 'YES' : 'NO'}`);
      });

      const validation = ProfileCache.validateCacheIntegrity();
      report.push(`\nCache Integrity: ${validation.isValid ? 'VALID' : 'INVALID'}`);
      if (!validation.isValid) {
        report.push('Issues:');
        validation.issues.forEach(issue => report.push(`  - ${issue}`));
      }

    } catch (error) {
      report.push(`\nError generating report: ${error}`);
    }

    report.push('\n=== End Report ===');
    return report.join('\n');
  }

  /**
   * Add cache debug info to window for browser console access
   */
  static exposeToWindow(): void {
    if (!this.isEnabled || typeof window === 'undefined') return;

    (window as any).profileCacheDebug = {
      logState: () => this.logCacheState(),
      checkIssues: (userId: string) => this.checkForAccountSwitchIssues(userId),
      generateReport: () => console.log(this.generateCacheReport()),
      clearAll: () => ProfileCache.clearAllCache(),
      validate: () => ProfileCache.validateCacheIntegrity(),
      getCachedUsers: () => ProfileCache.getCachedUserIds(),
    };

    console.log('🔍 Profile cache debug tools available at window.profileCacheDebug');
  }
}

// Auto-enable in development
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  ProfileCacheDebug.exposeToWindow();
}
