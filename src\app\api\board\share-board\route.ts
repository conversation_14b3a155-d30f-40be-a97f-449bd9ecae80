import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { NextResponse } from 'next/server';
import type { Database } from '@/lib/database.types';

export const dynamic = 'force-dynamic';

// DELETE: Remove a board share
export async function DELETE(request: Request) {
  const supabase = createRouteHandlerClient<Database>({ cookies });

  try {
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    if (sessionError || !session) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    const { boardId, usernameToRemove } = await request.json();
    if (!boardId || !usernameToRemove) {
      return NextResponse.json({ error: 'Missing required fields: boardId, usernameToRemove' }, { status: 400 });
    }

    // Perform deletion based on boardId and username
    const { error } = await supabase
      .from('board_shares')
      .delete()
      .eq('board_id', boardId)
      .eq('username', usernameToRemove);

    if (error) {
      console.error('Error deleting board share:', error);
      if (error.code === '42501') {
        return NextResponse.json({ error: 'Permission denied. Only board owners can remove shares.' }, { status: 403 });
      }
      return NextResponse.json({ error: 'Failed to delete board share', details: error.message }, { status: 500 });
    }

    return NextResponse.json({ message: 'Board share removed successfully' }, { status: 200 });

  } catch (e: any) {
    if (e instanceof SyntaxError) {
      return NextResponse.json({ error: 'Invalid request body format' }, { status: 400 });
    }
    console.error('Unexpected error deleting board share:', e);
    return NextResponse.json({ error: 'An unexpected error occurred', details: e.message }, { status: 500 });
  }
}

// PUT: Update a board share by boardId and username
export async function PUT(request: Request) {
  const supabase = createRouteHandlerClient<Database>({ cookies });
  try {
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    if (sessionError || !session) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }
    const { boardId, usernameToUpdate, permissionLevel } = await request.json();
    if (!boardId || !usernameToUpdate || !permissionLevel) {
      return NextResponse.json({ error: 'Missing required fields: boardId, usernameToUpdate, permissionLevel' }, { status: 400 });
    }
    if (permissionLevel !== 'view' && permissionLevel !== 'edit') {
      return NextResponse.json({ error: 'Invalid permission level. Must be "view" or "edit".' }, { status: 400 });
    }
    // Perform update based on boardId and username
    const { data, error } = await supabase
      .from('board_shares')
      .update({ permission_level: permissionLevel, updated_at: new Date().toISOString() })
      .eq('board_id', boardId)
      .eq('username', usernameToUpdate)
      .select(
        `id, user_id, username, permission_level, created_at, updated_at, board_id`
      )
      .single();
    if (error) {
      console.error('Error updating board share:', error);
      if (error.code === '42501') {
        return NextResponse.json({ error: 'Permission denied. Only board owners can update shares.' }, { status: 403 });
      }
      return NextResponse.json({ error: 'Failed to update board share', details: error.message }, { status: 500 });
    }
    return NextResponse.json(data, { status: 200 });
  } catch (e: any) {
    if (e instanceof SyntaxError) {
      return NextResponse.json({ error: 'Invalid request body format' }, { status: 400 });
    }
    console.error('Unexpected error updating board share:', e);
    return NextResponse.json({ error: 'An unexpected error occurred', details: e.message }, { status: 500 });
  }
}

// GET: Retrieve share information for a specific board
export async function GET(request: Request) {
  const supabase = createRouteHandlerClient<Database>({ cookies });
  const { searchParams } = new URL(request.url);
  const boardId = searchParams.get('boardId');

  if (!boardId) {
    return NextResponse.json({ error: 'Board ID is required' }, { status: 400 });
  }

  try {
    // Fetch current user session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    // Fetch share entries for the board
    // RLS policy ensures user can only see shares they are involved in or if they own the board
    const { data, error } = await supabase
      .from('board_shares')
      .select(`
        id,
        user_id,
        username,
        permission_level,
        created_at,
        board_id
      `)
      .eq('board_id', boardId);

    if (error) {
      console.error('Error fetching board shares:', error);
      // Check for specific errors like RLS violation if needed, though RLS should just return empty data if no access
      return NextResponse.json({ error: 'Failed to fetch board shares', details: error.message }, { status: 500 });
    }

    return NextResponse.json(data || []); // Return empty array if no shares found or accessible

  } catch (e: any) {
    console.error('Unexpected error fetching board shares:', e);
    return NextResponse.json({ error: 'An unexpected error occurred', details: e.message }, { status: 500 });
  }
}

// POST: Create a new board share (invite user by username)
export async function POST(request: Request) {
  const supabase = createRouteHandlerClient<Database>({ cookies });

  try {
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }
    
    // Request expects: boardId and usernameToInvite
    const { boardId, usernameToInvite } = await request.json();
    const permissionLevel = 'view'; // default permission

    // Input validation
    if (!boardId || !usernameToInvite) {
      return NextResponse.json({ error: 'Missing required fields: boardId, usernameToInvite' }, { status: 400 });
    }

    // Lookup user ID from public.users by username
    const { data: userRecord, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('username', usernameToInvite)
      .maybeSingle();
    if (userError) {
      console.error('Error querying user:', userError);
      return NextResponse.json({ error: 'Database error looking up user', details: userError.message }, { status: 500 });
    }
    if (!userRecord) {
      return NextResponse.json({ error: `User '${usernameToInvite}' not found` }, { status: 404 });
    }
    const userIdToInvite = userRecord.id;

    // Check if the share already exists for this user and board
    const { data: existingShare, error: existingError } = await supabase
      .from('board_shares')
      .select('id')
      .eq('board_id', boardId)
      .eq('user_id', userIdToInvite)
      .maybeSingle();

    if (existingError && existingError.code !== 'PGRST116') {
      console.error('Error checking existing share:', existingError);
      return NextResponse.json({ error: 'Database error checking existing share', details: existingError.message }, { status: 500 });
    }

    if (existingShare) {
        return NextResponse.json({ error: 'User already has access to this board. Use PUT to update permissions.' }, { status: 409 });
    }

    // Insert new share row
    const { data, error } = await supabase
      .from('board_shares')
      .insert({
        board_id: boardId,
        user_id: userIdToInvite,
        username: usernameToInvite,        
        permission_level: permissionLevel,
      })
      .select(`
        id,
        user_id,
        username,
        permission_level,
        created_at,
        board_id
      `)
      .single();

    if (error) {
      console.error('Error creating board share:', error);
      if (error.code === '42501') {
        return NextResponse.json({ error: 'Permission denied. Only board owners can share.' }, { status: 403 });
      }
      return NextResponse.json({ error: 'Failed to create board share', details: error.message }, { status: 500 });
    }

    return NextResponse.json(data, { status: 201 });

  } catch (e: any) {
    if (e instanceof SyntaxError) {
      return NextResponse.json({ error: 'Invalid request body format' }, { status: 400 });
    }
    console.error('Unexpected error creating board share:', e);
    return NextResponse.json({ error: 'An unexpected error occurred', details: e.message }, { status: 500 });
  }
}

// Placeholder for POST, PUT, DELETE which will likely need separate route files
// or more complex handling within this file depending on the desired URL structure.
