'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { PlusSquare, FolderOpen, LogIn, UserPlus, LogOut, Loader2, Globe } from 'lucide-react';
import { useAuth } from '@/context/AuthContext';
import { Button } from '@/components/ui/button';
import { SignInModal } from '../auth/SignInModal';
import { SignUpModal } from '../auth/SignUpModal';
import { VerificationModal } from '../auth/VerificationModal';
import { UsernameSetupModal } from '../auth/UsernameSetupModal';
import { Modal } from '@/components/ui/modal';
import ReadableFontWrapper from '@/components/layout/ReadableFontWrapper';

const Dashboard: React.FC = () => {
  const { isAuthenticated, user, signOut, isUsernameSetupRequired } = useAuth();
  const router = useRouter();
  const [isSignInOpen, setIsSignInOpen] = useState(false);
  const [isSignUpOpen, setIsSignUpOpen] = useState(false);
  const [isVerificationOpen, setIsVerificationOpen] = useState(false);
  const [verificationEmail, setVerificationEmail] = useState('');
  const [verificationUserId, setVerificationUserId] = useState<string | undefined>(undefined);
  const [verificationSentAt, setVerificationSentAt] = useState<string | undefined>();
  const [isSignOutConfirmOpen, setIsSignOutConfirmOpen] = useState(false);
  const [isSigningOut, setIsSigningOut] = useState(false);
  const [isCreatingNewBoard, setIsCreatingNewBoard] = useState(false);
  const [isLoadingRecentBoards, setIsLoadingRecentBoards] = useState(false);
  const [isLoadingPublicBoards, setIsLoadingPublicBoards] = useState(false);

  useEffect(() => {
    if (isAuthenticated && !isUsernameSetupRequired) {
      const destination = localStorage.getItem('intendedDestination');
      if (destination) {
        if (destination === '/recent') {
          setIsLoadingRecentBoards(true);
        } else if (destination === '/board/new') {
          setIsCreatingNewBoard(true);
        }
        router.push(destination);
        localStorage.removeItem('intendedDestination');
      }
    }
  }, [isAuthenticated, user?.username, router, isUsernameSetupRequired]);

  const handleGenericSignInOpen = () => {
    localStorage.removeItem('intendedDestination');
    setIsSignInOpen(true);
  };

  const handleSignInModalClose = () => {
    localStorage.removeItem('intendedDestination');
    setIsSignInOpen(false);
  };

  const handleCreateNewBoard = (e: React.MouseEvent) => {
    e.preventDefault();
    if (!isAuthenticated) {
      localStorage.setItem('intendedDestination', '/board/new');
      setIsSignInOpen(true);
    } else {
      setIsCreatingNewBoard(true);
      router.push('/board/new');
    }
  };

  const handleRecentBoardsClick = (e: React.MouseEvent) => {
    if (!isAuthenticated) {
      e.preventDefault();
      localStorage.setItem('intendedDestination', '/recent');
      setIsSignInOpen(true);
    } else {
      e.preventDefault();
      setIsLoadingRecentBoards(true);
      router.push('/recent');
    }
  };

  const handlePublicBoardsClick = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsLoadingPublicBoards(true);
    router.push('/public-boards');
  };

  const handleVerificationNeeded = (email: string, userId?: string) => {
    setVerificationEmail(email);
    setVerificationUserId(userId);
    setVerificationSentAt(new Date().toISOString());
    setIsVerificationOpen(true);
  };

  const handleVerificationSuccess = () => {
    // Close all modals
    setIsSignInOpen(false);
    setIsSignUpOpen(false);
  };

  const handleSignInSuccess = () => {
    setIsSignInOpen(false);
  };

  const handleSignOut = async () => {
    setIsSigningOut(true);
    try {
      await signOut();
    } catch (error) {
        console.error("Dashboard: Error during handleSignOut:", error);
    } finally {
        setIsSignOutConfirmOpen(false);
        setIsSigningOut(false);
    }
  };

  return (
    <>
      <ReadableFontWrapper>
        <div className="flex flex-col items-center justify-center w-full min-h-screen px-4 py-12 text-white">
          <div className="absolute top-4 right-4 flex space-x-2">
            {isAuthenticated ? (
              <>
                <span className="text-gray-400 mr-2">
                  Hello, {user?.username || user?.email || 'User'}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsSignOutConfirmOpen(true)}
                >
                  <LogOut className="mr-2 h-4 w-4" />
                  Sign Out
                </Button>
              </>
            ) : (
              <>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={handleGenericSignInOpen}
                >
                  <LogIn className="mr-2 h-4 w-4" />
                  Sign In
                </Button>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => setIsSignUpOpen(true)}
                >
                  <UserPlus className="mr-2 h-4 w-4" />
                  Sign Up
                </Button>
              </>
            )}
          </div>

          <div className="max-w-5xl w-full animate-fade-in">
            <div className="mb-16 text-center">
              <h1 className="text-5xl md:text-7xl font-bold mb-4 tracking-tight detective-text animate-fade-up">
                <span className="text-noir-accent">DETECTIVE</span> BOARD
              </h1>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
              <div 
                onClick={handleCreateNewBoard}
                className="group relative flex flex-col items-center p-8 rounded-lg neo-blur transition-all duration-300 hover:-translate-y-1 hover:bg-white/10 animate-fade-up cursor-pointer"
                style={{ animationDelay: '0.2s' }}
              >
                <div className="rounded-full bg-noir-50 p-5 mb-6 group-hover:bg-noir-accent transition-colors duration-300">
                  {isCreatingNewBoard ? (
                    <Loader2 size={48} className="text-white animate-spin" />
                  ) : (
                    <PlusSquare size={48} className="text-white" />
                  )}
                </div>
                <h2 className="text-2xl font-bold mb-3">Create New Board</h2>
                <p className="text-gray-400 text-center">
                  Start from scratch with a blank canvas to map out your investigation.
                </p>
              </div>

              <div 
                onClick={handleRecentBoardsClick}
                className="group relative flex flex-col items-center p-8 rounded-lg neo-blur transition-all duration-300 hover:-translate-y-1 hover:bg-white/10 animate-fade-up cursor-pointer"
                style={{ animationDelay: '0.3s' }}
              >
                <div className="rounded-full bg-noir-50 p-5 mb-6 group-hover:bg-noir-teal transition-colors duration-300">
                  {isLoadingRecentBoards ? (
                    <Loader2 size={48} className="text-white animate-spin" />
                  ) : (
                    <FolderOpen size={48} className="text-white" />
                  )}
                </div>
                <h2 className="text-2xl font-bold mb-3">Recent Boards</h2>
                <p className="text-gray-400 text-center">
                  Continue your investigation where you left off.
                </p>
              </div>

              <div 
                onClick={handlePublicBoardsClick}
                className="group relative flex flex-col items-center p-8 rounded-lg neo-blur transition-all duration-300 hover:-translate-y-1 hover:bg-white/10 animate-fade-up cursor-pointer"
                style={{ animationDelay: '0.4s' }}
              >
                <div className="rounded-full bg-noir-50 p-5 mb-6 group-hover:bg-purple-600 transition-colors duration-300">
                  {isLoadingPublicBoards ? (
                    <Loader2 size={48} className="text-white animate-spin" />
                  ) : (
                    <Globe size={48} className="text-white" />
                  )}
                </div>
                <h2 className="text-2xl font-bold mb-3">Public Boards</h2>
                <p className="text-gray-400 text-center">
                  Explore popular detective boards shared by the community.
                </p>
              </div>
            </div>
          </div>
        </div>
      </ReadableFontWrapper>

      <SignInModal
        isOpen={isSignInOpen}
        onClose={handleSignInModalClose}
        onVerificationNeeded={handleVerificationNeeded}
        onSuccess={handleSignInSuccess}
        onSignUpClick={() => {
          handleSignInModalClose();
          setIsSignUpOpen(true);
        }}
      />

      <SignUpModal
        isOpen={isSignUpOpen}
        onClose={() => setIsSignUpOpen(false)}
        onSuccess={handleSignInSuccess}
        onSignInClick={() => {
          setIsSignUpOpen(false);
          handleGenericSignInOpen();
        }}
        onVerificationNeeded={handleVerificationNeeded}
      />

      {isVerificationOpen && (
        <VerificationModal
          isOpen={isVerificationOpen}
          onClose={() => setIsVerificationOpen(false)}
          email={verificationEmail}
          userId={verificationUserId}
          verificationSentAt={verificationSentAt}
          onSuccess={handleVerificationSuccess}
        />
      )}

      {/* Username Setup Modal */}
      <UsernameSetupModal />

      {/* Sign Out Confirmation Modal */}
      <Modal 
        isOpen={isSignOutConfirmOpen} 
        onClose={() => setIsSignOutConfirmOpen(false)}
        title="Sign Out"
      >
        <div className="space-y-4">
          <p className="text-center text-gray-400">
            Are you sure you want to sign out?
          </p>
          
          <div className="flex space-x-3 justify-center">
            <Button
              variant="outline"
              onClick={() => setIsSignOutConfirmOpen(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSignOut}
              className="bg-red-600 hover:bg-red-700 flex items-center justify-center"
              disabled={isSigningOut}
            >
              {isSigningOut ? (
                <Loader2 className="h-5 w-5 animate-spin" />
              ) : (
                'Sign Out'
              )}
            </Button>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default Dashboard; 