'use client';

import React, { useState, useEffect } from 'react';
import { Loader2 } from 'lucide-react';
import { useAuth } from '@/context/AuthContext';
import { Button } from '@/components/ui/button';
import { Modal } from '@/components/ui/modal';

interface UsernameSetupModalProps {
  // The modal's visibility will be controlled by isUsernameSetupRequired from AuthContext
  // onClose will typically be handled by setting isUsernameSetupRequired to false, 
  // or by successful profile completion which reloads the app.
}

interface FormErrors {
  username?: string;
  general?: string;
}

export function UsernameSetupModal(props: UsernameSetupModalProps) {
  const {
    isUsernameSetupRequired,
    pendingUserForSetup,
    completeUserProfile,
    checkUsernameExists,
    signOut, // Added for a way to cancel/sign out
    user,
    profile,
  } = useAuth();

  const [username, setUsername] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<FormErrors>({});
  const [initialRender, setInitialRender] = useState(true);
  const [showModal, setShowModal] = useState(false);

  useEffect(() => {
    // Use a delayed mounting strategy to prevent modal flash
    let isMounted = true;
    
    // Only show after a delay and if truly required
    if (isUsernameSetupRequired && pendingUserForSetup && !initialRender) {
      // Double check - don't show if we have a profile with username
      if (profile?.username || user?.username) {
        console.log("[UsernameSetupModal] Not showing modal because profile already exists");
        // Auto-close by completing with existing username
        if (profile?.username) {
          try {
            completeUserProfile(profile.username);
          } catch (error) {
            console.error("[UsernameSetupModal] Error completing with existing profile:", error);
          }
        }
        return;
      }
      
      // Add delay before showing to allow other state to settle
      const timer = setTimeout(() => {
        if (isMounted) {
          setShowModal(true);
          setUsername('');
          setErrors({});
          setIsLoading(false);
        }
      }, 300);
      
      return () => {
        isMounted = false;
        clearTimeout(timer);
      };
    } else if (!isUsernameSetupRequired) {
      setShowModal(false);
    }
    
    // Mark initial render as complete after a short delay
    if (initialRender) {
      const timer = setTimeout(() => {
        if (isMounted) {
          setInitialRender(false);
        }
      }, 500);
      
      return () => {
        isMounted = false;
        clearTimeout(timer);
      };
    }
  }, [isUsernameSetupRequired, pendingUserForSetup, initialRender, user, profile, completeUserProfile]);

  // Super early check - if we have a user with username, close modal by completing profile
  useEffect(() => {
    if ((user?.username || profile?.username) && isUsernameSetupRequired) {
      console.log("[UsernameSetupModal] Auto-completing profile due to existing username");
      try {
        completeUserProfile(user?.username || profile?.username || "");
      } catch (error) {
        console.error("[UsernameSetupModal] Error auto-completing:", error);
      }
    }
  }, [user, profile, isUsernameSetupRequired, completeUserProfile]);
  
  // Prevent the modal from rendering on initial page load or if profile exists
  if (initialRender || !showModal || !isUsernameSetupRequired || !pendingUserForSetup) {
    return null;
  }
  
  // Check one more time that we truly need this modal
  if (user?.username || profile?.username) {
    return null;
  }

  const validateUsername = async (currentUsername: string): Promise<string | undefined> => {
    if (!currentUsername) {
      return 'Username is required';
    }
    if (currentUsername.length < 3) {
      return 'Username must be at least 3 characters';
    }
    if (!/^[a-zA-Z0-9_-]+$/.test(currentUsername)) {
      return 'Username can only contain letters, numbers, underscores, and hyphens';
    }
    try {
      const exists = await checkUsernameExists(currentUsername);
      if (exists) {
        return 'Username already taken. Please choose another.';
      }
    } catch (apiError: any) {
      console.error("Error checking username existence:", apiError);
      return 'Could not verify username. Please try again.'; // Or a more specific error
    }
    return undefined;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setErrors({});

    const usernameError = await validateUsername(username);
    if (usernameError) {
      setErrors({ username: usernameError });
      setIsLoading(false);
      return;
    }

    try {
      await completeUserProfile(username);
      // On success, AuthContext will reload the window, so no need to manually close modal here.
      // If reload wasn't happening, you'd call a method here to set isUsernameSetupRequired to false.
    } catch (error: any) {
      console.error('Error completing profile:', error);
      if (error.message.toLowerCase().includes('username')) {
        setErrors({ username: error.message });
      } else {
        setErrors({ general: error.message || 'Failed to set up profile. Please try again.' });
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancelAndSignOut = async () => {
    // This provides a way for the user to bail out if they don't want to set a username right away.
    // Depending on UX, you might just allow them to close the modal without signing out,
    // but then they'd be in a state where they have a session but no profile.
    // Signing out ensures a cleaner state if they abandon setup.
    setIsLoading(true);
    try {
        await signOut();
        // After sign out, onAuthStateChange should clear isUsernameSetupRequired.
    } catch(error) {
        console.error("Error during sign out from username setup:", error);
        setErrors({ general: "Failed to sign out. Please try again or refresh the page." });
    } finally {
        setIsLoading(false);
    }
  };

  const inputClassName = (error?: string) =>
    error
      ? "mt-1 block w-full rounded-md border px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-1 border-red-500 bg-red-900/10 focus:border-red-500 focus:ring-red-500"
      : "mt-1 block w-full rounded-md border px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-1 border-white/10 bg-black/20 focus:border-noir-accent focus:ring-noir-accent";

  return (
    <Modal 
        isOpen={showModal} 
        // Allow closing if the user already has a complete profile
        onClose={() => {
          // Check if the user is already authenticated with a complete profile
          if (user && user.username) {
            console.log("[UsernameSetupModal] User already has complete profile, allowing close");
            // This requires completeUserProfile to be called with the existing username
            // to ensure state is properly reset
            try {
              completeUserProfile(user.username);
            } catch (error) {
              console.error("[UsernameSetupModal] Error resetting after redundant modal:", error);
            }
          } else {
            console.log("[UsernameSetupModal] Modal close attempted but user needs setup");
            // Do nothing on default close attempts for users who still need setup
          }
        }}
        title="Complete Your Profile"
        showCloseButton={!!user?.username} // Only show close button if user has username
    >
      <p className="text-sm text-gray-400 text-center mb-2">
        Welcome! Please choose a username to continue.
      </p>
      {pendingUserForSetup.email && (
        <p className="text-xs text-gray-500 text-center mb-6">
          Signed in as: {pendingUserForSetup.email}
        </p>
      )}

      <form className="space-y-6" onSubmit={handleSubmit}>
        {errors.general && (
          <div className="text-red-500 text-sm text-center bg-red-500/10 p-2 rounded-md border border-red-500/20">
            {errors.general}
          </div>
        )}

        <div>
          <label htmlFor="username-setup" className="block text-sm font-medium text-gray-300">
            Username
          </label>
          <input
            id="username-setup"
            type="text"
            autoFocus
            required
            className={inputClassName(errors.username)}
            placeholder="Choose your username"
            value={username}
            onChange={(e) => {
              setUsername(e.target.value);
              setErrors(prev => ({ ...prev, username: undefined, general: undefined }));
            }}
          />
          {errors.username && (
            <p className="mt-1 text-sm text-red-500">{errors.username}</p>
          )}
        </div>

        <div>
          <Button
            type="submit"
            disabled={isLoading}
            className="w-full bg-noir-accent hover:bg-noir-accent/90 relative"
          >
            {isLoading ? (
              <Loader2 className="h-5 w-5 animate-spin" />
            ) : (
              'Save and Continue'
            )}
          </Button>
        </div>
      </form>
        <div className="mt-4">
            <Button
                type="button"
                variant="ghost"
                disabled={isLoading}
                onClick={handleCancelAndSignOut}
                className="w-full text-gray-400 hover:text-white hover:bg-gray-700/50"
            >
                Cancel and Sign Out
            </Button>
        </div>
    </Modal>
  );
} 