'use client';

import { useEffect, useRef, RefObject } from 'react';

type Handler = (event: MouseEvent | TouchEvent) => void;

/**
 * Hook that detects clicks outside of a specified element
 */
export function useOutsideClick<T extends HTMLElement = HTMLElement>(
  handler: Handler,
  active = true
): RefObject<T> {
  const ref = useRef<T>(null);

  useEffect(() => {
    if (!active) return;

    const handleEvent = (event: MouseEvent | TouchEvent) => {
      const el = ref.current;

      // Do nothing if clicking ref's element or descendent elements
      if (!el || el.contains(event.target as Node)) {
        return;
      }

      handler(event);
    };

    document.addEventListener('mousedown', handleEvent);
    document.addEventListener('touchstart', handleEvent);

    return () => {
      document.removeEventListener('mousedown', handleEvent);
      document.removeEventListener('touchstart', handleEvent);
    };
  }, [handler, active]);

  return ref;
}

/**
 * Hook that manages multiple refs for click outside detection
 */
export function useMultipleOutsideClick<T extends HTMLElement = HTMLElement>(
  handler: Handler,
  active = true
): [RefObject<T>, (el: T | null) => void] {
  const refs = useRef<Set<T | null>>(new Set());
  const mainRef = useRef<T>(null);

  const addRef = (el: T | null) => {
    if (el) {
      refs.current.add(el);
    }
  };

  useEffect(() => {
    if (!active) return;

    const handleEvent = (event: MouseEvent | TouchEvent) => {
      // Check if clicked element is inside any of the tracked elements
      const isInside = Array.from(refs.current).some(
        (el) => el && el.contains(event.target as Node)
      );

      if (!isInside) {
        handler(event);
      }
    };

    document.addEventListener('mousedown', handleEvent);
    document.addEventListener('touchstart', handleEvent);

    return () => {
      document.removeEventListener('mousedown', handleEvent);
      document.removeEventListener('touchstart', handleEvent);
    };
  }, [handler, active]);

  // Add the main ref to the set when it's assigned
  useEffect(() => {
    if (mainRef.current) {
      refs.current.add(mainRef.current);
    }
    return () => {
      if (mainRef.current) {
        refs.current.delete(mainRef.current);
      }
    };
  }, [mainRef.current]);

  return [mainRef, addRef];
} 