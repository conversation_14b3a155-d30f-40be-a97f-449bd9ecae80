import React, { useState, useEffect } from 'react';
import { X, Loader2, Refresh<PERSON><PERSON>, Users, UserX } from 'lucide-react';
import { motion } from '../../../utils/MotionWrapper';
import { toast } from 'sonner';

interface BoardShare {
  id: string;
  user_id: string;
  username: string;
  permission_level: 'view' | 'edit';
  created_at: string;
  board_id: string;
}

interface ShareModalProps {
  isOpen: boolean;
  onClose: () => void;
  isPublic: boolean;
  onPublicStatusChange: (isPublic: boolean) => Promise<void>;
  boardId: string | null;
}

const ShareModal: React.FC<ShareModalProps> = ({ isOpen, onClose, isPublic, onPublicStatusChange, boardId }) => {
  const [shares, setShares] = useState<BoardShare[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [inviteEmail, setInviteEmail] = useState('');
  const [isInviting, setIsInviting] = useState(false);
  const [updatingIds, setUpdatingIds] = useState<Record<string, boolean>>({});
  const [deletingIds, setDeletingIds] = useState<Record<string, boolean>>({});

  // Fetch shares function
  const fetchShares = async () => {
    if (!boardId) return;
    setIsLoading(true);
    setError(null);
    try {
      const res = await fetch(`/api/board/share-board?boardId=${boardId}`);
      if (!res.ok) {
        const errBody = await res.json();
        throw new Error(errBody.error || `Error ${res.status}`);
      }
      const data: BoardShare[] = await res.json();
      setShares(data);
    } catch (err: any) {
      console.error('Fetch shares error:', err);
      setError(err.message || 'Unable to load shares');
      setShares([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Effect to fetch and reset shares when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      fetchShares();
    } else {
      setShares([]);
      setError(null);
      setIsLoading(false);
      setInviteEmail('');
      setIsInviting(false);
    }
  }, [isOpen, boardId]);

  const handleClose = (e: React.MouseEvent) => {
    e.stopPropagation();
    onClose();
  };

  const handleTogglePublic = async () => {
    if (!boardId) {
      toast.error('Board ID is required to change public status.');
      return;
    }
    try {
      await onPublicStatusChange(!isPublic);
    } catch (err) {
      console.error('Toggle public status failed:', err);
    }
  };

  // Invite user by username
  const handleInviteUser = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!boardId) {
      toast.error('Board ID is required.');
      return;
    }
    setIsInviting(true);
    try {
      const response = await fetch('/api/board/share-board', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ boardId, usernameToInvite: inviteEmail }),
      });
      const result = await response.json();
      if (!response.ok) {
        toast.error(result.error || 'Failed to invite user');
      } else {
        toast.success(`Invited ${inviteEmail}`);
        setInviteEmail('');
        fetchShares();
      }
    } catch (err) {
      console.error('Invite user error:', err);
      toast.error('An unexpected error occurred');
    } finally {
      setIsInviting(false);
    }
  };

  const handleRemoveShare = async (shareId: string, username: string) => {
    if (!boardId) {
      toast.error('Board ID is required.');
      return;
    }
    setDeletingIds(prev => ({ ...prev, [shareId]: true }));
    try {
      const response = await fetch('/api/board/share-board', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ boardId, usernameToRemove: username }),
      });
      const result = await response.json();
      if (!response.ok) {
        toast.error(result.error || 'Failed to remove access');
      } else {
        toast.success(`Removed access for ${username}`);
        await fetchShares();
      }
    } catch (err) {
      console.error('Remove share error:', err);
      toast.error('An unexpected error occurred');
    } finally {
      setDeletingIds(prev => ({ ...prev, [shareId]: false }));
    }
  };

  const handleUpdatePermission = async (shareId: string, newPerm: 'view' | 'edit', username: string) => {
    setUpdatingIds(prev => ({ ...prev, [shareId]: true }));
    try {
      if (!boardId) throw new Error('Board ID is required');
      const response = await fetch('/api/board/share-board', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ boardId, usernameToUpdate: username, permissionLevel: newPerm }),
      });
      const result = await response.json();
      if (!response.ok) {
        toast.error(result.error || 'Failed to update permission');
      } else {
        toast.success(`Permission updated to ${newPerm}`);
        await fetchShares();
      }
    } catch (err) {
      console.error('Update permission error:', err);
      toast.error('An unexpected error occurred');
    } finally {
      setUpdatingIds(prev => ({ ...prev, [shareId]: false }));
    }
  };

  const inputClass = 'w-full p-2 bg-noir-200 border border-noir-50 rounded text-white placeholder-gray-400 focus:outline-none focus:border-noir-paper disabled:opacity-50';
  const primaryBtn = 'px-4 py-2 bg-noir-accent text-white rounded hover:bg-red-700 transition-colors disabled:opacity-50 flex gap-2 items-center justify-center';
  const secondaryBtn = 'px-4 py-2 bg-noir-200 text-white rounded hover:bg-noir-50 transition-colors disabled:opacity-50';

  if (!isOpen) return null;

  return (
    <motion.div
      className="fixed inset-0 z-50 flex items-center justify-center bg-black/70"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
    >
      <motion.div
        className="bg-noir-100 border border-noir-50 rounded-lg shadow-xl w-[500px] max-w-[95vw] relative"
        initial={{ scale: 0.9, y: 20 }}
        animate={{ scale: 1, y: 0 }}
        exit={{ scale: 0.9, y: 20 }}
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex justify-between items-center p-4 border-b border-noir-50">
          <h2 className="text-lg font-medium text-white">Share Board & Access</h2>
          <button onClick={handleClose} className="text-gray-400 hover:text-white">
            <X size={20} />
          </button>
        </div>

        <div className="p-6 space-y-6">
          <div className="bg-noir-200 p-4 rounded-md border border-noir-50">
            <p className="text-sm text-gray-300 mb-3">
              Board Status: <span className={`font-medium ${isPublic ? 'text-green-400' : 'text-red-400'}`}>{isPublic ? 'Public' : 'Private'}</span>
            </p>
            <button onClick={handleTogglePublic} className={`w-full px-4 py-2 rounded text-sm font-medium ${isPublic ? 'bg-red-600 hover:bg-red-700' : 'bg-green-600 hover:bg-green-700'} text-white`}>
              {isPublic ? 'Make Private' : 'Make Public'}
            </button>
            {isPublic && boardId && (
              <a
                href={`/public-board/${boardId}`}
                onClick={(e) => {
                  e.preventDefault();
                  // Construct the full URL for redirection
                  const publicBoardUrl = `${window.location.origin}/public-board/${boardId}`;
                  window.location.href = publicBoardUrl;
                }}
                className="mt-3 block text-center text-sm text-noir-accent hover:underline"
              >
                Go to public board
              </a>
            )}
          </div>

          <form onSubmit={handleInviteUser} className="flex space-x-2 items-center">
            <input
              type="text"
              placeholder="Enter username"
              value={inviteEmail}
              onChange={e => setInviteEmail(e.target.value)}
              className={`${inputClass} flex-grow`}
              disabled={isInviting}
              required
            />
            <button
              type="submit"
              className={primaryBtn}
              disabled={isInviting || !inviteEmail}
            >
              {isInviting ? <Loader2 size={16} className="animate-spin" /> : 'Invite'}
            </button>
          </form>

          <div>
            <div className="flex justify-between items-center mb-2">
              <h4 className="text-md font-medium text-white">Who has access:</h4>
              <button onClick={() => fetchShares()} disabled={isLoading} className="text-xs text-gray-400 hover:text-white disabled:opacity-50 p-1 rounded hover:bg-noir-50">
                {isLoading ? <Loader2 size={14} className="animate-spin"/> : <RefreshCw size={14} />}
              </button>
            </div>
            <div className="max-h-48 overflow-y-auto bg-noir-200 p-3 rounded border border-noir-50">
              {isLoading && <div className="flex justify-center items-center py-4"><Loader2 size={20} className="animate-spin text-gray-400"/><span className="ml-2 text-gray-400 text-sm">Loading access list...</span></div>}
              {error && <div className="text-center py-4 text-red-400 text-sm">Error: {error}</div>}
              {!isLoading && !error && (
                <ul className="space-y-2 text-sm text-gray-300">
                  <li className="flex justify-between items-center opacity-70"><span>You (Owner)</span><span className="text-xs bg-noir-50 px-2 py-0.5 rounded">Owner</span></li>
                  {isPublic && <li className="flex justify-between items-center text-green-400"><span>Anyone</span><span className="text-xs bg-green-900/50 px-2 py-0.5 rounded">Can View</span></li>}
                  {shares.map(share => (
                    <li key={share.id} className="flex justify-between items-center group">
                      <span className="truncate pr-2" title={share.username}>{share.username}</span>
                      <div className="flex items-center space-x-1">
                        <select
                          value={share.permission_level}
                          onChange={e => handleUpdatePermission(share.id, e.target.value as 'view' | 'edit', share.username)}
                          className={`${inputClass.replace('w-full','w-auto')} text-xs py-0.5 px-1`}
                          disabled={updatingIds[share.id]}
                        >
                          <option value="view">Can View</option>
                          <option value="edit">Can Edit</option>
                        </select>
                        {updatingIds[share.id] && <Loader2 size={12} className="animate-spin text-gray-400" />}
                        <button
                          onClick={() => handleRemoveShare(share.id, share.username)}
                          disabled={deletingIds[share.id]}
                          className="text-gray-500 hover:text-red-500 p-0.5 rounded hover:bg-noir-50 disabled:opacity-50"
                          title="Remove access"
                        >
                          {deletingIds[share.id] ? <Loader2 size={12} className="animate-spin text-red-500" /> : <UserX size={14}/>}
                        </button>
                      </div>
                    </li>
                  ))}
                  {!isPublic && shares.length === 0 && <li className="text-center text-gray-500 text-xs py-2">No one else has access yet.</li>}
                </ul>
              )}
            </div>
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default ShareModal; 