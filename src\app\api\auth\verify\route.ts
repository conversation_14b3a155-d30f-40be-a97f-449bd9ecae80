import { NextRequest, NextResponse } from 'next/server';
// import { createClient } from '@supabase/supabase-js'; // No longer needed for admin client
import { cookies } from 'next/headers';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import type { Database } from '@/lib/database.types';

// Initialize Supabase client with service role key for admin operations (Removing this)
// const supabaseAdmin = createClient<Database>(
// process.env.NEXT_PUBLIC_SUPABASE_URL!,
// process.env.SUPABASE_SERVICE_ROLE_KEY!
// );

export async function POST(request: NextRequest) {
  try {
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient<Database>({ cookies: () => cookieStore });

    const { userId, code } = await request.json();

    if (!userId || !code) {
      return NextResponse.json({ error: 'User ID and verification code are required' }, { status: 400 });
    }

    // Check user in public.users table
    const { data: userProfile, error: userError } = await supabase // Use route handler client
      .from('users')
      .select('id, is_verified')
      .eq('id', userId)
      .single();

    if (userError || !userProfile) {
      console.error("Verify Error - User Profile Fetch:", userError);
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    if (userProfile.is_verified) {
      return NextResponse.json({
        message: 'User is already verified',
        userId: userProfile.id,
      }, { status: 200 });
    }

    const now = new Date();
    const { data: verificationCode, error: codeFindError } = await supabase // Use route handler client
      .from('verification_codes')
      .select('id')
      .eq('user_id', userId)
      .eq('code', code)
      .gt('expires_at', now.toISOString())
      .order('sent_at', { ascending: false })
      .limit(1)
      .maybeSingle();

    if (codeFindError || !verificationCode) {
      if (codeFindError) {
        console.error("Verify Error - Code Fetch Query Failed:", codeFindError);
      }
      return NextResponse.json({ error: 'Invalid or expired verification code' }, { status: 400 });
    }

    // WARNING: This admin operation might fail without service_role. 
    // Consider a SECURITY DEFINER function in your database if this is an issue.
    const { error: supabaseAuthError } = await supabase.auth.admin.updateUserById(
      userId,
      { email_confirm: true }
    );

    if (supabaseAuthError) {
      console.error('Verify Error - Failed to update Supabase Auth user:', supabaseAuthError);
      // Depending on requirements, you might want to return an error here
      // For now, it logs and proceeds, which might be desired if profile update is main goal
    }

    const { error: profileUpdateError } = await supabase // Use route handler client
      .from('users')
      .update({ is_verified: true, updated_at: now.toISOString() })
      .eq('id', userId);

    if (profileUpdateError) {
      console.error('Verify Error - Failed to update public user profile:', profileUpdateError);
      return NextResponse.json({ error: 'Failed to update user profile verification status' }, { status: 500 });
    }

    const { error: codeDeleteError } = await supabase // Use route handler client
      .from('verification_codes')
      .delete()
      .eq('user_id', userId);

    if (codeDeleteError) {
      console.error('Verify Error - Failed to delete verification codes:', codeDeleteError);
    }

    return NextResponse.json({
      message: 'Email verified successfully. Please sign in.',
      userId: userId,
    }, { status: 200 });

  } catch (error) {
    console.error('Verification Route Error:', error);
    return NextResponse.json({ error: 'An error occurred during verification' }, { status: 500 });
  }
} 