import React, { useEffect, useRef, useState } from 'react';

interface AutoFitTextProps {
  children: string;
  className?: string;
  style?: React.CSSProperties;
  mode?: 'single' | 'multi';
  min?: number;
  max?: number;
  parentWidth?: number;
  parentHeight?: number;
}

const AutoFitText: React.FC<AutoFitTextProps> = ({
  children,
  className = '',
  style = {},
  mode = 'multi',
  min = 8,
  max = 80,
  parentWidth,
  parentHeight,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const textRef = useRef<HTMLDivElement>(null);
  const [fontSize, setFontSize] = useState(max);

  useEffect(() => {
    const fitText = () => {
      if (!containerRef.current || !textRef.current) return;
      
      // Reset font size to max before measuring
      textRef.current.style.fontSize = `${max}px`;
      
      const containerWidth = containerRef.current.clientWidth;
      const containerHeight = containerRef.current.clientHeight;
      
      let currentSize = max;
      
      // If single line mode, only check width
      if (mode === 'single') {
        while (
          textRef.current.scrollWidth > containerWidth && 
          currentSize > min
        ) {
          currentSize--;
          textRef.current.style.fontSize = `${currentSize}px`;
        }
      } else {
        // For multi-line mode, check both width and height
        while (
          (textRef.current.scrollWidth > containerWidth || 
           textRef.current.scrollHeight > containerHeight) && 
          currentSize > min
        ) {
          currentSize--;
          textRef.current.style.fontSize = `${currentSize}px`;
        }
      }
      
      setFontSize(currentSize);
    };

    fitText();
    
    // Re-fit when window resizes
    window.addEventListener('resize', fitText);
    return () => window.removeEventListener('resize', fitText);
  }, [children, min, max, mode, parentWidth, parentHeight]);

  return (
    <div 
      ref={containerRef}
      className={className}
      style={{ 
        width: '100%', 
        height: '100%', 
        overflow: 'hidden',
        ...style
      }}
    >
      <div
        ref={textRef}
        style={{ 
          fontSize: `${fontSize}px`,
          lineHeight: mode === 'single' ? '1' : style.lineHeight || '1.1',
          whiteSpace: mode === 'single' ? 'nowrap' : 'normal',
          width: '100%',
          height: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}
      >
        {children}
      </div>
    </div>
  );
};

export default AutoFitText; 