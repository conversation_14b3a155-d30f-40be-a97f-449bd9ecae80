import { AuthService } from '../types';
import { useRouter } from 'next/navigation';

/**
 * Implementation of the AuthService interface
 */
export class AuthServiceImpl implements AuthService {
  /**
   * Get the authentication token from local storage
   */
  getAuthToken(): string | null {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem('auth_token');
  }

  /**
   * Check if the user is authenticated
   */
  isAuthenticated(): boolean {
    return !!this.getAuthToken();
  }

  /**
   * Redirect to the authentication page
   */
  redirectToAuth(): void {
    if (typeof window === 'undefined') return;
    window.location.href = '/auth';
  }
}

/**
 * Singleton instance of the auth service
 */
export const authService = new AuthServiceImpl();

/**
 * Hook to use auth service in components
 */
export const useAuthService = (): AuthService => {
  const router = useRouter();
  
  return {
    getAuthToken: authService.getAuthToken,
    isAuthenticated: authService.isAuthenticated,
    redirectToAuth: () => router.push('/auth')
  };
}; 