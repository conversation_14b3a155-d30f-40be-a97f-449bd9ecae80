import React, { useState, useRef, useEffect } from 'react';
import { ExternalLink, Trash2, Image, UploadCloud } from 'lucide-react';
import BoardItem, { BoardItemProps } from '../BoardItem';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import type { Database } from '@/lib/database.types';

interface ArticleNodeProps extends Omit<BoardItemProps, 'type' | 'children'> {
  title: string;
  url: string;
  website_url?: string;
  imageUrl?: string | null; // Can be blob: or http: or null
  file_url?: string | null; // Should contain the persistent storage path
  onDelete: (id: string) => void;
  onContentChange: (id: string, content: string) => void;
  onTitleChange: (id: string, title: string) => void;
  scale: number;
  connectMode?: boolean;
  connectStart?: string | null;
  onConnectionStart?: (id: string) => void;
  onConnectionComplete?: (id: string) => void;
  width: number;
  height: number;
  onSizeChange: (id: string, width: number, height: number) => void;
  onRequestImageChange: (id: string) => void;
  isMultiSelected?: boolean; // Added for multi-selection
}

const ArticleNode: React.FC<ArticleNodeProps> = ({
  id,
  title,
  content,
  url,
  website_url,
  imageUrl,
  file_url,
  position,
  width,
  height,
  onSizeChange,
  onPositionChange,
  onSelect,
  isSelected,
  onContentChange,
  onTitleChange,
  onDelete,
  scale,
  connectMode = false,
  connectStart = null,
  onConnectionStart,
  onConnectionComplete,
  onRequestImageChange,
  isMultiSelected, // Destructure isMultiSelected
  ...rest // Capture other props
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editableContent, setEditableContent] = useState(content);
  const [isTitleEditing, setIsTitleEditing] = useState(false);
  const [editableTitle, setEditableTitle] = useState(title);
  const originalContentRef = useRef<string | null>(null);
  const originalTitleRef = useRef<string | null>(null);
  const [imageError, setImageError] = useState(false);
  const [effectiveImageUrl, setEffectiveImageUrl] = useState<string | null>(null);

  // --- Dynamic font size calculations ---
  const baseWidth = 300; // Base width in pixels for typical font scaling
  const scaleFactor = width / baseWidth;
  // Clamp scale factor to prevent excessively small or large text (e.g., 70% to 200%)
  const clampedScale = Math.max(0.7, Math.min(2.0, scaleFactor));

  const titleFontSizeRem = 1.5 * clampedScale;
  const editableTitleFontSizeRem = 1.25 * clampedScale;
  const contentFontSizeRem = 0.875 * clampedScale;
  const metaFontSizeRem = 0.75 * clampedScale; // For smaller texts like date, domain, footer, image captions
  const contentLineHeight = 1.5; // Unitless, relative to contentFontSizeRem for main content area


  // --- Dynamic tape styling calculations ---
  const tapeBaseWidthPx = 100;
  const tapeBaseHeightPx = 30;
  const dynamicTapeWidthPx = tapeBaseWidthPx * clampedScale;
  const dynamicTapeHeightPx = tapeBaseHeightPx * clampedScale;
  // Maintain ~40% of tape height as overlap (e.g., 12px up for 30px height)
  const dynamicTapeTranslateYPx = -(dynamicTapeHeightPx * 0.4);
  // --- End dynamic tape styling calculations ---

  useEffect(() => {
    console.log(`[ArticleNode ${id}] useEffect triggered. Received imageUrl: ${imageUrl?.substring(0,50)}..., file_url: ${file_url}`);
    setImageError(false);

    // Priority 1: Use imageUrl if it's a displayable URL (blob or http)
    if (imageUrl && (imageUrl.startsWith('http') || imageUrl.startsWith('blob:') || imageUrl.startsWith('data:'))) {
      console.log(`[ArticleNode ${id}] Using displayable imageUrl: ${imageUrl.substring(0,50)}...`);
      setEffectiveImageUrl(imageUrl);
      return;
    }

    // Priority 2: If imageUrl isn't displayable, but file_url (storage path) exists, fetch signed URL
    if (file_url) {
      console.log(`[ArticleNode ${id}] imageUrl not displayable, fetching signed URL for file_url: ${file_url}`);
      
      const fetchSignedUrl = async (path: string) => {
        try {
          const supabase = createClientComponentClient<Database>();
          console.log(`[ArticleNode ${id}] Attempting createSignedUrl for path: ${path}`);
          const { data: signedUrlData, error: signedUrlError } = await supabase
            .storage
            .from('images')
            .createSignedUrl(path, 3600); // 1 hour expiry
          
          if (signedUrlError) {
            console.error(`[ArticleNode ${id}] Failed createSignedUrl for path ${path}:`, signedUrlError);
            console.log(`[ArticleNode ${id}] Attempting getPublicUrl for path: ${path}`);
            const { data: publicUrlData } = supabase.storage.from('images').getPublicUrl(path);
            
            if (publicUrlData?.publicUrl) {
              console.log(`[ArticleNode ${id}] Using public URL for path ${path}:`, publicUrlData.publicUrl);
              setEffectiveImageUrl(publicUrlData.publicUrl);
            } else {
              console.warn(`[ArticleNode ${id}] Failed getPublicUrl for path ${path}. Setting image to null.`);
              setEffectiveImageUrl(null);
              setImageError(true); 
            }
          } else if (signedUrlData?.signedUrl) {
            console.log(`[ArticleNode ${id}] Successfully generated signed URL for path ${path}`);
            setEffectiveImageUrl(signedUrlData.signedUrl);
          } else {
             console.warn(`[ArticleNode ${id}] createSignedUrl returned no data/error for path ${path}. Setting image to null.`);
             setEffectiveImageUrl(null);
             setImageError(true);
          }
        } catch (error) {
          console.error(`[ArticleNode ${id}] Error in fetchSignedUrl for path ${path}:`, error);
          setEffectiveImageUrl(null);
          setImageError(true);
        }
      };
      
      fetchSignedUrl(file_url);
      return; // Don't fall through to the final fallback yet
    }

    // Fallback: Neither imageUrl nor file_url yielded a result
    console.log(`[ArticleNode ${id}] No displayable imageUrl or file_url found.`);
    setEffectiveImageUrl(null);
    
  }, [id, imageUrl, file_url]); // Depend on id, imageUrl, and file_url

  const handleDoubleClick = (e: React.MouseEvent) => {
    if (connectMode) {
      e.preventDefault();
      return;
    }
    originalContentRef.current = content; // Store original content
    setIsEditing(true);
  };

  const handleTitleDoubleClick = (e: React.MouseEvent) => {
    if (connectMode) {
      e.preventDefault();
      return;
    }
    originalTitleRef.current = title;
    setIsTitleEditing(true);
    setEditableTitle(title);
  };

  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEditableTitle(e.target.value);
  };

  const handleTitleBlur = () => {
    const wasEditing = isTitleEditing;
    setIsTitleEditing(false);
    
    if (wasEditing && editableTitle !== title && editableTitle.trim() !== '') {
      onTitleChange(id, editableTitle.trim());
    } else {
      // Reset to original if empty or unchanged
      setEditableTitle(title);
    }
  };

  const handleTitleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      setIsTitleEditing(false);
      
      if (editableTitle !== title && editableTitle.trim() !== '') {
        onTitleChange(id, editableTitle.trim());
      } else {
        setEditableTitle(title);
      }
    } else if (e.key === 'Escape') {
      setIsTitleEditing(false);
      setEditableTitle(title); // Revert on escape
    }
  };

  const handleTitleInputFocus = (e: React.FocusEvent<HTMLInputElement>) => {
    e.target.select();
  };

  const handleBlur = () => {
    const wasEditing = isEditing; // Check if we were editing *before* setting state
    setIsEditing(false);
    
    // Only compare and update content if we were actually editing AND content changed
    if (wasEditing && editableContent !== content) {
        onContentChange(id, editableContent);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setEditableContent(e.target.value);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      setIsEditing(false);
      // Compare final content against the prop content
      if (editableContent !== content) {
          onContentChange(id, editableContent);
      }
    }
  };

  // Auto-select all text when the textarea is focused
  const handleTextareaFocus = (e: React.FocusEvent<HTMLTextAreaElement>) => {
    e.target.select();
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    onDelete(id);
  };

  const openArticle = (e: React.MouseEvent) => {
    if (connectMode) {
      e.preventDefault();
      e.stopPropagation();
      return;
    }
    e.stopPropagation();
    
    // Make sure we're opening the website_url, not the file_url that might be in the url property
    const linkToOpen = website_url ||
      // Only use url as fallback IF IT'S NOT a storage path AND website_url is missing
      (url && !website_url && !url.includes('/article-') && !url.includes('.jpg') && !url.includes('.png') ?
        url : null);
    
    if (linkToOpen) {
      window.open(linkToOpen, '_blank', 'noopener,noreferrer');
    } else {
      console.warn('No valid URL to open for this article');
    }
  };

  const handleImageError = () => {
    console.warn(`Image failed to load: ${effectiveImageUrl}`);
    setImageError(true);
  };

  // Extract domain for display
  const getDomain = () => {
    // Only try to extract domain from website_url, not from file_url
    if (website_url) {
      try {
        const urlObj = new URL(website_url);
        return urlObj.hostname.replace('www.', '');
      } catch (e) {
        return 'website';
      }
    }
    
    // If no website_url but url doesn't look like a storage path, try to extract from url
    if (url && !url.includes('/article-') && !url.includes('.jpg') && !url.includes('.png')) {
      try {
        const urlObj = new URL(url);
        return urlObj.hostname.replace('www.', '');
      } catch (e) {
        return 'website';
      }
    }
    
    // Fallback
    return 'article';
  };

  // Modified handler to call the prop
  const handleChangeImageClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onRequestImageChange(id); // Call the prop to signal modal opening
  };

  return (
    <BoardItem
      id={id}
      type="article"
      content={content}
      position={position}
      width={width}
      height={height}
      onSizeChange={onSizeChange}
      onPositionChange={onPositionChange}
      onSelect={onSelect}
      isSelected={isSelected}
      scale={scale}
      isEditMode={isEditing || isTitleEditing}
      connectMode={connectMode}
      connectStart={connectStart}
      onConnectionStart={onConnectionStart}
      onConnectionComplete={onConnectionComplete}
      isMultiSelected={isMultiSelected}
    >
      <style jsx global>{`
        .newspaper-component *::selection {
          background-color: rgba(59, 130, 246, 0.3) !important;
          color: #1e293b !important;
        }
      `}</style>
      {/* Pinned Evidence Note styling container */}
      <div className="relative flex flex-col bg-yellow-50 border-b-2 border-r-2 border-yellow-200 rounded-md shadow-lg p-4 transform -rotate-1 w-full h-full font-serif">
        {/* Torn Tape Effect */}
        <div 
          className="absolute top-0 left-1/2 z-10 pointer-events-none"
          style={{
            width: `${dynamicTapeWidthPx}px`,
            height: `${dynamicTapeHeightPx}px`,
            backgroundColor: 'rgba(245, 222, 179, 0.75)',
            borderLeft: '2px dashed rgba(0,0,0,0.15)',
            borderRight: '2px dashed rgba(0,0,0,0.15)',
            borderTop: '1px solid rgba(0,0,0,0.05)',
            borderBottom: '1px solid rgba(0,0,0,0.05)',
            boxShadow: '0 2px 3px rgba(0,0,0,0.15)',
            transform: `translateX(-50%) translateY(${dynamicTapeTranslateYPx}px)`,
          }}
        />
        
        {/* Header Area: Title and Action Buttons */}
        <div className="relative flex justify-between items-start mb-3">
          {/* Title */}
          {isTitleEditing ? (
            <input
              type="text"
              value={editableTitle}
              onChange={handleTitleChange}
              onBlur={handleTitleBlur}
              onKeyDown={handleTitleKeyDown}
              onFocus={handleTitleInputFocus}
              className="font-bold text-stone-900 w-full bg-white p-1 rounded border border-blue-400 focus:outline-none"
              style={{ fontSize: `${editableTitleFontSizeRem}rem` }}
              autoFocus
              data-nodrag="true"
            />
          ) : (
            <h3 
              className="font-black text-stone-900 cursor-text select-text leading-tight article-title flex-grow mr-2"
              style={{ 
                fontSize: `${titleFontSizeRem}rem`,
                WebkitUserSelect: 'text',
                MozUserSelect: 'text',
                msUserSelect: 'text',
                userSelect: 'text'
              }}
              onDoubleClick={handleTitleDoubleClick}
              data-nodrag="true"
            >
              {title}
            </h3>
          )}
          
          {/* Action buttons - relocated to top right */}
          {!connectMode && !isMultiSelected && (
            <div className="flex flex-col items-end space-y-1 absolute top-0 right-0 transform translate-x-1 -translate-y-1 bg-yellow-50 p-1 rounded-tr-md">
                 <button
                  className="p-1 hover:bg-yellow-100 rounded transition-colors text-stone-600"
                  onClick={openArticle}
                  data-nodrag="true"
                  title="Open Article"
                >
                  <ExternalLink size={14} />
                </button>
                <button
                  className="p-1 hover:bg-yellow-100 rounded transition-colors text-stone-600"
                  onClick={handleChangeImageClick}
                  data-nodrag="true"
                  title="Change Image"
                >
                  <UploadCloud size={14} />
                </button>
                <button
                  className="p-1 hover:bg-yellow-100 rounded transition-colors text-red-500"
                  onClick={handleDelete}
                  data-nodrag="true"
                  title="Delete Article"
                >
                  <Trash2 size={14} />
                </button>
            </div>
          )}
        </div>
        
        {/* Domain and Date - previously masthead */}
        <div 
          className="text-stone-500 mb-2 flex justify-between items-center border-b border-stone-200 pb-1"
          style={{ fontSize: `${metaFontSizeRem}rem` }}
        >
          <div className="uppercase tracking-wider">{getDomain()}</div>
          <div>
            {new Date().toLocaleDateString('en-US', {year: 'numeric', month: 'short', day: 'numeric'})}
          </div>
        </div>

        {/* Image Section */}
        {effectiveImageUrl && !imageError && (
          <div className="w-full bg-stone-200 overflow-hidden border-b border-stone-300 flex-none">
            <img
              src={effectiveImageUrl}
              alt={title}
              className="w-full h-full object-cover"
              onError={handleImageError}
              referrerPolicy="no-referrer"
              loading="lazy"
            />
            <div 
              className="italic text-stone-600 px-2 py-1 border-t border-stone-300 bg-stone-100"
              style={{ fontSize: `${metaFontSizeRem}rem` }}
            >
              {title} - Photo
            </div>
          </div>
        )}

        {/* Article Content - Ruled paper style */}
        <div 
          className="p-3 text-stone-800 font-serif flex-1 overflow-auto min-h-0"
          style={{
            backgroundImage: `
              linear-gradient(to bottom, transparent 0.95rem, #e0e0e040 0.95rem, #e0e0e040 1rem),
              linear-gradient(to right, transparent 0.95rem, #e0e0e040 0.95rem, #e0e0e040 1rem)
            `,
            backgroundSize: '1rem 1rem',
            lineHeight: '1.5rem', // Adjust if needed based on font size and desired line spacing
          }}
        >
          {isEditing ? (
            <textarea
              value={editableContent}
              onChange={handleChange}
              onBlur={handleBlur}
              onKeyDown={handleKeyDown}
              onFocus={handleTextareaFocus}
              onWheel={(e) => e.stopPropagation()}
              className="flex-1 w-full h-full bg-transparent p-2 text-stone-900 rounded border-none resize-none focus:outline-none focus:ring-0 selection:bg-blue-200 selection:text-blue-900"
              style={{ fontSize: `${contentFontSizeRem}rem`, lineHeight: contentLineHeight }}
              autoFocus
              data-nodrag="true"
            />
          ) : (
            <div
              className="flex-1 w-full overflow-auto p-2 text-stone-900 cursor-text selection:bg-blue-200 selection:text-blue-900"
              style={{ fontSize: `${contentFontSizeRem}rem`, lineHeight: contentLineHeight }}
              onWheel={(e) => e.stopPropagation()}
              onDoubleClick={handleDoubleClick}
              data-nodrag="true"
            >
              <div
                style={{
                  height: '100%',
                  columnCount: 2,
                  columnGap: '0.75rem', // Tailwind's gap-3 is 0.75rem
                  // columnRuleWidth: '1px', // Ruled paper effect takes care of vertical lines
                  // columnRuleStyle: 'solid',
                  // columnRuleColor: '#d6d3d1',
                  wordBreak: 'break-word',
                  // lineHeight is now inherited from parent with dynamic font size
                }}
              >
                {content || <span className="text-stone-500 italic">No article content. Double-click to add.</span>}
              </div>
            </div>
          )}
          
          {/* Newspaper footer */}
          <div 
            className="mt-3 pt-2 border-t border-stone-200 text-stone-500 flex justify-between"
            style={{ fontSize: `${metaFontSizeRem}rem` }}
          >
            <span>Source: {getDomain()}</span>
            <button
              className="hover:underline"
              onClick={openArticle}
              data-nodrag="true"
            >
              Read full article
            </button>
          </div>
        </div>
      </div>
    </BoardItem>
  );
};

export default ArticleNode;