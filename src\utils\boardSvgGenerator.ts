import { Board, BoardItem, Connection, PenStroke, Position, isImageNode } from '../types'; // Corrected types, Added isImageNode

// Constants for Supabase storage (ideally from environment variables)
// const SUPABASE_PROJECT_REF = "ngoqknggspzugiuyxlcw";
// const SUPABASE_IMAGES_BUCKET = "images";
// const SUPABASE_STORAGE_BASE_URL = `https://${SUPABASE_PROJECT_REF}.supabase.co/storage/v1/object/public/${SUPABASE_IMAGES_BUCKET}/`;

// Helper to convert image URL to data URI
async function imageToDataURI(originalUrl: string): Promise<string> {
  if (!originalUrl) {
    console.warn("[SVG Image] imageToDataURI called with empty URL.");
    return '';
  }

  // 1. Handle blob URLs
  if (originalUrl.startsWith('blob:')) {
    console.log(`[SVG Image] Handling blob URL: ${originalUrl}`);
    try {
        const response = await fetch(originalUrl);
        const blob = await response.blob();
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onloadend = () => resolve(reader.result as string);
            reader.onerror = (e) => {
                console.error(`[SVG Image] FileReader error for blob URL ${originalUrl}:`, e);
                reject(e);
            };
            reader.readAsDataURL(blob);
        });
    } catch (error) {
        console.error(`[SVG Image] Error fetching or reading blob URL ${originalUrl}:`, error);
        return '';
    }
  }

  // 2. Handle data URIs (they are already self-contained)
  if (originalUrl.startsWith('data:')) {
    console.log(`[SVG Image] URL "${originalUrl}" is already a data URI.`);
    return originalUrl;
  }

  // 3. For all other URLs (full http/https, or relative paths)
  let urlToFetch = originalUrl;
  console.log(`[SVG Image] Attempting to fetch: "${urlToFetch}"`);

  try {
    const response = await fetch(urlToFetch);

    if (!response.ok) {
      let errorMessage = `[SVG Image] Failed to fetch from "${urlToFetch}" (${response.status} ${response.statusText}).`;
      
      // Fallback for simple relative paths: try prepending /board/
      // This is a last resort, hoping a local server route might handle it.
      if (!originalUrl.startsWith('/') && !originalUrl.match(/^(https?:|data:|blob:)/)) {
        const localFallbackUrl = `/board/${originalUrl}`;
        console.warn(`${errorMessage} Retrying as local path: "${localFallbackUrl}"`);
        const fallbackResponse = await fetch(localFallbackUrl);
        
        if (fallbackResponse.ok) {
          console.log(`[SVG Image] Local fallback successful: "${localFallbackUrl}"`);
          const blob = await fallbackResponse.blob();
          return new Promise((resolve, reject) => {
              const reader = new FileReader();
              reader.onloadend = () => resolve(reader.result as string);
              reader.onerror = (e) => {
                  console.error(`[SVG Image] FileReader error on fallback for "${localFallbackUrl}":`, e);
                  reject(e);
              };
              reader.readAsDataURL(blob);
          });
        } else {
          errorMessage += ` Local fallback also failed: "${localFallbackUrl}" (${fallbackResponse.status} ${fallbackResponse.statusText}).`;
          console.error(errorMessage);
          throw new Error(errorMessage);
        }
      } else {
        console.error(errorMessage);
        throw new Error(errorMessage);
      }
    }

    const blob = await response.blob();
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => resolve(reader.result as string);
      reader.onerror = (error) => {
        console.error(`[SVG Image] FileReader error for "${urlToFetch}":`, error);
        reject(error);
      };
      reader.readAsDataURL(blob);
    });
  } catch (error) {
    const caughtErrorMessage = (error as any)?.message || 'Unknown error during image processing';
    console.error(`[SVG Image] General error processing URL "${originalUrl}" (attempted as "${urlToFetch}"): ${caughtErrorMessage}`, error);
    return '';
  }
}

// Helper for text wrapping (simplified)
function wrapText(
  text: string,
  maxWidth: number,
  fontSize: number,
  lineHeightFactor: number = 1.2,
  maxLines?: number,
  minFontSize: number = 8,
  maxFontSize: number = 80,
  maxHeight?: number
): { lines: string[], textHeight: number, fontSize: number } {
  let currentFontSize = fontSize;
  let lines: string[] = [];
  let textHeight = 0;
  
  // Binary search for the optimal font size
  let low = minFontSize;
  let high = Math.min(maxFontSize, fontSize); // Start with initial fontSize as max
  
  const findLines = (testFontSize: number) => {
    const approximateCharWidth = testFontSize * 0.55;
    const words = text.split(' ');
    const testLines = [];
    let currentLine = words[0] || '';
    
    for (let i = 1; i < words.length; i++) {
      const word = words[i];
      const testLine = `${currentLine} ${word}`;
      if (testLine.length * approximateCharWidth < maxWidth) {
        currentLine = testLine;
      } else {
        testLines.push(currentLine);
        currentLine = word;
      }
    }
    if (currentLine) {
      testLines.push(currentLine);
    }
    return testLines;
  };

  while (low <= high) {
    currentFontSize = Math.floor((low + high) / 2);
    lines = findLines(currentFontSize);
    textHeight = lines.length * currentFontSize * lineHeightFactor;
    
    // Check if this font size works
    if (maxHeight) {
      if (textHeight > maxHeight || lines.some(line => line.length * currentFontSize * 0.55 > maxWidth)) {
        // Text is too big, try smaller font size
        high = currentFontSize - 1;
      } else if (textHeight < maxHeight * 0.5) {
        // Text is too small, try larger font size
        low = currentFontSize + 1;
      } else {
        // Font size is good
        break;
      }
    } else {
      // If no height constraint, just check width
      if (lines.some(line => line.length * currentFontSize * 0.55 > maxWidth)) {
        high = currentFontSize - 1;
      } else {
        break;
      }
    }
  }
  
  // Ensure we use the last valid font size
  currentFontSize = Math.min(currentFontSize, high);
  lines = findLines(currentFontSize);
  textHeight = lines.length * currentFontSize * lineHeightFactor;
  
  return { lines, textHeight, fontSize: currentFontSize };
}

function escapeXml(unsafe: string): string {
    return unsafe.replace(/[<>&'"]/g, function (c) {
        switch (c) {
            case '<': return '&lt;';
            case '>': return '&gt;';
            case '&': return '&amp;';
            case '\'': return '&apos;';
            case '"': return '&quot;';
            default: return c;
        }
    });
}

const STICKY_NOTE_STYLES: Record<string, { gradientId: string; fromColor: string; toColor: string; textColor: string }> = {
  yellow: { gradientId: 'gradYellowSticky', fromColor: '#D1BCAA', toColor: '#C1AC9A', textColor: '#3A3026' }, // Adjusted text for better contrast
  red:    { gradientId: 'gradRedSticky',    fromColor: '#E57373', toColor: '#B71C1C', textColor: '#FFFFFF' }, // Example for red, actual from noir-accent needed
  blue:   { gradientId: 'gradBlueSticky',   fromColor: '#64B5F6', toColor: '#0D47A1', textColor: '#FFFFFF' }, // Example for blue, actual from noir-teal needed
  // Add other colors from StickyNote.tsx getColorClass if they exist (pink, purple, orange, gray etc.)
  default: { gradientId: 'gradDefaultSticky', fromColor: '#D1BCAA', toColor: '#C1AC9A', textColor: '#3A3026' },
};

export async function generateBoardSVG(
  board: Board,
  connections: Connection[],
  options: { embedImages?: boolean } = { 
    embedImages: true,
  }
): Promise<string> {
  let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;

  const allContentPoints: Position[] = [];

  board.elements.forEach((item: BoardItem) => {
    // Ensure we don't process items that are meant to be fully hidden from SVG
    // if (item.type === 'image-invisible') return; // This was removed, visibility is handled by rendering style now
    allContentPoints.push({ x: item.position.x, y: item.position.y });
    allContentPoints.push({ x: item.position.x + (item.width ?? 150), y: item.position.y + (item.height ?? 100) });
  });

  board.strokes?.forEach((stroke: PenStroke) => {
    stroke.points.forEach((p: Position) => allContentPoints.push({ x: p.x, y: p.y }));
  });
  
  if (allContentPoints.length === 0) {
      minX = 0; minY = 0; maxX = 800; maxY = 600; 
  } else {
    allContentPoints.forEach((p: Position) => {
        minX = Math.min(minX, p.x);
        minY = Math.min(minY, p.y);
        maxX = Math.max(maxX, p.x);
        maxY = Math.max(maxY, p.y);
    });
  }

  const padding = 50;
  const contentWidth = maxX - minX;
  const contentHeight = maxY - minY;

  const finalSvgWidth = Math.max(contentWidth + 2 * padding, 400);
  const finalSvgHeight = Math.max(contentHeight + 2 * padding, 300);
  
  const viewBoxX = minX - padding;
  const viewBoxY = minY - padding;
  const viewBoxWidth = finalSvgWidth;
  const viewBoxHeight = finalSvgHeight;

  // Define default colors (previously theme-dependent)
  const DEFAULT_TEXT_COLOR = '#ffffff'; // Changed to white for text nodes
  const DEFAULT_STROKE_COLOR = '#9ca3af'; // Medium gray for borders/strokes
  const BOARD_BACKGROUND_BASE_COLOR = '#1a1a1a'; // bg-noir-300 equivalent
  const connectionYarnColor = '#C0392B';

  let svgDefs = '';
  let svgElementsHtml: string = '';

  // Film Grain Filter - Updated to match the CSS implementation
  svgDefs += `
    <filter id="filmGrainEffect">
      <feTurbulence type="fractalNoise" baseFrequency="0.65" numOctaves="3" stitchTiles="stitch" result="noise"/>
      <feColorMatrix type="matrix" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 0.05 0" in="noise" result="monoNoise"/>
      <feBlend in="SourceGraphic" in2="monoNoise" mode="overlay" />
    </filter>
  `;

  // Define yarnWithShadow filter
  svgDefs += `
    <filter id="yarnWithShadow">
      <feTurbulence type="fractalNoise" baseFrequency="0.8" numOctaves="2" result="noiseOut" />
      <feDisplacementMap in="SourceGraphic" in2="noiseOut" scale="2" result="textured" />
      <feDropShadow dx="3" dy="4" stdDeviation="3.5" flood-color="rgba(0,0,0,0.5)" in="textured" result="finalYarn"/>
    </filter>
  `;

  // Define simple drop shadow for pins if needed, or use inline filter on elements
  svgDefs += `
    <filter id="pinDropShadow">
      <feDropShadow dx="0" dy="2" stdDeviation="2" flood-color="rgba(0,0,0,0.5)" />
    </filter>
  `;

  // Sticky Note Gradients and Shadow Filter
  svgDefs += `
    <filter id="stickyShadowFilter" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur in="SourceAlpha" stdDeviation="3" result="blur"/>
      <feOffset in="blur" dx="2" dy="2" result="offsetBlur"/>
      <feComponentTransfer in="offsetBlur" result="shadowOpacity">
        <feFuncA type="linear" slope="0.3"/>
      </feComponentTransfer>
      <feMerge>
        <feMergeNode in="shadowOpacity"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  `;

  // Add polaroid shadow filter
  svgDefs += `
    <filter id="polaroidShadowFilter" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="4" stdDeviation="3" flood-color="rgba(0,0,0,0.15)" result="shadow"/>
      <feMerge>
        <feMergeNode in="shadow"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  `;

  // Add sticky note gradients
  for (const key in STICKY_NOTE_STYLES) {
    const style = STICKY_NOTE_STYLES[key];
    svgDefs += `
      <linearGradient id="${style.gradientId}" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:${style.fromColor};stop-opacity:1" />
        <stop offset="100%" style="stop-color:${style.toColor};stop-opacity:1" />
      </linearGradient>
    `;
  }

  // Add background rectangle with film grain effect
  svgElementsHtml += `<rect x="${viewBoxX}" y="${viewBoxY}" width="${viewBoxWidth}" height="${viewBoxHeight}" fill="${BOARD_BACKGROUND_BASE_COLOR}" />`;
  svgElementsHtml += `<rect x="${viewBoxX}" y="${viewBoxY}" width="${viewBoxWidth}" height="${viewBoxHeight}" fill="${BOARD_BACKGROUND_BASE_COLOR}" filter="url(#filmGrainEffect)" />`;

  // Add grid pattern to match the Grid component
  svgDefs += `
    <pattern id="gridPattern" x="0" y="0" width="30" height="30" patternUnits="userSpaceOnUse">
      <circle cx="1" cy="1" r="1" fill="rgba(255,255,255,0.03)" />
    </pattern>
  `;
  svgElementsHtml += `<rect x="${viewBoxX}" y="${viewBoxY}" width="${viewBoxWidth}" height="${viewBoxHeight}" fill="url(#gridPattern)" />`;

  // Loop through board elements
  console.log(`[SVG Generator] Processing ${board.elements.length} board elements.`); // Log total elements

  for (const item of board.elements as BoardItem[]) {
    console.log(`[SVG Generator Loop] Item ID: ${item.id}, Type: ${item.type}`); // ACTIVE LOG

    const elX = item.position.x;
    const elY = item.position.y;
    const elWidth = item.width ?? 150;
    const elHeight = item.height ?? 100;
    const itemPadding = 10;
    let itemSvg = '';

    if (item.type.startsWith('sticky')) {
      const colorKey = item.type.startsWith('sticky-') ? item.type.split('-')[1] : (item.color || 'default');
      const style = STICKY_NOTE_STYLES[colorKey] || STICKY_NOTE_STYLES.default;
      
      itemSvg += `<rect x="${elX}" y="${elY}" width="${elWidth}" height="${elHeight}" fill="url(#${style.gradientId})" rx="3" ry="3" filter="url(#stickyShadowFilter)" />`;
      
      const pinWidth = Math.min(elWidth * 0.3, 30);
      const pinHeight = 4;
      itemSvg += `<rect x="${elX + itemPadding}" y="${elY + itemPadding / 2}" width="${pinWidth}" height="${pinHeight}" fill="rgba(0,0,0,0.2)" rx="${pinHeight/2}" ry="${pinHeight/2}" />`;

      if (item.content) {
        const textYOffset = itemPadding + pinHeight + itemPadding;
        const availableTextHeight = elHeight - textYOffset - (2 * itemPadding);
        const availableTextWidth = elWidth - (4 * itemPadding); // More horizontal padding
        
        // Calculate optimal font size and wrap text
        const { lines, fontSize } = wrapText(
          item.content,
          availableTextWidth,
          Math.min(elWidth / 8, 40), // More conservative initial font size
          1.3,
          undefined,
          10,
          40, // Reduced max font size
          availableTextHeight
        );

        // Center text vertically in available space
        const totalTextHeight = lines.length * fontSize * 1.3;
        const verticalPadding = Math.max(0, (availableTextHeight - totalTextHeight) / 2);
        const textStartY = elY + textYOffset + verticalPadding + fontSize;

        // Create a text group for better organization
        itemSvg += `<g transform="translate(${elX + elWidth/2}, 0)">`;
        
        // Add text elements
        lines.forEach((line, index) => {
          itemSvg += `<text 
            x="0" 
            y="${textStartY + index * fontSize * 1.3}" 
            font-family="'handwritten-font', 'Comic Sans MS', 'Brush Script MT', sans-serif" 
            font-size="${fontSize}px" 
            fill="${style.textColor}"
            text-anchor="middle"
            dominant-baseline="middle">${escapeXml(line)}</text>`;
        });
        
        itemSvg += `</g>`;
      }
    } else if (item.type === 'text') {
      if (item.content) {
        // Calculate available space
        const textPadding = 8; // Matches the p-2 class in TextNode
        const availableWidth = elWidth - (2 * textPadding);
        const availableHeight = elHeight - (2 * textPadding);

        // Calculate optimal font size and wrap text
        const { lines, fontSize } = wrapText(
          item.content,
          availableWidth,
          Math.min(elWidth / 8, 60), // Initial font size guess
          1.2, // Line height factor
          undefined,
          12, // Min font size
          60, // Max font size
          availableHeight
        );

        // Center text vertically in available space
        const totalTextHeight = lines.length * fontSize * 1.2;
        const verticalPadding = Math.max(0, (availableHeight - totalTextHeight) / 2);
        const textStartY = elY + textPadding + verticalPadding + fontSize;

        // Create a text group for better organization and centering
        itemSvg += `<g transform="translate(${elX + elWidth/2}, 0)">`;
        
        // Add text elements
        lines.forEach((line, index) => {
          itemSvg += `<text 
            x="0" 
            y="${textStartY + index * fontSize * 1.2}" 
            font-family="'Gaegu', 'Comic Sans MS', cursive" 
            font-size="${fontSize}px" 
            fill="${DEFAULT_TEXT_COLOR}"
            text-anchor="middle"
            dominant-baseline="middle"
            class="handwritten-font">${escapeXml(line)}</text>`;
        });
        
        itemSvg += `</g>`;
      }
    } else if (item.type === 'article') {
      // --- Pinned Evidence Styling for Article Nodes in SVG ---
      const rotation = (parseInt(item.id.slice(-2), 16) % 2 === 0) ? -1 : 1; // Simple rotation based on ID
      itemSvg += `<g transform="translate(${elX + elWidth / 2}, ${elY + elHeight / 2}) rotate(${rotation}) translate(${-elWidth / 2}, ${-elHeight / 2})">`;
      
      // Main note background
      itemSvg += `<rect x="0" y="0" width="${elWidth}" height="${elHeight}" fill="#FEFBEB" stroke="#FDE68A" stroke-width="1" rx="3" ry="3" shadow="2px 2px 5px rgba(0,0,0,0.1)"/>`;

      // Tape effect (simplified)
      const tapeWidth = Math.min(elWidth * 0.3, 80) * ( (item.width ?? 300) /300 ); // Scale tape with article width
      const tapeHeight = 15 * ( (item.width ?? 300) /300 ); 
      const tapeX = (elWidth - tapeWidth) / 2;
      const tapeY = -tapeHeight * 0.4; // Overlap top edge
      itemSvg += `<rect x="${tapeX}" y="${tapeY}" width="${tapeWidth}" height="${tapeHeight}" fill="rgba(245, 222, 179, 0.75)" rx="2" ry="2" stroke="rgba(0,0,0,0.1)" stroke-width="0.5"/>`;

      let currentY = itemPadding;
      const availableWidth = elWidth - 2 * itemPadding;

      // Title
      if (item.title) {
        const titleFontSize = Math.max(12, Math.min(availableWidth / (item.title.length * 0.4 || 1), elHeight * 0.12, 24));
        const { lines, textHeight: titleTextHeight } = wrapText(item.title, availableWidth, titleFontSize, 1.2, 2, 10, titleFontSize, elHeight * 0.25);
        lines.forEach((line, idx) => {
          itemSvg += `<text x="${itemPadding}" y="${currentY + lines.length === 1 ? titleFontSize * 0.8 : titleFontSize + idx * titleFontSize * 1.2}" font-family="'Georgia', serif" font-size="${titleFontSize}px" font-weight="bold" fill="#333333">${escapeXml(line)}</text>`;
        });
        currentY += titleTextHeight + itemPadding * 0.5;
      }

      // Domain & Date (simplified)
      const metaFontSize = Math.max(8, Math.min(availableWidth / 30, elHeight * 0.05, 10));
      const domain = item.website_url ? new URL(item.website_url).hostname.replace('www.', '') : (item.url && !item.url.startsWith('http') ? 'local source' : 'source');
      itemSvg += `<text x="${itemPadding}" y="${currentY + metaFontSize}" font-family="'Arial', sans-serif" font-size="${metaFontSize}px" fill="#666666">${escapeXml(domain)}</text>`;
      const dateStr = new Date().toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
      itemSvg += `<text x="${elWidth - itemPadding}" y="${currentY + metaFontSize}" font-family="'Arial', sans-serif" font-size="${metaFontSize}px" fill="#666666" text-anchor="end">${dateStr}</text>`;
      currentY += metaFontSize + itemPadding;

      // Image
      const imageUrlToRenderForArticle = item.imageUrl || item.file_url;
      const imageSpaceTop = currentY;
      const imageMaxHeight = elHeight * 0.35; // Max 35% of node height for image
      const remainingHeightForImage = elHeight - imageSpaceTop - itemPadding; // Space left before content starts
      
      if (imageUrlToRenderForArticle && remainingHeightForImage > imageMaxHeight * 0.5 && imageMaxHeight > 20) { 
        let dataUri = imageUrlToRenderForArticle;
        if (options.embedImages && !imageUrlToRenderForArticle.startsWith('data:')) {
          try {
            dataUri = await imageToDataURI(imageUrlToRenderForArticle);
          } catch (e) {
            console.error(`[SVG Article Image] Failed to convert ${imageUrlToRenderForArticle} to data URI:`, e);
            dataUri = ''; // Prevent broken image link
          }
        }
        if (dataUri) {
          const imgX = itemPadding;
          const imgY = imageSpaceTop;
          const imgWidth = availableWidth;
          itemSvg += `<image href="${escapeXml(dataUri)}" x="${imgX}" y="${imgY}" width="${imgWidth}" height="${imageMaxHeight}" preserveAspectRatio="xMidYMid slice" />`;
          currentY += imageMaxHeight + itemPadding * 0.5;
        } else {
           // Placeholder for missing image if dataUri conversion fails
           itemSvg += `<rect x="${itemPadding}" y="${imageSpaceTop}" width="${availableWidth}" height="${imageMaxHeight}" fill="#f0f0f0" stroke="#cccccc" stroke-width="0.5" />`;
           itemSvg += `<text x="${elWidth / 2}" y="${imageSpaceTop + imageMaxHeight / 2}" text-anchor="middle" dominant-baseline="middle" font-family="sans-serif" font-size="10px" fill="#888888">Image N/A</text>`;
           currentY += imageMaxHeight + itemPadding * 0.5;
        }
      } else {
        // No image or not enough space, add a small gap or line
        currentY += itemPadding * 0.25;
      }

      // Content (simplified text block, no columns for SVG complexity)
      if (item.content) {
        const contentFontSize = Math.max(10, Math.min(availableWidth / (item.content.length * 0.05 || 1), elHeight * 0.06, 12));
        const contentMaxHeight = elHeight - currentY - itemPadding;
        if (contentMaxHeight > contentFontSize * 1.2) { // Ensure there's enough space for at least one line
            const { lines } = wrapText(item.content, availableWidth, contentFontSize, 1.3, undefined, 8, contentFontSize, contentMaxHeight);
            lines.forEach((line, idx) => {
              if (currentY + (idx + 1) * contentFontSize * 1.3 < elHeight - itemPadding) { // Prevent overflow
                itemSvg += `<text x="${itemPadding}" y="${currentY + contentFontSize + idx * contentFontSize * 1.3}" font-family="'Georgia', serif" font-size="${contentFontSize}px" fill="#444444">${escapeXml(line)}</text>`;
              }
            });
        }
      }
      itemSvg += `</g>`; // Close rotation group
      // --- End Pinned Evidence Styling for Article --- 
    } else if (isImageNode(item)) { 
      console.log(`[SVG Generator] Item ID: ${item.id} - Passed isImageNode() check. Actual type: ${item.type}`); // ACTIVE LOG
      if (item.type === 'image') {
        console.log(`[SVG Generator] Item ID: ${item.id} - Rendering as 'image' (Polaroid).`); // ACTIVE LOG
        const imageUrlToRenderForImage = item.imageUrl || item.file_url;
        if (imageUrlToRenderForImage) {
          let dataUri = imageUrlToRenderForImage;
          if (options.embedImages && !imageUrlToRenderForImage.startsWith('data:')) {
            dataUri = await imageToDataURI(imageUrlToRenderForImage);
          }
          if (dataUri) {
            const framePadding = 15;
            const textCaptionAreaHeight = Math.max(elHeight * 0.15, 30);
            const textMarginTop = 10;
            const imageAreaX = elX + framePadding;
            const imageAreaY = elY + framePadding;
            const imageAreaWidth = elWidth - (2 * framePadding);
            const imageAreaHeight = elHeight - framePadding - textCaptionAreaHeight - textMarginTop - framePadding;

            itemSvg += `<rect x="${elX}" y="${elY}" width="${elWidth}" height="${elHeight}" fill="#ffffff" filter="url(#polaroidShadowFilter)" rx="3" ry="3" />`;

            if (imageAreaWidth > 0 && imageAreaHeight > 0) {
              itemSvg += `<image href="${escapeXml(dataUri)}" x="${imageAreaX}" y="${imageAreaY}" width="${imageAreaWidth}" height="${imageAreaHeight}" preserveAspectRatio="xMidYMid meet" />`;
            } else {
              itemSvg += `<image href="${escapeXml(dataUri)}" x="${elX + 5}" y="${elY + 5}" width="${elWidth - 10}" height="${elHeight - 10}" preserveAspectRatio="xMidYMid meet" />`;
            }

            if (item.content) {
              const captionAreaX = elX + framePadding;
              const captionAreaY = imageAreaY + imageAreaHeight + textMarginTop;
              const captionAreaWidth = imageAreaWidth;
              const initialCaptionFontSize = Math.max(8, Math.min(textCaptionAreaHeight * 0.7, captionAreaWidth / (item.content.length * 0.5 || 1), 30));
              const maxCaptionFontSize = Math.max(12, textCaptionAreaHeight * 0.8, 40);
              const { lines, fontSize } = wrapText( item.content, captionAreaWidth - 10, initialCaptionFontSize, 1.2, undefined, 8, maxCaptionFontSize, textCaptionAreaHeight - 5 );
              const totalTextHeight = lines.length * fontSize * 1.2;
              const textVerticalPadding = Math.max(0, (textCaptionAreaHeight - totalTextHeight) / 2);
              const textRenderStartY = captionAreaY + textVerticalPadding + (fontSize / 2);
              itemSvg += `<g transform="translate(${captionAreaX + captionAreaWidth / 2}, 0)">`;
              lines.forEach((line, index) => {
                itemSvg += `<text x="0" y="${textRenderStartY + index * fontSize * 1.2}" font-family="'DryWhiteboardMarker', 'Comic Sans MS', cursive" font-size="${fontSize}px" fill="#000000" text-anchor="middle" dominant-baseline="middle">${escapeXml(line)}</text>`;
              });
              itemSvg += `</g>`;
            }
          }
        }
      } else { // Handles 'image-invisible' due to isImageNode guard
        console.log(`[SVG Generator] Item ID: ${item.id} - Rendering as 'image-invisible'.`); // ACTIVE LOG
        const imageUrlToRenderForInvisibleImage = item.imageUrl || item.file_url;
        console.log(`[SVG Image-Invisible Log] Attempting for ID: ${item.id}, URL: ${imageUrlToRenderForInvisibleImage}`); // ACTIVE LOG
        if (imageUrlToRenderForInvisibleImage) {
          let dataUri = imageUrlToRenderForInvisibleImage;
          if (options.embedImages && !imageUrlToRenderForInvisibleImage.startsWith('data:')) {
            dataUri = await imageToDataURI(imageUrlToRenderForInvisibleImage);
          }
          console.log(`[SVG Image-Invisible Log] ID: ${item.id}, Data URI obtained: ${dataUri ? 'Yes' : 'No'}, Length: ${dataUri?.length || 0}`); // ACTIVE LOG
          if (dataUri) {
            itemSvg += `<image href="${escapeXml(dataUri)}" x="${elX}" y="${elY}" width="${elWidth}" height="${elHeight}" preserveAspectRatio="xMidYMid meet" />`;
          } else {
            console.warn(`[SVG Image-Invisible Log] No Data URI for ID: ${item.id}, URL: ${imageUrlToRenderForInvisibleImage}. Placeholder rendered.`); // ACTIVE LOG
            itemSvg += `<rect x="${elX}" y="${elY}" width="${elWidth}" height="${elHeight}" fill="#f0f0f0" stroke="#cccccc" stroke-width="1" />`;
            itemSvg += `<text x="${elX + elWidth/2}" y="${elY + elHeight/2}" text-anchor="middle" dominant-baseline="middle" font-family="sans-serif" font-size="10px" fill="#888888">Inv. Img Missing: ${item.id.substring(0,8)}</text>`;
          }
        }
      }
    } else if (item.type === 'evidence') {
      // Placeholder for evidence item rendering if needed in SVG
      // For now, just a simple representation or skip
      itemSvg += `<rect x="${elX}" y="${elY}" width="${elWidth}" height="${elHeight}" fill="#cccccc" stroke="#888888" stroke-width="1" rx="3" ry="3" />`;
      itemSvg += `<text x="${elX + 5}" y="${elY + 20}" font-family="sans-serif" font-size="12px" fill="#000000">Evidence: ${escapeXml(item.content || item.title || 'Untitled')}</text>`;
    }
    svgElementsHtml += itemSvg;
  }

  connections.forEach(conn => {
    const fromItem = board.elements.find(el => el.id === conn.fromId) as BoardItem | undefined;
    const toItem = board.elements.find(el => el.id === conn.toId) as BoardItem | undefined;

    if (fromItem && toItem && fromItem.type !== 'text' && toItem.type !== 'text') {
      const fromW = fromItem.width ?? 250;
      const fromH = fromItem.height ?? 100;
      const toW = toItem.width ?? 250;
      const toH = toItem.height ?? 100;
      const startX = fromItem.position.x + fromW / 2;
      const startY = fromItem.position.y + 20;
      const endX = toItem.position.x + toW / 2;
      const endY = toItem.position.y + 20;
      const midX = (startX + endX) / 2;
      const dx = endX - startX;
      const dy = endY - startY;
      const distance = Math.sqrt(dx * dx + dy * dy);
      const minSag = 75;
      const maxSag = 150;
      const sagScale = 0.2;
      const sagAmount = minSag + (maxSag - minSag) * (1 - Math.exp(-sagScale * distance / 500));
      const midY = (startY + endY) / 2 + sagAmount;
      const pathData = `M ${startX} ${startY} Q ${midX} ${midY} ${endX} ${endY}`;
      svgElementsHtml += `<path d="${pathData}" stroke="${connectionYarnColor}" stroke-width="3" fill="none" filter="url(#yarnWithShadow)" stroke-linecap="round" />`;
      const pinRadius = 6;
      const pinColor = '#B22222';
      const pinStrokeColor = '#990000';
      const pinTopColor = '#ffcccc';
      svgElementsHtml += `<g filter="url(#pinDropShadow)">`;
      svgElementsHtml += `<circle cx="${startX}" cy="${startY}" r="${pinRadius}" fill="${pinColor}" stroke="${pinStrokeColor}" stroke-width="1" />`;
      svgElementsHtml += `<circle cx="${startX}" cy="${startY - 2}" r="2" fill="${pinTopColor}" stroke="${pinStrokeColor}" stroke-width="0.5" />`;
      svgElementsHtml += `</g>`;
      svgElementsHtml += `<g filter="url(#pinDropShadow)">`;
      svgElementsHtml += `<circle cx="${endX}" cy="${endY}" r="${pinRadius}" fill="${pinColor}" stroke="${pinStrokeColor}" stroke-width="1" />`;
      svgElementsHtml += `<circle cx="${endX}" cy="${endY - 2}" r="2" fill="${pinTopColor}" stroke="${pinStrokeColor}" stroke-width="0.5" />`;
      svgElementsHtml += `</g>`;
    }
  });

  (board.strokes as PenStroke[])?.forEach(stroke => {
    if (!stroke.points || stroke.points.length < 1) return;
    if (stroke.points.length === 1) {
        const p = stroke.points[0];
        const radius = (stroke.strokeWidth || 2) / 2;
        svgElementsHtml += `<circle cx="${p.x}" cy="${p.y}" r="${radius}" fill="${stroke.color || DEFAULT_TEXT_COLOR}" />`;
    } else {
        const pathData = "M " + stroke.points.map((p: Position) => `${p.x.toFixed(2)} ${p.y.toFixed(2)}`).join(" L ");
        svgElementsHtml += `<path d="${pathData}" stroke="${stroke.color || DEFAULT_TEXT_COLOR}" stroke-width="${stroke.strokeWidth || 2}" fill="none" stroke-linecap="round" stroke-linejoin="round" />`;
    }
  });

  return `<svg width="${finalSvgWidth}" height="${finalSvgHeight}" viewBox="${viewBoxX} ${viewBoxY} ${viewBoxWidth} ${viewBoxHeight}" xmlns="http://www.w3.org/2000/svg"><defs>${svgDefs}</defs>${svgElementsHtml}</svg>`;
} 