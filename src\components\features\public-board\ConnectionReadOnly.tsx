import React, { useState, useEffect, useMemo } from 'react';
import { motion } from '../../../utils/MotionWrapper'; // Adjust path if needed

// Simplified props for read-only version
interface ConnectionReadOnlyProps {
  id: string;
  from: { id: string; position: { x: number; y: number }; width?: number; height?: number };
  to: { id: string; position: { x: number; y: number }; width?: number; height?: number };
  simplificationLevel?: number; // Added prop
}

const ConnectionReadOnly: React.FC<ConnectionReadOnlyProps> = ({ 
  id, 
  from, 
  to,
  simplificationLevel = 0 // Default to 0 if not provided
}) => {

  const [pathData, setPathData] = useState('');
  
  // Use static positions from props
  const fromPosition = from.position;
  const toPosition = to.position;
  
  // Calculate connection points using center of elements
  const fromW = from.width ?? 250; // Default width if not provided
  const fromH = from.height ?? 250; // Default height if not provided
  const toW = to.width ?? 250;     // Default width if not provided
  const toH = to.height ?? 250;     // Default height if not provided

  const startX = fromPosition.x + fromW / 2;
  const startY = fromPosition.y + 20; // Revert to 20px from the top
  
  const endX = toPosition.x + toW / 2;     
  const endY = toPosition.y + 20;   // Revert to 20px from the top

  // Generate simple curved path (match original Connection logic)
  useEffect(() => {
    // Calculate the midpoint with a sag effect
    const midX = (startX + endX) / 2;
    const dx = endX - startX;
    const dy = endY - startY;
    const distance = Math.sqrt(dx * dx + dy * dy);
    const minSag = 75;
    const maxSag = 150;
    const sagScale = 0.2;
    const sagAmount = minSag + (maxSag - minSag) * (1 - Math.exp(-sagScale * distance / 500));
    const midY = (startY + endY) / 2 + sagAmount;
    
    // Create a quadratic B\u00e9zier path
    const newPathData = `M ${startX} ${startY} Q ${midX} ${midY} ${endX} ${endY}`;
    
    // Basic validation
    if (isNaN(startX) || isNaN(startY) || isNaN(endX) || isNaN(endY) || isNaN(midX) || isNaN(midY)) {
      console.error(`[ConnectionReadOnly ${id}] ERROR: NaN detected in calculated points!`);
      setPathData(''); // Set empty path on error
    } else {
      setPathData(newPathData);
    }
  }, [id, startX, startY, endX, endY]);

  // Return null if pathData is invalid or empty
  if (!pathData) {
    return null;
  }

  // Determine styles based on simplification level
  let strokeWidth = 3;
  let showPins = true; // Always show pins
  let filterValue = "url(#yarnWithShadowReadOnly)";
  
  // Pin sizes and details based on simplification level
  const pinHeadRadius = simplificationLevel === 0 ? 6 : simplificationLevel === 1 ? 5 : 4;
  const pinTopRadius = simplificationLevel === 0 ? 2 : simplificationLevel === 1 ? 1.5 : 1;
  const pinStrokeWidth = simplificationLevel === 0 ? 1 : simplificationLevel === 1 ? 0.75 : 0.5;
  const showPinTop = simplificationLevel < 2; // Only show pin top detail for lower simplification levels

  if (simplificationLevel === 1) {
    strokeWidth = 2;
    filterValue = "url(#yarnTextureReadOnly)"; // Simpler filter
  } else if (simplificationLevel === 2) {
    strokeWidth = 1;
    filterValue = "none"; // No filter for max simplification
  }

  return (
    <div 
      className="absolute top-0 left-0 w-full h-full" 
      style={{ overflow: 'visible', zIndex: 10, pointerEvents: 'none' }} // Set pointerEvents to none
    >
      <svg 
        width="100%" 
        height="100%" 
        className="absolute top-0 left-0"
        style={{ overflow: 'visible' }}
      >
        {/* Define the yarn texture filters (copied from Connection.tsx) */}
        <defs>
          <filter id="yarnTextureReadOnly"> {/* Use unique ID */}
            <feTurbulence type="fractalNoise" baseFrequency="0.8" numOctaves="2" result="noise" />
            <feDisplacementMap in="SourceGraphic" in2="noise" scale="2" />
          </filter>
          <filter id="yarnWithShadowReadOnly"> {/* Use unique ID */}
            <feTurbulence type="fractalNoise" baseFrequency="0.8" numOctaves="2" result="noise" />
            <feDisplacementMap in="SourceGraphic" in2="noise" scale="2" result="textured" />
            <feDropShadow dx="3" dy="4" stdDeviation="3.5" floodColor="rgba(0, 0, 0, 0.5)" />
          </filter>
        </defs>
        
        {/* Main connection path - use fixed styles */}
        <motion.path
          d={pathData}
          initial={{ opacity: 0, pathLength: 0, strokeWidth: 0 }}
          animate={{
            opacity: 1,
            pathLength: 1,
            stroke: '#C0392B', // Fixed color
            strokeWidth: strokeWidth,    // Use dynamic width
          }}
          transition={{ duration: 0.4 }}
          style={{
            strokeLinecap: 'round',
            fill: 'none',
            filter: filterValue
          }}
        />
        
        {/* Start point pin */}
        {showPins && (
          <g>
            <motion.circle
              cx={startX}
              cy={startY}
              r={pinHeadRadius}
              initial={{ opacity: 0, strokeWidth: 0 }}
              animate={{ 
                opacity: 1,
                fill: '#B22222',
                stroke: '#990000',
                strokeWidth: pinStrokeWidth
              }}
              transition={{ duration: 0.2 }}
              style={{ filter: simplificationLevel < 2 ? 'drop-shadow(0px 2px 2px rgba(0,0,0,0.5))' : 'none' }}
            />
            {showPinTop && (
              <motion.circle
                cx={startX}
                cy={startY - 2}
                r={pinTopRadius}
                initial={{ opacity: 0, strokeWidth: 0 }}
                animate={{ 
                  opacity: 1,
                  fill: '#ffcccc',
                  stroke: '#990000',
                  strokeWidth: pinStrokeWidth / 2
                }}
                transition={{ duration: 0.2 }}
              />
            )}
          </g>
        )}
        
        {/* End point pin */}
        {showPins && (
          <g>
            <motion.circle
              cx={endX}
              cy={endY}
              r={pinHeadRadius}
              initial={{ opacity: 0, strokeWidth: 0 }}
              animate={{ 
                opacity: 1,
                fill: '#B22222',
                stroke: '#990000',
                strokeWidth: pinStrokeWidth 
              }}
              transition={{ duration: 0.2 }}
              style={{ filter: simplificationLevel < 2 ? 'drop-shadow(0px 2px 2px rgba(0,0,0,0.5))' : 'none' }}
            />
            {showPinTop && (
              <motion.circle
                cx={endX}
                cy={endY - 2}
                r={pinTopRadius}
                initial={{ opacity: 0, strokeWidth: 0 }}
                animate={{ 
                  opacity: 1,
                  fill: '#ffcccc',
                  stroke: '#990000',
                  strokeWidth: pinStrokeWidth / 2
                }}
                transition={{ duration: 0.2 }}
              />
            )}
          </g>
        )}
      </svg>
    </div>
  );
};

export default ConnectionReadOnly; 