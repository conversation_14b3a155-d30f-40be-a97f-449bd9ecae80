// Types for the new structured AI messaging system
export interface ToolCall {
  id: string;
  name: string;
  arguments: Record<string, any>;
}

export interface UserMessage {
  role: 'user';
  content: string;
}

export interface AssistantContentMessage {
  role: 'assistant';
  content: string;
}

export interface AssistantToolCallMessage {
  role: 'assistant';
  tool_call: ToolCall;
}

export interface ToolResponseMessage {
  role: 'tool';
  tool_call_id: string;
  name: string;
  content: string; // JSON string of tool result
}

export interface SystemMessage {
  role: 'system';
  content: string;
}

export type AIMessage = 
  | UserMessage
  | AssistantContentMessage
  | AssistantToolCallMessage
  | ToolResponseMessage
  | SystemMessage;

// JSON Schema for structured AI responses
export const AI_RESPONSE_SCHEMA = {
  type: "object",
  properties: {
    type: {
      type: "string",
      enum: ["content", "tool_call"]
    },
    content: {
      type: "string",
      description: "The content to display to the user"
    },
    finished: {
      type: "boolean",
      description: "Whether this response completes the conversation (true) or should continue (false). Only used with content type."
    },
    tool_call: {
      type: "object",
      properties: {
        id: {
          type: "string",
          description: "Unique identifier for the tool call"
        },
        name: {
          type: "string",
          description: "Name of the tool to call"
        },
        arguments: {
          type: "object",
          description: "Arguments to pass to the tool"
        }
      },
      required: ["id", "name", "arguments"]
    }
  },
  required: ["type"],
  oneOf: [
    {
      properties: {
        type: { const: "content" },
        content: { type: "string" },
        finished: { type: "boolean" }
      },
      required: ["type", "content"]
    },
    {
      properties: {
        type: { const: "tool_call" },
        tool_call: { type: "object" }
      },
      required: ["type", "tool_call"]
    }
  ]
} as const;

// Response format for structured AI responses
export interface StructuredAIResponse {
  type: 'content' | 'tool_call';
  content?: string;
  finished?: boolean; // Only for content responses - indicates if conversation should end
  tool_call?: ToolCall;
}

// Tool execution result
export interface ToolResult {
  success: boolean;
  data?: any;
  error?: string;
}

// Conversation state for managing multi-turn tool calls
export interface ConversationState {
  messages: AIMessage[];
  pendingToolCalls: ToolCall[];
  awaitingToolResults: boolean;
} 