'use client';

import { useState } from 'react';

export type ValidationFunction<T> = (value: T) => string | undefined;

export interface FieldValidation<T extends Record<string, any>> {
  [key: string]: ValidationFunction<T[keyof T]>;
}

export interface FormErrors<T> {
  [key: string]: string | undefined;
}

/**
 * Custom hook for form validation
 */
export function useFormValidation<T extends Record<string, any>>(
  initialValues: T,
  validations: FieldValidation<T>
) {
  const [values, setValues] = useState<T>(initialValues);
  const [errors, setErrors] = useState<FormErrors<T>>({});
  const [touched, setTouched] = useState<Record<keyof T, boolean>>({} as Record<keyof T, boolean>);

  /**
   * Update a specific field value
   */
  const handleChange = (field: keyof T, value: T[keyof T]) => {
    setValues((prev) => ({ ...prev, [field]: value }));
    
    // Clear error when field is changed
    if (errors[field as string]) {
      setErrors((prev) => ({ ...prev, [field]: undefined }));
    }
    
    // Mark field as touched
    if (!touched[field]) {
      setTouched((prev) => ({ ...prev, [field]: true }));
    }
  };

  /**
   * Mark a field as touched (typically on blur)
   */
  const handleBlur = (field: keyof T) => {
    if (!touched[field]) {
      setTouched((prev) => ({ ...prev, [field]: true }));
    }
    
    // Validate the field on blur
    if (validations[field as string]) {
      const error = validations[field as string](values[field]);
      if (error) {
        setErrors((prev) => ({ ...prev, [field]: error }));
      }
    }
  };

  /**
   * Reset the form to initial values
   */
  const resetForm = () => {
    setValues(initialValues);
    setErrors({});
    setTouched({} as Record<keyof T, boolean>);
  };

  /**
   * Set a general error not tied to a specific field
   */
  const setGeneralError = (message: string) => {
    setErrors((prev) => ({ ...prev, general: message }));
  };

  /**
   * Validate all fields and return whether the form is valid
   */
  const validateForm = (): boolean => {
    const newErrors: FormErrors<T> = {};
    let isValid = true;

    // Check all validations
    Object.keys(validations).forEach((key) => {
      const field = key as keyof T;
      const validationFn = validations[key];
      
      if (validationFn) {
        const error = validationFn(values[field]);
        if (error) {
          newErrors[key] = error;
          isValid = false;
        }
      }
    });

    setErrors(newErrors);
    
    // Mark all fields as touched if form is invalid
    if (!isValid) {
      const allTouched = Object.keys(values).reduce((acc, key) => {
        acc[key as keyof T] = true;
        return acc;
      }, {} as Record<keyof T, boolean>);
      
      setTouched(allTouched);
    }

    return isValid;
  };

  return {
    values,
    errors,
    touched,
    handleChange,
    handleBlur,
    validateForm,
    resetForm,
    setGeneralError,
  };
} 