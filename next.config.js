/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'ngoqknggspzugiuyxlcw.supabase.co',
        port: '',
        pathname: '/storage/v1/object/**', // Allows any path under the storage object URL
      },
      // Add any other existing patterns here if necessary
    ],
    dangerouslyAllowSVG: true,
    contentDispositionType: 'attachment',
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
    // Add any other existing image configurations here if necessary
  },
};

module.exports = nextConfig; 