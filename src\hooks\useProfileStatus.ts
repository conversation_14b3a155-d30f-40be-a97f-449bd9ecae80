import { useState, useEffect, useCallback } from 'react';
import { ProfileCache } from '@/utils/profileCache';
import type { UserProfile } from '@/types/auth';

interface UseProfileStatusReturn {
  isProfileComplete: boolean | null;
  profile: UserProfile | null;
  isLoading: boolean;
  error: string | null;
  refetchProfile: () => Promise<void>;
  clearCache: () => void;
}

/**
 * Hook to manage profile status with intelligent caching
 * Reduces redundant API calls by using cached data when available
 */
export function useProfileStatus(userId: string | null): UseProfileStatusReturn {
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchProfile = useCallback(async (force = false) => {
    if (!userId) {
      setProfile(null);
      setError(null);
      return;
    }

    // Check cache first unless forced
    if (!force) {
      const cachedProfile = ProfileCache.getCachedProfile(userId);
      if (cachedProfile) {
        setProfile(cachedProfile);
        setError(null);
        return;
      }

      // Check completion status cache
      const cachedStatus = ProfileCache.isProfileComplete(userId);
      if (cachedStatus === true) {
        // We know it's complete, but don't have the full profile
        // This is still better than making an API call
        setError(null);
        return;
      }
    }

    setIsLoading(true);
    setError(null);

    try {
      const fetchedProfile = await ProfileCache.fetchProfile(userId);
      setProfile(fetchedProfile);
    } catch (err: any) {
      console.error('Error fetching profile:', err);
      setError(err.message || 'Failed to fetch profile');
      setProfile(null);
    } finally {
      setIsLoading(false);
    }
  }, [userId]);

  const refetchProfile = useCallback(async () => {
    await fetchProfile(true);
  }, [fetchProfile]);

  const clearCache = useCallback(() => {
    if (userId) {
      ProfileCache.clearUserCache(userId);
    }
  }, [userId]);

  // Initial fetch when userId changes
  useEffect(() => {
    fetchProfile();
  }, [fetchProfile]);

  const isProfileComplete = profile 
    ? !!(profile.username && profile.is_verified)
    : null;

  return {
    isProfileComplete,
    profile,
    isLoading,
    error,
    refetchProfile,
    clearCache
  };
}

/**
 * Lightweight hook to just check if profile is complete
 * Uses cached data only, no API calls
 */
export function useProfileCompletionStatus(userId: string | null): boolean | null {
  const [isComplete, setIsComplete] = useState<boolean | null>(null);

  useEffect(() => {
    if (!userId) {
      setIsComplete(null);
      return;
    }

    // Check cached profile first
    const cachedProfile = ProfileCache.getCachedProfile(userId);
    if (cachedProfile) {
      setIsComplete(!!(cachedProfile.username && cachedProfile.is_verified));
      return;
    }

    // Check cached completion status
    const cachedStatus = ProfileCache.isProfileComplete(userId);
    setIsComplete(cachedStatus);
  }, [userId]);

  return isComplete;
}

/**
 * Hook to determine if a user needs to complete their profile setup
 * Specifically designed for Google OAuth users who need to choose a username
 */
export function useProfileSetupRequired(
  userId: string | null, 
  userEmail: string | null
): { 
  isSetupRequired: boolean | null; 
  shouldShowModal: boolean;
  skipCheck: () => void;
} {
  const [isSetupRequired, setIsSetupRequired] = useState<boolean | null>(null);
  const [hasSkipped, setHasSkipped] = useState(false);

  useEffect(() => {
    if (!userId || !userEmail) {
      setIsSetupRequired(null);
      return;
    }

    // Check if this is likely a Google OAuth user (no password set)
    // This is a heuristic - you might want to store this info more explicitly
    const isLikelyOAuthUser = userEmail.includes('@gmail.com') || 
                              userEmail.includes('@googlemail.com');

    if (!isLikelyOAuthUser) {
      setIsSetupRequired(false);
      return;
    }

    // Check cached completion status first
    const cachedStatus = ProfileCache.isProfileComplete(userId);
    if (cachedStatus === true) {
      setIsSetupRequired(false);
      return;
    }

    // Check cached profile
    const cachedProfile = ProfileCache.getCachedProfile(userId);
    if (cachedProfile) {
      const isComplete = !!(cachedProfile.username && cachedProfile.is_verified);
      setIsSetupRequired(!isComplete);
      return;
    }

    // If we don't have cached data, we need to check
    setIsSetupRequired(true);
  }, [userId, userEmail]);

  const skipCheck = useCallback(() => {
    setHasSkipped(true);
  }, []);

  const shouldShowModal = isSetupRequired === true && !hasSkipped;

  return {
    isSetupRequired,
    shouldShowModal,
    skipCheck
  };
}
