'use client';

import React, { useMemo } from 'react';
import { useBatchSignedImageUrls } from '@/hooks/useBatchSignedImageUrls';
import OptimizedImageNode from './OptimizedImageNode';

interface Element {
  id: string;
  type: string;
  title: string;
  content: string;
  position: { x: number; y: number };
  url?: string;
  imageUrl?: string;
  alt?: string;
  color?: string;
  isAiGenerated?: boolean;
  width?: number;
  height?: number;
}

interface BatchImageLoaderProps {
  boardId: string;
  elements: Element[];
  scale?: number;
}

/**
 * Component to efficiently load all images for a public board in a single batch request
 * This improves performance by reducing the number of network requests
 */
const BatchImageLoader: React.FC<BatchImageLoaderProps> = ({
  boardId,
  elements,
  scale = 1,
}) => {
  // Extract all image URLs from elements
  const imageUrls = useMemo(() => {
    const urls: string[] = [];
    
    elements.forEach(element => {
      if ((element.type === 'image' || element.type === 'image-invisible') && element.imageUrl) {
        urls.push(element.imageUrl);
      } else if (element.type === 'article' && element.imageUrl) {
        urls.push(element.imageUrl);
      }
    });
    
    return urls;
  }, [elements]);
  
  // Fetch all signed URLs in a single batch
  const { signedUrls, isLoading } = useBatchSignedImageUrls(boardId, imageUrls);
  
  // Only render image elements here
  const imageElements = useMemo(() => {
    return elements.filter(el => 
      (el.type === 'image' || el.type === 'image-invisible' || 
      (el.type === 'article' && el.imageUrl))
    );
  }, [elements]);
  
  // Add basic loading skeleton during initial load
  if (isLoading && imageElements.length > 0) {
    return (
      <>
        {imageElements.map((element, index) => (
          <OptimizedImageNode
            key={element.id}
            id={element.id}
            boardId={boardId}
            content={element.content}
            position={element.position}
            scale={scale}
            width={element.width || 250}
            height={element.height || 250}
            imageUrl={element.imageUrl}
            caption={element.title}
            alt={element.alt}
            isPlaceholder={true}
          />
        ))}
      </>
    );
  }
  
  // Render all images with signed URLs
  return (
    <>
      {imageElements.map((element, index) => (
        <OptimizedImageNode
          key={element.id}
          id={element.id}
          boardId={boardId}
          content={element.content}
          position={element.position}
          scale={scale}
          width={element.width || 250}
          height={element.height || 250}
          imageUrl={element.imageUrl}
          caption={element.title}
          alt={element.alt}
          signedUrls={signedUrls}
          priority={index < 3} // Only prioritize first 3 images
          lazy={index >= 3}    // Lazy load the rest
        />
      ))}
    </>
  );
};

export default BatchImageLoader; 