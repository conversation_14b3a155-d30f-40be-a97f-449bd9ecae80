import React, { useState, useEffect, useRef } from 'react';
import { motion } from '../../../../utils/MotionWrapper';
import { useBoard } from '../../../../context/BoardContext';

interface ConnectionInProgressProps {
  startItem: {
    id: string;
    position: { x: number; y: number };
  };
  scale: number;
  boardPosition: { x: number; y: number };
}

/**
 * Shows a connection string from the first selected item to the cursor
 */
const ConnectionInProgress: React.FC<ConnectionInProgressProps> = ({
  startItem,
  scale,
  boardPosition
}) => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [pathData, setPathData] = useState('');
  const [isOverTextElement, setIsOverTextElement] = useState(false);
  const [hasValidMousePosition, setHasValidMousePosition] = useState(false);
  
  // Access board to check element types
  const { board } = useBoard();

  // Initialize mouse position on component mount
  useEffect(() => {
    // Get current mouse position
    const initializeMousePosition = (e: MouseEvent) => {
      const x = (e.clientX - boardPosition.x) / scale;
      const y = (e.clientY - boardPosition.y) / scale;
      setMousePosition({ x, y });
      setHasValidMousePosition(true);
      // Once we have the position, we no longer need this listener
      window.removeEventListener('mousemove', initializeMousePosition);
    };
    
    // Use a one-time listener to get the initial position
    window.addEventListener('mousemove', initializeMousePosition, { once: true });
    
    return () => {
      window.removeEventListener('mousemove', initializeMousePosition);
    };
  }, [scale, boardPosition]);

  // Track mouse position and detect elements under cursor
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      // Convert mouse position to board coordinates
      const x = (e.clientX - boardPosition.x) / scale;
      const y = (e.clientY - boardPosition.y) / scale;
      setMousePosition({ x, y });
      setHasValidMousePosition(true);
      
      // Check if over any text element for connection visualization
      let isOverText = false;
      
      // Check if over starting element
      const startX = startItem.position.x;
      const startY = startItem.position.y;
      const elementWidth = 250; // Assuming standard width
      const elementHeight = 120; // Assuming standard height
      
      // Check if over any board element
      for (const element of board.elements) {
        const itemX = element.position.x;
        const itemY = element.position.y;
        
        if (
          x >= itemX && 
          x <= itemX + elementWidth && 
          y >= itemY && 
          y <= itemY + elementHeight
        ) {
          if (element.type === 'text') {
            isOverText = true;
          }
          break;
        }
      }
      
      setIsOverTextElement(isOverText);
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
    };
  }, [scale, boardPosition, board.elements, startItem]);

  // Calculate connection points and generate path
  useEffect(() => {
    if (!startItem || !hasValidMousePosition) return;

    // Start point is at the top center of the item
    const startX = startItem.position.x + 125; // Half the element width (assuming 250px width)
    const startY = startItem.position.y + 20;  // 20px from the top of the element
    
    // End point is the mouse position
    const endX = mousePosition.x;
    const endY = mousePosition.y;
    
    // Calculate the midpoint with a sag effect
    const midX = (startX + endX) / 2;
    
    // Calculate distance for proportional sag
    const dx = endX - startX;
    const dy = endY - startY;
    const distance = Math.sqrt(dx * dx + dy * dy);
    
    // Scale sag based on distance
    const minSag = 50;
    const maxSag = 120;
    const sagScale = 0.2;
    
    // Calculate sag with a formula that starts at minSag and approaches maxSag
    const sagAmount = minSag + (maxSag - minSag) * (1 - Math.exp(-sagScale * distance / 500));
    
    // Determine if the sag should be up or down based on direction
    // If the line is mostly horizontal, make the sag up (negative)
    // If the line is mostly vertical, make the sag to the right or left
    let midY, midXOffset = 0;
    
    if (Math.abs(dx) > Math.abs(dy)) {
      // Horizontal line - sag upward
      midY = Math.min(startY, endY) - sagAmount;
    } else {
      // Vertical line - sag sideways a bit
      midY = (startY + endY) / 2;
      midXOffset = dx > 0 ? sagAmount : -sagAmount;
    }
    
    // Create a quadratic Bézier path
    const pathData = `M ${startX} ${startY} Q ${midX + midXOffset} ${midY} ${endX} ${endY}`;
    
    setPathData(pathData);
  }, [startItem, mousePosition, hasValidMousePosition]);

  // Don't render anything until we have all the necessary data
  if (!startItem || !pathData || !hasValidMousePosition) {
    return null;
  }

  // Start position for the pin (top center of the item)
  const startX = startItem.position.x + 125;
  const startY = startItem.position.y + 20;

  return (
    <div 
      className="absolute top-0 left-0 w-full h-full"
      style={{ overflow: 'visible', zIndex: 20 }} // Higher z-index to appear above elements
    >
      <svg 
        width="100%" 
        height="100%" 
        className="absolute top-0 left-0"
        style={{ overflow: 'visible', pointerEvents: 'none' }}
      >
        {/* Define the yarn texture filter */}
        <defs>
          <filter id="yarnTexture">
            <feTurbulence type="fractalNoise" baseFrequency="0.8" numOctaves="2" result="noise" />
            <feDisplacementMap in="SourceGraphic" in2="noise" scale="2" />
          </filter>
          
          <filter id="yarnWithShadow">
            <feTurbulence type="fractalNoise" baseFrequency="0.8" numOctaves="2" result="noise" />
            <feDisplacementMap in="SourceGraphic" in2="noise" scale="2" result="textured" />
            <feDropShadow dx="3" dy="4" stdDeviation="3.5" floodColor="rgba(0, 0, 0, 0.5)" />
          </filter>
        </defs>
        
        {/* Main connection path - yarn-like appearance - only show if we have valid mouse position */}
        {hasValidMousePosition && (
          <motion.path
            d={pathData}
            initial={{ 
              opacity: 0, 
              pathLength: 0,
              strokeWidth: 3 // Add explicit initial strokeWidth
            }}
            animate={{ 
              opacity: 1,
              pathLength: 1,
              stroke: '#C0392B',
              strokeWidth: 3,
            }}
            transition={{ duration: 0.2 }}
            style={{
              strokeLinecap: 'round',
              fill: 'none',
              filter: 'url(#yarnWithShadow)'
            }}
          />
        )}
        
        {/* Start point pin (small red circle with shadow) */}
        <g pointerEvents="none">
          {/* Pin head */}
          <motion.circle
            cx={startX}
            cy={startY}
            r={8}
            initial={{ 
              opacity: 0,
              strokeWidth: 1.5 // Add explicit initial strokeWidth
            }}
            animate={{ 
              opacity: 1,
              fill: '#B22222',
              stroke: '#990000',
              strokeWidth: 1.5
            }}
            transition={{ duration: 0.2 }}
            style={{ filter: 'drop-shadow(1px 3px 3px rgba(0,0,0,0.7))' }}
          />
          
          {/* Pin top */}
          <motion.circle
            cx={startX}
            cy={startY - 3}
            r={3}
            initial={{ 
              opacity: 0,
              strokeWidth: 0.5 // Add explicit initial strokeWidth
            }}
            animate={{ 
              opacity: 1,
              fill: '#ffcccc',
              stroke: '#990000',
              strokeWidth: 0.5
            }}
            transition={{ duration: 0.2 }}
          />
        </g>
        
        {/* Pin at cursor position - only show when not over text element and we have valid mouse position */}
        {hasValidMousePosition && (
          <g pointerEvents="none">
            <motion.circle
              cx={mousePosition.x}
              cy={mousePosition.y}
              r={6}
              initial={{ 
                opacity: 0,
                strokeWidth: 1 // Add explicit initial strokeWidth
              }}
              animate={{ 
                opacity: 1,
                fill: '#B22222',
                stroke: '#990000',
                strokeWidth: 1
              }}
              transition={{ duration: 0.2 }}
              style={{ 
                filter: 'drop-shadow(1px 3px 3px rgba(0,0,0,0.7))',
                animation: 'pulse 1.5s infinite'
              }}
            />
          </g>
        )}
        
        {/* Add CSS animation for the pulse effect */}
        <style>
          {`
            @keyframes pulse {
              0% { r: 6; }
              50% { r: 8; }
              100% { r: 6; }
            }
          `}
        </style>
      </svg>
    </div>
  );
};

export default ConnectionInProgress; 