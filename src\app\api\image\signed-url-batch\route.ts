import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import type { Database } from '@/lib/database.types';

import { getAuthenticatedUser } from '@/utils/authUtils';
/**
 * API route to sign multiple image URLs in a single request
 * This is used to optimize performance by reducing the number of API calls
 */
export async function POST(request: NextRequest) {
  try {
    const { user, error: authError, supabase } = await getAuthenticatedUser();
    
    // Get the request body
    const { boardId, imageUrls } = await request.json();
    
    // Validate request parameters
    if (!boardId || !Array.isArray(imageUrls)) {
      return NextResponse.json({ error: 'Invalid request parameters' }, { status: 400 });
    }
    
    // Check if the user has access to the board
    // For public boards, we'll still verify that the board is actually public
    const { data: boardAccess, error: boardAccessError } = await supabase
      .from('board_sharing')
      .select('public_board')
      .eq('board_id', boardId)
      .eq('public_board', true)
      .single();
    
    const { data: userBoardAccess, error: userBoardAccessError } = user ? await supabase
      .from('board_sharing')
      .select('board_id, user_id')
      .eq('board_id', boardId)
      .eq('user_id', user.id)
      .maybeSingle() : { data: null, error: null };
    
    const hasAccess = !!boardAccess || !!userBoardAccess;
    
    if (!hasAccess) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }
    
    // Helper to extract relative path from URL
    function extractRelativePath(url: string | null): string | null {
      if (!url) return null;
      
      try {
        // Check if it's already a relative path
        if (!url.startsWith('http')) {
          return url.includes('/') ? url : null;
        }
        
        // Parse URL and extract path components
        const parsedUrl = new URL(url);
        const pathSegments = parsedUrl.pathname.split('/');
        
        // Find the bucket name ('images')
        const bucketName = 'images';
        const bucketIndex = pathSegments.indexOf(bucketName);
        
        if (bucketIndex !== -1 && bucketIndex < pathSegments.length - 1) {
          // Join the segments after the bucket name
          return pathSegments.slice(bucketIndex + 1).join('/');
        }
        
        return null;
      } catch (error) {
        console.error(`Error parsing URL: ${url}`, error);
        return null;
      }
    }
    
    // Process all image URLs
    const signedUrls: Record<string, string> = {};
    
    await Promise.all(imageUrls.map(async (url: string) => {
      if (!url) return;
      
      const relativePath = extractRelativePath(url);
      if (!relativePath) return;
      
      try {
        // Generate a signed URL with a longer expiration (15 minutes)
        const { data } = await supabase.storage
          .from('images')
          .createSignedUrl(relativePath, 60 * 15);
        
        if (data?.signedUrl) {
          signedUrls[relativePath] = data.signedUrl;
        }
      } catch (error) {
        console.error(`Error signing URL for ${relativePath}:`, error);
      }
    }));
    
    // Respond with all signed URLs
    const response = NextResponse.json({ signedUrls });
    
    // Cache control - private since these are signed URLs
    response.headers.set('Cache-Control', 'private, max-age=600');
    
    return response;
  } catch (error) {
    console.error('Error in batch signed URL generation:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
} 