---
description: overview of context folder
globs: 
alwaysApply: false
---
# React Context Guide

This directory holds React Context providers and hooks for managing global or shared application state.

- [index.ts](mdc:src/context/index.ts): A barrel file that re-exports the contexts for easier importing throughout the application.
- [AuthContext.tsx](mdc:src/context/AuthContext.tsx): Manages user authentication state using Supabase Auth. It provides user information, session details, authentication status, and functions for sign-in, sign-up, sign-out, and email verification. It integrates with Supabase's `onAuthStateChange` to keep the state synchronized.
- [BoardContext.tsx](mdc:src/context/BoardContext.tsx): This is the central context for the main detective board functionality. It aggregates several custom hooks (`useBoardState`, `useZoomAndPan`, `useConnections`, `useModalState`, `useAutoSave`, `usePenTool`) to provide a comprehensive API for interacting with the board. This includes managing board data (items, connections, strokes), view state (zoom, pan, selection), connection logic, modal dialogs, auto-saving, and pen/eraser tools.
- [DragContext.tsx](mdc:src/context/DragContext.tsx): Manages the state related to dragging items on the board. It tracks the real-time positions of items being dragged and whether an item is currently in a drag state. This allows components to access consistent drag information without prop drilling.

