import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import type { Database } from '@/lib/database.types';

import { getAuthenticatedUser } from '@/utils/authUtils';
/**
 * Get replies for a specific comment
 * GET /api/board/comment/replies?commentId=123
 */
export async function GET(request: NextRequest) {
  try {
    const { user, error: authError, supabase } = await getAuthenticatedUser();
    
    const { searchParams } = new URL(request.url);
    const commentId = searchParams.get('commentId');

    if (!commentId) {
      return NextResponse.json({ error: 'Comment ID is required' }, { status: 400 });
    }

    // Get the parent comment first to verify access rights
    const { data: parentComment, error: parentError } = await supabase
      .from('comments')
      .select('id, board_id')
      .eq('id', commentId)
      .single();

    if (parentError) {
      console.error('Error fetching parent comment:', parentError);
      return NextResponse.json({ error: 'Parent comment not found' }, { status: 404 });
    }

    // Verify board access if user is not authenticated
    if (!user) {
      const { data: sharingData, error: sharingError } = await supabase
        .from('board_sharing')
        .select('public_board')
        .eq('board_id', parentComment.board_id)
        .eq('public_board', true)
        .single();

      if (sharingError || !sharingData) {
        return NextResponse.json({ error: 'Authentication required for private boards' }, { status: 401 });
      }
    }

    // Get replies for the comment
    const { data: replies, error: repliesError } = await supabase
      .from('comments')
      .select('*')
      .eq('parent_comment_id', commentId)
      .order('created_at', { ascending: true });

    if (repliesError) {
      console.error('Error fetching comment replies:', repliesError);
      return NextResponse.json({ error: 'Failed to fetch replies' }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      replies
    });
  } catch (error) {
    console.error('Error fetching comment replies:', error);
    return NextResponse.json({ error: 'Failed to fetch replies' }, { status: 500 });
  }
} 