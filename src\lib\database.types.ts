export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export type Database = {
  // Allows to automatically instanciate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "12.2.3 (519615d)"
  }
  public: {
    Tables: {
      _prisma_migrations: {
        Row: {
          applied_steps_count: number
          checksum: string
          finished_at: string | null
          id: string
          logs: string | null
          migration_name: string
          rolled_back_at: string | null
          started_at: string
        }
        Insert: {
          applied_steps_count?: number
          checksum: string
          finished_at?: string | null
          id: string
          logs?: string | null
          migration_name: string
          rolled_back_at?: string | null
          started_at?: string
        }
        Update: {
          applied_steps_count?: number
          checksum?: string
          finished_at?: string | null
          id?: string
          logs?: string | null
          migration_name?: string
          rolled_back_at?: string | null
          started_at?: string
        }
        Relationships: []
      }
      ai_chat_messages: {
        Row: {
          board_id: string
          content: string
          created_at: string
          id: string
          raw_content: Json | null
          role: string
          user_id: string
        }
        Insert: {
          board_id: string
          content: string
          created_at?: string
          id?: string
          raw_content?: Json | null
          role: string
          user_id: string
        }
        Update: {
          board_id?: string
          content?: string
          created_at?: string
          id?: string
          raw_content?: Json | null
          role?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "ai_chat_messages_board_id_fkey"
            columns: ["board_id"]
            isOneToOne: false
            referencedRelation: "boards"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "ai_chat_messages_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      board_likes: {
        Row: {
          board_id: string
          date_liked: string
          id: string
          user_id: string
        }
        Insert: {
          board_id: string
          date_liked?: string
          id?: string
          user_id: string
        }
        Update: {
          board_id?: string
          date_liked?: string
          id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "board_likes_board_id_fkey"
            columns: ["board_id"]
            isOneToOne: false
            referencedRelation: "boards"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "board_likes_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      board_shares: {
        Row: {
          board_id: string
          created_at: string
          id: string
          permission_level: string
          updated_at: string
          user_id: string
          username: string | null
        }
        Insert: {
          board_id: string
          created_at?: string
          id?: string
          permission_level: string
          updated_at?: string
          user_id: string
          username?: string | null
        }
        Update: {
          board_id?: string
          created_at?: string
          id?: string
          permission_level?: string
          updated_at?: string
          user_id?: string
          username?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "fk_board"
            columns: ["board_id"]
            isOneToOne: false
            referencedRelation: "boards"
            referencedColumns: ["id"]
          },
        ]
      }
      board_sharing: {
        Row: {
          board_id: string
          created_at: string
          id: string
          likes: number | null
          public_board: boolean
          total_views: number | null
          updated_at: string
        }
        Insert: {
          board_id: string
          created_at?: string
          id?: string
          likes?: number | null
          public_board?: boolean
          total_views?: number | null
          updated_at?: string
        }
        Update: {
          board_id?: string
          created_at?: string
          id?: string
          likes?: number | null
          public_board?: boolean
          total_views?: number | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "board_sharing_board_id_fkey"
            columns: ["board_id"]
            isOneToOne: false
            referencedRelation: "boards"
            referencedColumns: ["id"]
          },
        ]
      }
      board_views: {
        Row: {
          board_id: string
          id: string
          viewed_at: string
        }
        Insert: {
          board_id: string
          id?: string
          viewed_at?: string
        }
        Update: {
          board_id?: string
          id?: string
          viewed_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "fk_board"
            columns: ["board_id"]
            isOneToOne: false
            referencedRelation: "boards"
            referencedColumns: ["id"]
          },
        ]
      }
      boards: {
        Row: {
          board_name: string
          board_name_search_vector: unknown | null
          created_at: string
          id: string
          popularity_score: number | null
          preview_image_url: string | null
          preview_upload_lock_key: string | null
          preview_upload_lock_timestamp: number | null
          preview_upload_lock_user_id: string | null
          strokes: Json | null
          updated_at: string
          user_id: string
        }
        Insert: {
          board_name?: string
          board_name_search_vector?: unknown | null
          created_at?: string
          id?: string
          popularity_score?: number | null
          preview_image_url?: string | null
          preview_upload_lock_key?: string | null
          preview_upload_lock_timestamp?: number | null
          preview_upload_lock_user_id?: string | null
          strokes?: Json | null
          updated_at?: string
          user_id: string
        }
        Update: {
          board_name?: string
          board_name_search_vector?: unknown | null
          created_at?: string
          id?: string
          popularity_score?: number | null
          preview_image_url?: string | null
          preview_upload_lock_key?: string | null
          preview_upload_lock_timestamp?: number | null
          preview_upload_lock_user_id?: string | null
          strokes?: Json | null
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "fk_boards_user_id"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      comment_reactions: {
        Row: {
          comment_id: string
          created_at: string
          id: string
          reaction_type: string
          user_id: string
        }
        Insert: {
          comment_id: string
          created_at?: string
          id?: string
          reaction_type: string
          user_id: string
        }
        Update: {
          comment_id?: string
          created_at?: string
          id?: string
          reaction_type?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "comment_reactions_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_comment_id"
            columns: ["comment_id"]
            isOneToOne: false
            referencedRelation: "comments"
            referencedColumns: ["id"]
          },
        ]
      }
      comments: {
        Row: {
          board_id: string
          content: string
          content_search_vector: unknown | null
          created_at: string
          dislikes: number | null
          id: string
          is_edited: boolean | null
          likes: number | null
          parent_comment_id: string | null
          updated_at: string
          user_id: string
        }
        Insert: {
          board_id: string
          content: string
          content_search_vector?: unknown | null
          created_at?: string
          dislikes?: number | null
          id?: string
          is_edited?: boolean | null
          likes?: number | null
          parent_comment_id?: string | null
          updated_at?: string
          user_id: string
        }
        Update: {
          board_id?: string
          content?: string
          content_search_vector?: unknown | null
          created_at?: string
          dislikes?: number | null
          id?: string
          is_edited?: boolean | null
          likes?: number | null
          parent_comment_id?: string | null
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "comments_board_id_fkey"
            columns: ["board_id"]
            isOneToOne: false
            referencedRelation: "boards"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "comments_parent_comment_id_fkey"
            columns: ["parent_comment_id"]
            isOneToOne: false
            referencedRelation: "comments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "comments_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      connections: {
        Row: {
          board_id: string
          connection_type: string
          created_at: string
          from_element_id: string
          id: string
          is_ai_generated: boolean
          label: string | null
          to_element_id: string
          updated_at: string
        }
        Insert: {
          board_id: string
          connection_type?: string
          created_at?: string
          from_element_id: string
          id?: string
          is_ai_generated?: boolean
          label?: string | null
          to_element_id: string
          updated_at?: string
        }
        Update: {
          board_id?: string
          connection_type?: string
          created_at?: string
          from_element_id?: string
          id?: string
          is_ai_generated?: boolean
          label?: string | null
          to_element_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "connections_board_id_fkey"
            columns: ["board_id"]
            isOneToOne: false
            referencedRelation: "boards"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "connections_from_element_id_fkey"
            columns: ["from_element_id"]
            isOneToOne: false
            referencedRelation: "elements"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "connections_to_element_id_fkey"
            columns: ["to_element_id"]
            isOneToOne: false
            referencedRelation: "elements"
            referencedColumns: ["id"]
          },
        ]
      }
      elements: {
        Row: {
          board_id: string
          created_at: string
          element_type: string
          file_url: string | null
          height: number | null
          id: string
          is_ai_generated: boolean
          position_x: number
          position_y: number
          text_content: string | null
          text_content_search_vector: unknown | null
          title: string
          updated_at: string
          website_url: string | null
          width: number | null
        }
        Insert: {
          board_id: string
          created_at?: string
          element_type: string
          file_url?: string | null
          height?: number | null
          id?: string
          is_ai_generated?: boolean
          position_x?: number
          position_y?: number
          text_content?: string | null
          text_content_search_vector?: unknown | null
          title?: string
          updated_at?: string
          website_url?: string | null
          width?: number | null
        }
        Update: {
          board_id?: string
          created_at?: string
          element_type?: string
          file_url?: string | null
          height?: number | null
          id?: string
          is_ai_generated?: boolean
          position_x?: number
          position_y?: number
          text_content?: string | null
          text_content_search_vector?: unknown | null
          title?: string
          updated_at?: string
          website_url?: string | null
          width?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "elements_board_id_fkey"
            columns: ["board_id"]
            isOneToOne: false
            referencedRelation: "boards"
            referencedColumns: ["id"]
          },
        ]
      }
      users: {
        Row: {
          created_at: string
          email: string
          id: string
          is_verified: boolean
          updated_at: string
          username: string
        }
        Insert: {
          created_at?: string
          email: string
          id: string
          is_verified?: boolean
          updated_at: string
          username: string
        }
        Update: {
          created_at?: string
          email?: string
          id?: string
          is_verified?: boolean
          updated_at?: string
          username?: string
        }
        Relationships: []
      }
      verification_codes: {
        Row: {
          code: string
          expires_at: string
          id: string
          sent_at: string
          user_id: string
        }
        Insert: {
          code: string
          expires_at: string
          id: string
          sent_at?: string
          user_id: string
        }
        Update: {
          code?: string
          expires_at?: string
          id?: string
          sent_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "verification_codes_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      acquire_preview_upload_lock: {
        Args: {
          p_board_id: string
          p_user_id: string
          p_lock_key: string
          p_lock_timestamp: number
          p_timeout_ms?: number
        }
        Returns: boolean
      }
      can_edit_board: {
        Args: { p_board_id: string }
        Returns: boolean
      }
      can_view_board: {
        Args: { p_board_id: string }
        Returns: boolean
      }
      check_board_access: {
        Args: { p_board_id: string; p_user_id: string }
        Returns: boolean
      }
      cleanup_expired_preview_locks: {
        Args: { p_timeout_ms?: number }
        Returns: number
      }
      count_search_public_boards_fts: {
        Args: { query_text: string; time_period_filter?: string }
        Returns: {
          total_boards: number
        }[]
      }
      create_comment: {
        Args: {
          p_content: string
          p_board_id: string
          p_parent_comment_id?: string
        }
        Returns: {
          id: string
          content: string
          created_at: string
          likes: number
          dislikes: number
          user_id: string
          board_id: string
          is_edited: boolean
          parent_comment_id: string
          updated_at: string
          username: string
        }[]
      }
      get_board_id_from_topic: {
        Args: { topic: string }
        Returns: string
      }
      has_board_edit_permission: {
        Args: { board_id_to_check: string; user_id_to_check: string }
        Returns: boolean
      }
      is_board_owner: {
        Args: { board_uuid: string }
        Returns: boolean
      }
      log_and_increment_view: {
        Args: { board_uuid: string }
        Returns: undefined
      }
      manage_connection: {
        Args: {
          p_board_id: string
          p_user_id: string
          p_action: string
          p_connection: Json
        }
        Returns: Json
      }
      manage_element: {
        Args: {
          p_user_id: string
          p_board_id: string
          p_action: string
          p_element: Json
          p_element_id?: string
        }
        Returns: Json
      }
      refresh_popularity_scores: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      release_preview_upload_lock: {
        Args: { p_board_id: string; p_lock_key: string }
        Returns: boolean
      }
      search_public_boards_fts: {
        Args: {
          query_text: string
          result_limit: number
          result_offset: number
          time_period_filter?: string
        }
        Returns: {
          board_id: string
          relevance: number
          popularity_score: number
        }[]
      }
      update_board_like: {
        Args: { p_board_id: string; p_increment: number }
        Returns: undefined
      }
      upsert_board_and_elements: {
        Args:
          | {
              p_user_id: string
              p_board_id: string
              p_board_name: string
              p_elements: Json
              p_connections: Json
            }
          | {
              p_user_id: string
              p_board_id?: string
              p_board_name?: string
              p_elements?: Json
              p_connections?: Json
              p_strokes?: Json
            }
        Returns: string
      }
      validate_realtime_board_access: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
