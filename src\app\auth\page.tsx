'use client';

import React, { useState, useRef, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { ArrowLeft, Loader2 } from 'lucide-react';
import { useAuth } from '@/context/AuthContext';
import { Button } from '@/components/ui/button';
import MainLayout from '@/components/layout/MainLayout';

interface FormErrors {
  email?: string;
  username?: string;
  password?: string;
  confirmPassword?: string;
  verificationCode?: string;
  general?: string;
}

export default function AuthPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { signIn, signUp, verifyEmail } = useAuth();
  const [isSignUp, setIsSignUp] = useState(false);
  const [isVerifying, setIsVerifying] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [email, setEmail] = useState('');
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [verificationCode, setVerificationCode] = useState(['', '', '', '', '', '']);
  const [verificationSentAt, setVerificationSentAt] = useState<string | null>(null);
  const [userId, setUserId] = useState<string | undefined>(undefined);
  const [errors, setErrors] = useState<FormErrors>({});
  const codeRefs = [
    useRef<HTMLInputElement>(null),
    useRef<HTMLInputElement>(null),
    useRef<HTMLInputElement>(null),
    useRef<HTMLInputElement>(null),
    useRef<HTMLInputElement>(null),
    useRef<HTMLInputElement>(null),
  ];

  // Handle initial mode and mode changes
  useEffect(() => {
    setIsSignUp(searchParams.get('mode') === 'signup');
  }, [searchParams]);

  const validateForm = () => {
    const newErrors: FormErrors = {};

    // Email validation
    if (!email) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    // Password validation
    if (!password) {
      newErrors.password = 'Password is required';
    } else if (password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }

    // Sign up specific validations
    if (isSignUp) {
      if (!username) {
        newErrors.username = 'Username is required';
      } else if (username.length < 3) {
        newErrors.username = 'Username must be at least 3 characters';
      }

      if (password !== confirmPassword) {
        newErrors.confirmPassword = 'Passwords do not match';
      }
    }

    // Verification code validation
    if (isVerifying && verificationCode.some(digit => !digit)) {
      newErrors.verificationCode = 'Please enter the complete verification code';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    try {
      if (isVerifying) {
        await verifyEmail(email, verificationCode.join(''), userId);
        router.push(searchParams.get('from') || '/');
        return;
      }

      if (isSignUp) {
        const result = await signUp(email, username, password);
        setIsVerifying(true);
        setUserId(result.userId);
        setVerificationSentAt(result.verificationSentAt || new Date().toISOString());
      } else {
        const result = await signIn(email, password);
        if (result.needsVerification) {
          setIsVerifying(true);
          setUserId(result.userId);
          setVerificationSentAt(result.verificationSentAt || new Date().toISOString());
        } else {
          router.push(searchParams.get('from') || '/');
        }
      }
    } catch (error: any) {
      // Handle specific error cases
      if (error.message.includes('Email already exists')) {
        setErrors(prev => ({ ...prev, email: 'Email already exists. Please sign in instead.' }));
      } else if (error.message.includes('Username already exists')) {
        setErrors(prev => ({ ...prev, username: 'Username already exists' }));
      } else if (error.message.includes('Invalid credentials')) {
        setErrors(prev => ({ ...prev, password: 'Invalid email or password' }));
      } else if (error.message.includes('Invalid or expired verification code')) {
        setErrors(prev => ({ ...prev, verificationCode: 'Invalid or expired verification code' }));
      } else if (error.message.includes('Account not verified')) {
        setIsVerifying(true);
        setVerificationSentAt(new Date().toISOString());
        setErrors(prev => ({ ...prev, general: 'A new verification code has been sent to your email' }));
      } else {
        setErrors(prev => ({ ...prev, general: error.message || 'An error occurred. Please try again.' }));
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerificationCodeChange = (index: number, value: string) => {
    if (value.length > 1) {
      // If pasting a full code
      const chars = value.slice(0, 6).split('');
      const newCode = [...verificationCode];
      chars.forEach((char, i) => {
        if (i + index < 6) {
          newCode[i + index] = char.toUpperCase();
        }
      });
      setVerificationCode(newCode);
      
      // Focus last input if there are 6 characters
      if (chars.length + index >= 6) {
        codeRefs[5].current?.focus();
      } else if (chars.length > 0) {
        codeRefs[index + chars.length]?.current?.focus();
      }

      // Auto-submit if code is complete
      if (newCode.every(digit => digit)) {
        handleSubmit(new Event('submit') as any);
      }
    } else {
      // Single character input
      const newCode = [...verificationCode];
      newCode[index] = value.toUpperCase();
      setVerificationCode(newCode);

      // Auto-focus next input
      if (value && index < 5) {
        codeRefs[index + 1].current?.focus();
      }

      // Auto-submit if code is complete
      if (newCode.every(digit => digit)) {
        handleSubmit(new Event('submit') as any);
      }
    }

    setErrors(prev => ({ ...prev, verificationCode: undefined }));
  };

  const handleVerificationKeyDown = (index: number, e: React.KeyboardEvent) => {
    if (e.key === 'Backspace' && !verificationCode[index] && index > 0) {
      // Move to previous input on backspace if current input is empty
      codeRefs[index - 1].current?.focus();
    }
  };

  const inputClassName = (error?: string) => `
    mt-1 block w-full rounded-md border px-3 py-2 text-white placeholder-gray-400 
    focus:outline-none focus:ring-1
    ${error 
      ? 'border-red-500 bg-red-900/10 focus:border-red-500 focus:ring-red-500' 
      : 'border-white/10 bg-black/20 focus:border-noir-accent focus:ring-noir-accent'
    }
  `;

  return (
    <MainLayout>
      <div className="flex min-h-screen items-center justify-center px-4">
        <div className="w-full max-w-md space-y-8 bg-black/40 p-8 rounded-lg border border-white/10">
          <div>
            <Link href="/" className="mb-6 inline-flex items-center text-white/60 hover:text-white">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Home
            </Link>
            <h2 className="text-center text-3xl font-bold tracking-tight text-white">
              {isVerifying ? 'Verify your email' : isSignUp ? 'Create your account' : 'Sign in to your account'}
            </h2>
          </div>

          {errors.general && (
            <div className="rounded-md bg-red-900/20 p-3 border border-red-500/50">
              <p className="text-sm text-red-400">{errors.general}</p>
            </div>
          )}

          <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
            {isVerifying ? (
              <>
                <div>
                  <p className="text-gray-300 mb-4 text-center">
                    We&apos;ve sent a verification code to <span className="font-medium text-white">{email}</span>
                  </p>
                  <div className="grid grid-cols-6 gap-2">
                    {verificationCode.map((digit, index) => (
                      <input
                        key={index}
                        ref={codeRefs[index]}
                        type="text"
                        maxLength={6}
                        className="w-full aspect-square border border-white/10 rounded-md bg-black/20 text-center text-xl font-bold text-white focus:border-noir-accent focus:ring-1 focus:ring-noir-accent"
                        value={digit}
                        onChange={(e) => handleVerificationCodeChange(index, e.target.value)}
                        onKeyDown={(e) => handleVerificationKeyDown(index, e)}
                        autoFocus={index === 0}
                      />
                    ))}
                  </div>
                  {errors.verificationCode && (
                    <p className="mt-2 text-sm text-red-400">{errors.verificationCode}</p>
                  )}
                  <p className="mt-4 text-xs text-gray-400 text-center">
                    Didn&apos;t receive a code? Check your spam folder or{' '}
                    <button
                      type="button"
                      className="text-noir-accent hover:text-noir-accent-hover underline"
                      onClick={async () => {
                        try {
                          await fetch('/api/auth/resend-verification', {
                            method: 'POST',
                            headers: {
                              'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({ email, userId }),
                          });
                          setVerificationSentAt(new Date().toISOString());
                          setErrors(prev => ({ ...prev, general: 'Verification code resent to your email.' }));
                        } catch (error) {
                          setErrors(prev => ({ ...prev, general: 'Failed to resend verification code.' }));
                        }
                      }}
                    >
                      resend code
                    </button>
                  </p>
                </div>
              </>
            ) : (
              <>
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-300">
                    Email address
                  </label>
                  <input
                    id="email"
                    name="email"
                    type="email"
                    autoComplete="email"
                    required
                    className={inputClassName(errors.email)}
                    placeholder="<EMAIL>"
                    value={email}
                    onChange={(e) => {
                      setEmail(e.target.value);
                      if (errors.email) setErrors({ ...errors, email: undefined });
                    }}
                  />
                  {errors.email && <p className="mt-1 text-sm text-red-400">{errors.email}</p>}
                </div>

                {isSignUp && (
                  <div>
                    <label htmlFor="username" className="block text-sm font-medium text-gray-300">
                      Username
                    </label>
                    <input
                      id="username"
                      name="username"
                      type="text"
                      autoComplete="username"
                      required
                      className={inputClassName(errors.username)}
                      placeholder="username"
                      value={username}
                      onChange={(e) => {
                        setUsername(e.target.value);
                        if (errors.username) setErrors({ ...errors, username: undefined });
                      }}
                    />
                    {errors.username && (
                      <p className="mt-1 text-sm text-red-400">{errors.username}</p>
                    )}
                  </div>
                )}

                <div>
                  <label htmlFor="password" className="block text-sm font-medium text-gray-300">
                    Password
                  </label>
                  <input
                    id="password"
                    name="password"
                    type="password"
                    autoComplete={isSignUp ? 'new-password' : 'current-password'}
                    required
                    className={inputClassName(errors.password)}
                    placeholder="••••••••"
                    value={password}
                    onChange={(e) => {
                      setPassword(e.target.value);
                      if (errors.password) setErrors({ ...errors, password: undefined });
                    }}
                  />
                  {errors.password && (
                    <p className="mt-1 text-sm text-red-400">{errors.password}</p>
                  )}
                </div>

                {isSignUp && (
                  <div>
                    <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-300">
                      Confirm Password
                    </label>
                    <input
                      id="confirmPassword"
                      name="confirmPassword"
                      type="password"
                      autoComplete="new-password"
                      required
                      className={inputClassName(errors.confirmPassword)}
                      placeholder="••••••••"
                      value={confirmPassword}
                      onChange={(e) => {
                        setConfirmPassword(e.target.value);
                        if (errors.confirmPassword)
                          setErrors({ ...errors, confirmPassword: undefined });
                      }}
                    />
                    {errors.confirmPassword && (
                      <p className="mt-1 text-sm text-red-400">{errors.confirmPassword}</p>
                    )}
                  </div>
                )}

                <div className="flex items-center justify-between">
                  {isSignUp ? (
                    <p className="text-sm text-gray-400">
                      Already have an account?{' '}
                      <Link
                        href="/auth"
                        className="text-noir-accent hover:text-noir-accent-hover"
                      >
                        Sign in
                      </Link>
                    </p>
                  ) : (
                    <p className="text-sm text-gray-400">
                      Don&apos;t have an account?{' '}
                      <Link
                        href="/auth?mode=signup"
                        className="text-noir-accent hover:text-noir-accent-hover"
                      >
                        Sign up
                      </Link>
                    </p>
                  )}
                </div>
              </>
            )}

            <div>
              <Button
                type="submit"
                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md text-white"
                disabled={isLoading}
              >
                {isLoading ? (
                  <Loader2 className="h-5 w-5 animate-spin" />
                ) : isVerifying ? (
                  'Verify Email'
                ) : isSignUp ? (
                  'Create Account'
                ) : (
                  'Sign In'
                )}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </MainLayout>
  );
} 