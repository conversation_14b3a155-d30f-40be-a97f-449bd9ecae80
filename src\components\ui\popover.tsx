'use client';

import * as React from 'react';
import { cn } from '@/lib/utils';

// Create the context at the module level
const PopoverContext = React.createContext<{
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
} | null>(null);

const usePopoverContext = () => {
  const context = React.useContext(PopoverContext);
  if (!context) {
    throw new Error('usePopoverContext must be used within a Popover');
  }
  return context;
};

interface PopoverProps {
  children: React.ReactNode;
}

const Popover: React.FC<PopoverProps> = ({ children }) => {
  const [isOpen, setIsOpen] = React.useState(false);
  const popoverRef = React.useRef<HTMLDivElement>(null);
  
  // Create the context value at the Popover level
  const contextValue = { isOpen, setIsOpen };
  
  // <PERSON><PERSON> click outside to close popover
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (popoverRef.current && !popoverRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);
  
  return (
    <PopoverContext.Provider value={contextValue}>
      <div className="relative" ref={popoverRef}>{children}</div>
    </PopoverContext.Provider>
  );
};

interface PopoverTriggerProps {
  children: React.ReactNode;
  asChild?: boolean;
  className?: string;
}

const PopoverTrigger = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & PopoverTriggerProps
>(({ children, asChild = false, className, ...props }, ref) => {
  const { isOpen, setIsOpen } = usePopoverContext();

  const togglePopover = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsOpen(!isOpen);
  };

  return (
    <div 
      ref={ref} 
      onClick={togglePopover}
      className={cn("inline-block", className)}
      {...props}
    >
      {children}
    </div>
  );
});

PopoverTrigger.displayName = 'PopoverTrigger';

interface PopoverContentProps {
  children: React.ReactNode;
  className?: string;
  sideOffset?: number;
  align?: 'start' | 'center' | 'end';
}

const PopoverContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & PopoverContentProps
>(({ className, children, sideOffset = 4, align = 'center', ...props }, ref) => {
  const { isOpen } = usePopoverContext();
  
  // Add a click handler to prevent clicks from closing the popover
  const handleContentClick = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  if (!isOpen) {
    return null;
  }

  return (
    <div 
      ref={ref}
      onClick={handleContentClick}
      className={cn(
        "absolute z-50 min-w-[8rem] overflow-hidden rounded-md border border-noir-700 bg-noir-800 p-1 text-white shadow-md",
        className
      )}
      style={{ 
        top: `calc(100% + ${sideOffset}px)`,
        ...(align === 'start' && { left: 0 }),
        ...(align === 'center' && { left: '50%', transform: 'translateX(-50%)' }),
        ...(align === 'end' && { right: 0 })
      }}
      {...props}
    >
      {children}
    </div>
  );
});

PopoverContent.displayName = 'PopoverContent';

export { Popover, PopoverTrigger, PopoverContent }; 