import { NextRequest, NextResponse } from 'next/server';
import { getAuthenticatedUser } from '@/utils/authUtils';

// --- Add Interface for Formatted Shared Board ---
interface FormattedSharedBoard {
  id: string;
  boardName: string;
  createdAt: string;
  updatedAt: string;
  elementsCount: number;
  connectionsCount: number;
  isOwner: false; // Explicitly false for shared boards
  sharedBy: string;
  isPublic: boolean; // Public board flag instead of permission level
}

export async function GET(request: NextRequest) {
  try {
    // 1. Get the authenticated user session (optimized - no API call)
    const { user, error: authError, supabase } = await getAuthenticatedUser();

    if (authError || !user) {
      return NextResponse.json({ message: 'Unauthorized: Cannot retrieve session' }, { status: 401 });
    }

    const userId = user.id;

    // 2. Find boards owned by the user
    const { data: ownedBoardsData, error: ownedBoardsError } = await supabase
      .from('boards')
      .select(`
        id,
        board_name,
        created_at,
        updated_at,
        elements:elements(count),
        connections:connections(count)
      `)
      .eq('user_id', userId)
      .order('updated_at', { ascending: false });

    if (ownedBoardsError) {
      return NextResponse.json({ message: 'Failed to fetch owned boards', error: ownedBoardsError.message }, { status: 500 });
    }

    // Format owned boards
    const formattedUserBoards = ownedBoardsData?.map(board => ({
      id: board.id,
      boardName: board.board_name,
      createdAt: board.created_at,
      updatedAt: board.updated_at,
      // Supabase returns counts as an array with one object { count: number }
      elementsCount: board.elements[0]?.count ?? 0,
      connectionsCount: board.connections[0]?.count ?? 0,
      isOwner: true,
    })) || [];

    // --- Explicitly type the array using the interface ---
    let formattedSharedBoards: FormattedSharedBoard[] = []; // Initialize with the correct type

    // 4. Return all boards
    return NextResponse.json({
      userBoards: formattedUserBoards,
      sharedBoards: formattedSharedBoards, // Type is now known
    }, { status: 200 });

  } catch (error: any) {
    return NextResponse.json({ message: 'An unexpected error occurred', error: error.message }, { status: 500 });
  }
  // No finally block needed for Supabase client
} 