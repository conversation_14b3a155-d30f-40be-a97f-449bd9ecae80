import React, { useState, useRef, useEffect } from 'react';
import BoardItem, { BoardItemProps } from '../BoardItem';
import { Trash2, Eye, EyeOff } from 'lucide-react';
import AutoFitText from '../../../ui/AutoFitText';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import type { Database } from '@/lib/database.types';

// Add the CSS styles for the Polaroid frame (fluid layout)
const polaroidStyle = `
  @font-face {
    font-family: 'DryWhiteboardMarker';
    src: url('/fonts/DryWhiteboardMarker-Regular.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
  }

  .polaroid-frame {
    padding: 15px 15px 30px 15px;
    background-color: white;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    width: 100%;
    height: 100%;
  }
  
  .polaroid-image-container {
    border: 4px solid white;
    overflow: hidden;
    background-color: white;
    flex: none;
  }
  
  .polaroid-image {
    width: 100%;
    height: auto;
    object-fit: contain;
    display: block;
  }
  
  .polaroid-text {
    margin-top: 15px;
    position: relative;
    z-index: 5;
    color: #000;
    font-weight: 500;
    flex: 1;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: 'DryWhiteboardMarker', sans-serif;
    width: 100%;
    box-sizing: border-box;
    overflow: hidden;
  }
  
  .polaroid-text > div {
    padding: 0 10px;
  }
  
  .whiteboard-text {
    font-family: 'DryWhiteboardMarker', sans-serif;
  }
  
  .polaroid-text textarea::selection {
    background-color: #3b82f6 !important;
    color: white !important;
  }
`;

interface ImageNodeProps extends Omit<BoardItemProps, 'type' | 'scale' | 'connections'> {
  imageUrl?: string;
  file_url?: string; // Add support for stored image path
  alt?: string;
  onDelete: (id: string) => void;
  onContentChange: (id: string, content: string) => void;
  onVisibilityToggle?: (id: string, isVisible: boolean) => void;
  scale?: number;
  connectMode?: boolean;
  connectStart?: string | null;
  onConnectionStart?: (id: string) => void;
  onConnectionComplete?: (id: string) => void;
  connections?: Array<{
    id: string;
    fromId?: string;
    toId?: string;
    from?: { id: string };
    to?: { id: string };
  }>;
  isVisible?: boolean;
  width?: number;
  height?: number;
  onSizeChange?: (id: string, width: number, height: number) => void;
  isMultiSelected?: boolean;
}

const ImageNode: React.FC<ImageNodeProps> = ({
  id,
  content,
  position,
  width,
  height,
  onSizeChange,
  imageUrl,
  file_url,
  alt = "",
  onPositionChange,
  onSelect,
  isSelected,
  onDelete,
  onContentChange,
  onVisibilityToggle,
  scale = 1,
  connectMode = false,
  connectStart = null,
  onConnectionStart,
  onConnectionComplete,
  connections = [],
  isVisible = true,
  isMultiSelected,
  ...rest
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [tempContent, setTempContent] = useState(content);
  const originalContentRef = useRef<string | null>(null);

  // State for handling image URL resolution (similar to ArticleNode)
  const [effectiveImageUrl, setEffectiveImageUrl] = useState<string | null>(null);
  const [imageError, setImageError] = useState(false);
  const supabase = createClientComponentClient<Database>();

  // Handle image URL resolution (similar to ArticleNode logic)
  useEffect(() => {
    console.log(`[ImageNode ${id}] useEffect triggered. Received imageUrl: ${imageUrl?.substring(0,50)}..., file_url: ${file_url}`);
    console.log(`[ImageNode ${id}] Full imageUrl: ${imageUrl}`);
    console.log(`[ImageNode ${id}] Full file_url: ${file_url}`);
    setImageError(false);

    // Priority 1: Use imageUrl if it's a displayable URL (blob or http)
    if (imageUrl && (imageUrl.startsWith('http') || imageUrl.startsWith('blob:') || imageUrl.startsWith('data:'))) {
      console.log(`[ImageNode ${id}] Using displayable imageUrl: ${imageUrl.substring(0,50)}...`);
      console.log(`[ImageNode ${id}] Setting effectiveImageUrl to: ${imageUrl}`);
      setEffectiveImageUrl(imageUrl);
      return;
    }

    // Priority 2: If imageUrl isn't displayable, but file_url (storage path) exists, fetch signed URL
    if (file_url) {
      console.log(`[ImageNode ${id}] imageUrl not displayable or missing, fetching signed URL for file_url: ${file_url}`);

      const fetchSignedUrl = async (path: string) => {
        try {
          const { data: signedUrlData, error: signedUrlError } = await supabase
            .storage
            .from('images')
            .createSignedUrl(path, 3600); // 1 hour expiry

          if (signedUrlError) {
            console.error(`[ImageNode ${id}] Failed createSignedUrl for path ${path}:`, signedUrlError);
            console.log(`[ImageNode ${id}] Attempting getPublicUrl for path: ${path}`);
            const { data: publicUrlData } = supabase.storage.from('images').getPublicUrl(path);

            if (publicUrlData?.publicUrl) {
              console.log(`[ImageNode ${id}] Using public URL for path ${path}:`, publicUrlData.publicUrl);
              setEffectiveImageUrl(publicUrlData.publicUrl);
            } else {
              console.warn(`[ImageNode ${id}] Failed getPublicUrl for path ${path}. Setting image to null.`);
              setEffectiveImageUrl(null);
              setImageError(true);
            }
          } else if (signedUrlData?.signedUrl) {
            console.log(`[ImageNode ${id}] Successfully generated signed URL for path ${path}`);
            setEffectiveImageUrl(signedUrlData.signedUrl);
          } else {
             console.warn(`[ImageNode ${id}] createSignedUrl returned no data/error for path ${path}. Setting image to null.`);
             setEffectiveImageUrl(null);
             setImageError(true);
          }
        } catch (error) {
          console.error(`[ImageNode ${id}] Error in fetchSignedUrl for path ${path}:`, error);
          setEffectiveImageUrl(null);
          setImageError(true);
        }
      };

      fetchSignedUrl(file_url);
      return;
    }

    // Fallback: Neither imageUrl nor file_url yielded a result
    console.log(`[ImageNode ${id}] No displayable imageUrl or file_url found.`);
    setEffectiveImageUrl(null);
  }, [imageUrl, file_url, id, supabase]);

  const handleDoubleClick = () => {
    if (!connectMode) {
      originalContentRef.current = content; // Store original content
      setIsEditing(true);
      setTempContent(content);
    }
  };
  
  const handleContentChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setTempContent(e.target.value);
  };
  
  const handleBlur = () => {
    const wasEditing = isEditing; // Check if we were editing before setting state
    setIsEditing(false);
    
    // Only update content if we were actually editing AND content changed
    if (wasEditing && tempContent !== content) {
      onContentChange(id, tempContent);
    }
    
    // Always clear the ref on blur
    originalContentRef.current = null;
  };
  
  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    onDelete(id);
  };
  
  // Auto-select all text when the textarea is focused
  const handleTextareaFocus = (e: React.FocusEvent<HTMLTextAreaElement>) => {
    e.target.select();
  };
  
  const preventScrollPropagation = (e: React.UIEvent) => {
    e.stopPropagation();
  };
  
  const handleVisibilityToggle = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onVisibilityToggle) {
      onVisibilityToggle(id, !isVisible);
    }
  };
  
  return (
    <>
      <style>{polaroidStyle}</style>
      <BoardItem
        id={id}
        type={isVisible ? "image" : "image-invisible"}
        content={content}
        position={position}
        width={width}
        height={height}
        onSizeChange={onSizeChange}
        onPositionChange={onPositionChange}
        onSelect={onSelect}
        isSelected={isSelected}
        scale={scale}
        connectMode={connectMode}
        connectStart={connectStart}
        connections={connections}
        onConnectionStart={onConnectionStart}
        onConnectionComplete={onConnectionComplete}
        isEditMode={isEditing}
        isMultiSelected={isMultiSelected}
      >
        <div className={`${isVisible ? 'polaroid-frame bg-white rounded-md shadow-lg flex flex-col' : ''} relative w-full h-full`}>
          {effectiveImageUrl && !imageError ? (
            <div className={isVisible ? "polaroid-image-container" : ""}>
              <img
                src={effectiveImageUrl}
                alt={alt || "Image"}
                className={isVisible ? "polaroid-image" : "w-full h-auto"}
                onError={(e) => {
                  console.log(`[ImageNode ${id}] Image failed to load: ${effectiveImageUrl}`);
                  console.log(`[ImageNode ${id}] Image error event:`, e);
                  setImageError(true);
                }}
                onLoad={() => {
                  console.log(`[ImageNode ${id}] Image loaded successfully: ${effectiveImageUrl}`);
                }}
                referrerPolicy="no-referrer"
                loading="lazy"
              />
            </div>
          ) : (
            <div className="border-2 border-white bg-gray-200 flex-1 flex items-center justify-center text-gray-500 w-full h-full">
              {imageError ? `Failed to load image: ${effectiveImageUrl}` : `No image (effectiveImageUrl: ${effectiveImageUrl})`}
            </div>
          )}
          
          {isVisible && (
            <div className="polaroid-text">
              {isEditing ? (
                <textarea
                  value={tempContent}
                  onChange={handleContentChange}
                  onBlur={handleBlur}
                  onFocus={handleTextareaFocus}
                  onScroll={preventScrollPropagation}
                  className="w-full h-full resize-none bg-transparent border-none focus:outline-none text-center whiteboard-text"
                  placeholder="Add a caption..."
                  autoFocus
                />
              ) : (
                <div onDoubleClick={handleDoubleClick} data-nodrag="true" className="w-full h-full flex items-center justify-center cursor-text">
                  <AutoFitText 
                    mode="multi"
                    className="whiteboard-text"
                    style={{ width: '100%', height: '100%' }}
                    parentWidth={width}
                    parentHeight={height ? height / 4 : 50} // Allocate roughly 1/4 of the height for text
                  >
                    {content || "Double click to add a caption"}
                  </AutoFitText>
                </div>
              )}
            </div>
          )}
          
          {!connectMode && !isMultiSelected && (
            <div className="absolute top-4 right-4 flex space-x-2">
              <button
                className="p-1 text-black bg-white/80 rounded-full hover:bg-white focus:outline-none"
                onClick={handleVisibilityToggle}
                data-nodrag="true"
                title={isVisible ? "Hide Image" : "Show Image"}
              >
                {isVisible ? <Eye size={16} /> : <EyeOff size={16} />}
              </button>
              <button
                className="p-1 text-black bg-white/80 rounded-full hover:bg-white focus:outline-none"
                onClick={handleDelete}
                data-nodrag="true"
              >
                <Trash2 size={16} />
              </button>
            </div>
          )}
        </div>
      </BoardItem>
    </>
  );
};

export default ImageNode; 