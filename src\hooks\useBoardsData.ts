'use client';

import { useQuery } from '@tanstack/react-query';

export interface BoardItem {
  id: string;
  boardName: string;
  createdAt: string;
  updatedAt: string;
  elementsCount: number;
  connectionsCount: number;
  isOwner: boolean;
  previewImageUrl?: string | null;
  sharedBy?: string;
  permissionLevel?: string;
}

interface PaginationInfo {
  totalUserBoards: number;
  totalSharedBoards: number;
  limit: number;
  offset: number;
}

interface BoardsResponse {
  userBoards: BoardItem[];
  sharedBoards: BoardItem[];
  pagination: PaginationInfo;
}

const fetchBoards = async (limit: number = 12, offset: number = 0): Promise<BoardsResponse> => {
  const response = await fetch(`/api/board/all-boards?limit=${limit}&offset=${offset}`);
  
  if (!response.ok) {
    const error = await response.json().catch(() => ({ message: response.statusText }));
    throw new Error(error.message || 'Failed to fetch boards');
  }
  
  return response.json();
};

export function useBoardsData(limit: number = 12, offset: number = 0) {
  return useQuery({
    queryKey: ['boards', limit, offset],
    queryFn: () => fetchBoards(limit, offset),
    staleTime: 60 * 1000, // 1 minute
    retry: 1,
  });
} 