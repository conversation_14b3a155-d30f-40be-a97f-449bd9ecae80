'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Loader2 } from 'lucide-react';
import { useAuth } from '@/context/AuthContext';
import { Button } from '@/components/ui/button';
import { Modal } from '@/components/ui/modal';

// Declare google accounts types for TypeScript (copied from SignInModal)
// In a larger app, consider moving this to a global .d.ts file (e.g., src/types/google-gsi.d.ts)
declare global {
  interface Window {
    googleGsiClientLoaded?: boolean;
    google?: {
      accounts: {
        id: {
          initialize: (config: {
            client_id: string;
            callback: (response: CredentialResponse) => void;
          }) => void;
          renderButton: (
            parent: HTMLElement,
            options: {
              theme?: 'outline' | 'filled_blue' | 'filled_black';
              size?: 'large' | 'medium' | 'small';
              type?: 'standard' | 'icon';
              shape?: 'rectangular' | 'pill' | 'circle' | 'square';
              text?: 'signin_with' | 'signup_with' | 'continue_with' | 'signin';
              logo_alignment?: 'left' | 'center';
              width?: string; // e.g., '250px'
            }
          ) => void;
          prompt: (momentNotification?: (notification: any) => void) => void;
        };
      };
    };
  }
}

interface CredentialResponse {
  credential?: string;
  select_by?: string;
  // ... other potential fields in the response
}

interface SignUpModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
  onSignInClick?: () => void;
  onVerificationNeeded: (email: string, userId?: string) => void;
}

interface FormErrors {
  email?: string;
  username?: string;
  password?: string;
  confirmPassword?: string;
  general?: string;
}

export function SignUpModal({ 
  isOpen, 
  onClose, 
  onSuccess, 
  onSignInClick,
  onVerificationNeeded 
}: SignUpModalProps) {
  const { signUp, checkEmailExists, checkUsernameExists, signInWithGoogle } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [email, setEmail] = useState('');
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [errors, setErrors] = useState<FormErrors>({});

  const googleButtonContainerRef = useRef<HTMLDivElement>(null);
  const [isGsiButtonRendered, setIsGsiButtonRendered] = useState(false);

  const validateForm = async () => {
    const newErrors: FormErrors = {};
    let isValid = true;

    // Password validation
    if (!password) {
      newErrors.password = 'Password is required';
      isValid = false;
    } else if (password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
      isValid = false;
    }

    // Confirm password validation
    if (!confirmPassword) {
      newErrors.confirmPassword = 'Please confirm your password';
      isValid = false;
    } else if (password && password !== confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
      isValid = false;
    }

    if (!isValid) {
      setErrors(newErrors);
      return false;
    }

    // Email validation
    if (!email) {
      newErrors.email = 'Email is required';
      isValid = false;
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      newErrors.email = 'Please enter a valid email address';
      isValid = false;
    } else {
      try {
        const emailExists = await checkEmailExists(email);
        if (emailExists) {
          newErrors.email = 'Email already exists. Please sign in instead.';
          isValid = false;
        }
      } catch (error) {
        console.error('Error checking email:', error);
      }
    }

    // Username validation
    if (!username) {
      newErrors.username = 'Username is required';
      isValid = false;
    } else if (username.length < 3) {
      newErrors.username = 'Username must be at least 3 characters';
      isValid = false;
    } else if (!/^[a-zA-Z0-9_-]+$/.test(username)) {
      newErrors.username = 'Username can only contain letters, numbers, underscores, and hyphens';
      isValid = false;
    } else {
      try {
        const usernameExists = await checkUsernameExists(username);
        if (usernameExists) {
          newErrors.username = 'Username already exists. Please choose another.';
          isValid = false;
        }
      } catch (error) {
        console.error('Error checking username:', error);
      }
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    setIsLoading(true);
    const isValid = await validateForm();
    
    if (!isValid) {
      setIsLoading(false);
      return;
    }
    
    try {
      const result = await signUp(email, username, password);
      
      if (result.needsVerification) {
        onVerificationNeeded(email, result.userId);
        onClose();
      } else if (onSuccess) {
        onSuccess();
        onClose();
      }
    } catch (error: any) {
      console.error('Sign up error:', error);
      if (error.message.includes('Email already exists')) {
        setErrors(prev => ({ ...prev, email: 'Email already exists. Please sign in instead.' }));
      } else if (error.message.includes('Username already exists')) {
        setErrors(prev => ({ ...prev, username: 'Username already exists. Please choose another.' }));
      } else {
        setErrors(prev => ({ ...prev, general: error.message || 'Failed to sign up' }));
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleCredentialResponse = async (response: CredentialResponse) => {
    console.log("SignUpModal Google Credential Response:", response);
    if (response.credential) {
      setIsLoading(true); 
      setErrors({});
      try {
        await signInWithGoogle(response.credential);
        if (onSuccess) onSuccess();
        onClose();
      } catch (error: any) {
        console.error('SignUpModal Google sign in error after GSI callback:', error);
        setErrors({ general: error.message || 'Failed to sign up/in with Google' });
      } finally {
        setIsLoading(false);
      }
    } else {
      console.error('SignUpModal Google GSI: No credential in response');
      setErrors({ general: 'Google Sign-Up failed. Please try again.'});
    }
  };

  useEffect(() => {
    console.log('SignUpModal useEffect triggered. isOpen:', isOpen);
    if (!isOpen) {
      if (googleButtonContainerRef.current) {
        googleButtonContainerRef.current.innerHTML = '';
      }
      setIsGsiButtonRendered(false);
      return;
    }

    const attemptRender = () => {
      if (typeof window === 'undefined' || !googleButtonContainerRef.current) {
        console.log('SignUpModal (attemptRender): Waiting for window or googleButtonContainerRef.current.');
        return false;
      }
      if (!window.googleGsiClientLoaded || !(window.google && window.google.accounts && window.google.accounts.id)) {
        console.log('SignUpModal (attemptRender): Google GSI script or accounts object not ready yet.');
        return false;
      }
      if (isGsiButtonRendered || googleButtonContainerRef.current!.childElementCount > 0) {
         console.log('SignUpModal (attemptRender): GSI button process already initiated or container not empty.');
         return true;
      }
      console.log("SignUpModal (attemptRender): Attempting to initialize and render Google Sign-Up button.");
      try {
        const clientId = process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID;
        if (!clientId) {
          console.error('SignUpModal (attemptRender): NEXT_PUBLIC_GOOGLE_CLIENT_ID is not set.');
          setErrors({ general: 'Google Sign-Up is not configured.' });
          return true;
        }
        window.google.accounts.id.initialize({
          client_id: clientId,
          callback: handleGoogleCredentialResponse,
        });
        if (googleButtonContainerRef.current) {
            googleButtonContainerRef.current.innerHTML = ''; 
        }
        window.google.accounts.id.renderButton(
          googleButtonContainerRef.current!,
          { theme: 'outline', size: 'large', type: 'standard', text: 'signup_with', width: googleButtonContainerRef.current!.offsetWidth ? `${googleButtonContainerRef.current!.offsetWidth}px` : '100%', shape: 'rectangular' }
        );
        setIsGsiButtonRendered(true);
        console.log('SignUpModal (attemptRender): Google Sign-Up button render process initiated.');
      } catch (error) {
        console.error('SignUpModal (attemptRender): Error rendering Google Sign-Up button:', error);
        setErrors({ general: 'Could not load Google Sign-Up. Please try again later.'});
        return true;
      }
      return true;
    };

    if (!attemptRender()) {
      console.log("SignUpModal: Initial render attempt failed, setting up interval.");
      const intervalId = setInterval(() => {
        console.log("SignUpModal: Interval check for GSI readiness.");
        if (attemptRender()) {
          console.log("SignUpModal: GSI ready via interval, clearing interval.");
          clearInterval(intervalId);
        }
      }, 300);
      return () => {
        console.log("SignUpModal: Clearing GSI readiness interval (useEffect cleanup).");
        clearInterval(intervalId);
      };
    } else {
      console.log("SignUpModal: Initial render attempt successful, no interval needed.");
    }
  }, [isOpen]);

  const inputClassName = (error?: string) => 
    error 
      ? "mt-1 block w-full rounded-md border px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-1 border-red-500 bg-red-900/10 focus:border-red-500 focus:ring-red-500" 
      : "mt-1 block w-full rounded-md border px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-1 border-white/10 bg-black/20 focus:border-noir-accent focus:ring-noir-accent";

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Create your account">
      <p className="text-sm text-gray-400 text-center mb-8">
        Join the detective community to save and share your investigation boards
      </p>

      <form className="space-y-6" onSubmit={handleSubmit}>
        {errors.general && (
          <div className="text-red-500 text-sm text-center bg-red-500/10 p-2 rounded-md border border-red-500/20">
            {errors.general}
          </div>
        )}

        <div>
          <label htmlFor="email" className="block text-sm font-medium text-gray-300">
            Email
          </label>
          <input
            id="email"
            type="email"
            required
            className={inputClassName(errors.email)}
            placeholder="<EMAIL>"
            value={email}
            onChange={(e) => {
              setEmail(e.target.value);
              setErrors(prev => ({ ...prev, email: undefined, general: undefined }));
            }}
          />
          {errors.email && (
            <p className="mt-1 text-sm text-red-500">{errors.email}</p>
          )}
        </div>

        <div>
          <label htmlFor="username" className="block text-sm font-medium text-gray-300">
            Username
          </label>
          <input
            id="username"
            type="text"
            required
            className={inputClassName(errors.username)}
            placeholder="detective123"
            value={username}
            onChange={(e) => {
              setUsername(e.target.value);
              setErrors(prev => ({ ...prev, username: undefined, general: undefined }));
            }}
          />
          {errors.username && (
            <p className="mt-1 text-sm text-red-500">{errors.username}</p>
          )}
        </div>

        <div>
          <label htmlFor="password" className="block text-sm font-medium text-gray-300">
            Password
          </label>
          <input
            id="password"
            type="password"
            required
            className={inputClassName(errors.password)}
            placeholder="••••••••"
            value={password}
            onChange={(e) => {
              setPassword(e.target.value);
              setErrors(prev => ({ ...prev, password: undefined, general: undefined }));
            }}
          />
          {errors.password && (
            <p className="mt-1 text-sm text-red-500">{errors.password}</p>
          )}
        </div>

        <div>
          <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-300">
            Confirm Password
          </label>
          <input
            id="confirmPassword"
            type="password"
            required
            className={inputClassName(errors.confirmPassword)}
            placeholder="••••••••"
            value={confirmPassword}
            onChange={(e) => {
              setConfirmPassword(e.target.value);
              setErrors(prev => ({ ...prev, confirmPassword: undefined, general: undefined }));
            }}
          />
          {errors.confirmPassword && (
            <p className="mt-1 text-sm text-red-500">{errors.confirmPassword}</p>
          )}
        </div>

        <div>
          <Button
            type="submit"
            disabled={isLoading}
            className="w-full bg-noir-accent hover:bg-noir-accent/90 relative"
          >
            {isLoading && !errors.general ? (
              <Loader2 className="h-5 w-5 animate-spin" />
            ) : (
              'Create Account'
            )}
          </Button>
        </div>
      </form>

      <div className="relative my-6">
        <div className="relative flex justify-center text-xs uppercase">
          <span className="bg-noir-primary px-2 text-gray-400">
            Or
          </span>
        </div>
      </div>

      <div ref={googleButtonContainerRef} id="gsi-signup-button-container" className="w-full flex justify-center"></div>
      {isLoading && !isGsiButtonRendered && !errors.general && (
        <div className="flex justify-center items-center h-[50px]">
            <Loader2 className="h-6 w-6 animate-spin text-gray-400" />
        </div>
      )}

      <div className="mt-4 text-center">
        <button
          type="button"
          className="text-sm text-gray-400 hover:text-white"
          onClick={onSignInClick}
        >
          Already have an account? Sign in
        </button>
      </div>
    </Modal>
  );
} 