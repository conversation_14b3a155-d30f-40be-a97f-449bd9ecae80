import { NextRequest, NextResponse } from 'next/server';
import type { Database } from '@/lib/database.types';
import { getAuthenticatedUser } from '@/utils/authUtils';
import { AI_CONFIG } from '@/config/ai-limits';

// GET handler to fetch chat history for a board
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const boardId = searchParams.get('boardId');

  if (!boardId) {
    return NextResponse.json({ error: 'Board ID is required' }, { status: 400 });
  }

  try {
    // We need the Supabase client with the user's session from cookies
    // for RLS to work, even if we don't explicitly check the user here.
    const { supabase } = await getAuthenticatedUser();

    // Check if user has view access (RLS should handle this based on can_view_board)
    const { data, error } = await supabase
      .from('ai_chat_messages')
      .select('*')
      .eq('board_id', boardId)
      .order('created_at', { ascending: true });

    if (error) {
      console.error('[API /ai/chat GET] Error fetching chat history:', error);
      // Don't expose detailed error to client
      return NextResponse.json({ error: 'Failed to fetch chat history' }, { status: 500 });
    }

    return NextResponse.json(data, { status: 200 });

  } catch (err: any) {
    console.error('[API /ai/chat GET] Unexpected error:', err);
    return NextResponse.json({ error: 'An unexpected error occurred' }, { status: 500 });
  }
}

// POST handler to save a chat message
export async function POST(request: NextRequest) {
  try {
    const { boardId, role, content, rawContent, is_confirmation = false } = await request.json();

    // Basic validation
    if (!boardId || !role || (content === undefined && !rawContent)) {
      return NextResponse.json({ error: 'Missing required fields (boardId, role, content or rawContent)' }, { status: 400 });
    }
    // Allow extended roles like "user+1", "assistant+2" for chat sessions
    const baseRole = role.includes('+') ? role.split('+')[0] : role;
    if (baseRole !== 'user' && baseRole !== 'assistant') {
      return NextResponse.json({ error: 'Invalid role specified' }, { status: 400 });
    }

    // Get the authenticated user ID
    const { user, error: authError, supabase } = await getAuthenticatedUser();

    if (authError || !user) {
      console.error('[API /ai/chat POST] Authentication error:', authError);
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    // Before RLS policy check

    // --- Rate Limiting Check (only for user messages) ---
    if (baseRole === 'user' && !is_confirmation) {
      const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();

      const { count, error: countError } = await supabase
        .from('ai_chat_messages')
        .select('id', { count: 'exact', head: true })
        .eq('board_id', boardId)
        .eq('user_id', user.id)
        .ilike('role', 'user%') // Count all user sessions (user, user+1, user+2, etc.)
        .gte('created_at', twentyFourHoursAgo);

      if (countError) {
        console.error('[API /ai/chat POST] Error counting messages for rate limit:', countError);
        return NextResponse.json({ error: 'Failed to check message limit' }, { status: 500 });
      }

      const messageLimit = AI_CONFIG.DAILY_MESSAGE_LIMIT;
      if (count !== null && count >= messageLimit) {
        console.log(`[API /ai/chat POST] User ${user.id} reached message limit for board ${boardId}`);
        return NextResponse.json({ error: `Message limit of ${messageLimit} per day reached for this board.` }, { status: 429 }); // 429 Too Many Requests
      }
    }
    // --- End Rate Limiting Check ---

    // RLS policy "insert_chat_if_can_view_board" checks auth.uid() = user_id AND can_view_board(board_id)
    const { data, error: insertError } = await supabase
      .from('ai_chat_messages')
      .insert({
        board_id: boardId,
        user_id: user.id, // Use authenticated user ID
        role: role,
        content: content,
        raw_content: rawContent ?? null
      })
      .select();

    if (insertError) {
      console.error('[API /ai/chat POST] Error inserting chat message:', insertError);
      // RLS failures often return a specific error code/message, but we avoid exposing details
      if (insertError.code === '42501') { // permission denied
        return NextResponse.json({ error: 'Permission denied to save message to this board' }, { status: 403 });
      }
      return NextResponse.json({ error: 'Failed to save chat message' }, { status: 500 });
    }

    return NextResponse.json(data[0], { status: 201 });

  } catch (err: any) {
    console.error('[API /ai/chat POST] Unexpected error:', err);
    if (err instanceof SyntaxError) {
      return NextResponse.json({ error: 'Invalid JSON payload' }, { status: 400 });
    }
    return NextResponse.json({ error: 'An unexpected error occurred' }, { status: 500 });
  }
}

// Add PUT handler at the end
export async function PUT(request: NextRequest) {
  try {
    const { messageId, content, rawContent } = await request.json();

    if (!messageId || !content) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    const { user, supabase } = await getAuthenticatedUser();
    if (!user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    const { error } = await supabase
      .from('ai_chat_messages')
      .update({
        content,
        raw_content: rawContent ?? null
      })
      .eq('id', messageId)
      .eq('user_id', user.id);

    if (error) {
      console.error('[API /ai/chat PUT] Error updating message:', error);
      return NextResponse.json({ error: 'Failed to update message' }, { status: 500 });
    }

    return NextResponse.json({ success: true }, { status: 200 });
  } catch (err: any) {
    console.error('[API /ai/chat PUT] Unexpected error:', err);
    return NextResponse.json({ error: 'An unexpected error occurred' }, { status: 500 });
  }
} 