import React, { useState, useEffect, useRef } from 'react';
import { X, Loader2, Globe } from 'lucide-react';
import { ArticleFormModalProps, ArticleFormState } from '../../../../types';
import { toast } from 'sonner';

// Define the article data type from the API
type ArticleApiResponse = {
  title: string;
  description: string;
  imageUrl: string | null;
  domain: string;
  url: string;
  website_url: string;
};

/**
 * Modal for adding articles by URL
 */
const ArticleForm: React.FC<ArticleFormModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  initialData = { title: '', url: '', content: '' }
}) => {
  const [url, setUrl] = useState(initialData.url);
  const [isLoading, setIsLoading] = useState(false);
  const [apiError, setApiError] = useState<string | null>(null);
  const [fetchErrorDetails, setFetchErrorDetails] = useState<{ title?: boolean, content?: boolean, image?: boolean }>({});
  const [loadingStatus, setLoadingStatus] = useState<string>('Extracting data...');

  // State for manual input mode
  const [manualMode, setManualMode] = useState(false);
  const [manualTitle, setManualTitle] = useState('');
  const [manualContent, setManualContent] = useState('');
  const [manualImageUrl, setManualImageUrl] = useState('');

  // State for direct file upload in manual mode
  const [isDraggingFile, setIsDraggingFile] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [localPreviewUrl, setLocalPreviewUrl] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Reset form data when modal opens or initialData changes
  useEffect(() => {
    if (isOpen) {
      setUrl(initialData.url || ''); // Ensure URL is reset
      // Reset all states
      setManualMode(false);
      setManualTitle('');
      setManualContent('');
      setManualImageUrl('');
      setSelectedFile(null);
      setLocalPreviewUrl(null);
      setIsDraggingFile(false);
      if (fileInputRef.current) fileInputRef.current.value = ''; // Clear file input
      setApiError(null);
      setFetchErrorDetails({});
      setLoadingStatus('Extracting data...');
      setIsLoading(false); // Ensure loading is false initially
    }
  }, [isOpen, initialData]);

  const handleUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setUrl(e.target.value);
    // Clear any previous errors when user types
    if (apiError) setApiError(null);
  };

  // Upload image to storage via proxy API
  const uploadImageToStorage = async (imageUrl: string): Promise<string | null> => {
    try {
      setLoadingStatus('Uploading image...');
      console.log(`Uploading image via proxy: ${imageUrl}`);
      
      const response = await fetch('/api/storage/proxy-image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ imageUrl }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        console.error('Image proxy error:', errorData);
        return null;
      }
      
      const result = await response.json();
      console.log('Image proxy success:', result);
      return result.path;
    } catch (error) {
      console.error('Error in image proxy:', error);
      return null;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // If in manual mode, submit the manual data
    if (manualMode) {
      setIsLoading(true);
      setApiError(null);
      let finalImageUrl = manualImageUrl;
      let finalFileUrl: string | undefined = undefined;

      // Prioritize direct file upload if a file was selected
      if (selectedFile) {
        try {
          const imagePath = await uploadDirectFile(selectedFile);
          if (imagePath) {
            finalFileUrl = imagePath;
            finalImageUrl = ''; // Clear manual URL if direct upload succeeds
          } else {
            // Upload failed, keep modal open?
            toast.error('Failed to upload selected image.');
            setIsLoading(false);
            return; // Keep modal open if direct upload fails
          }
        } catch (fileError) {
          console.error('Error uploading selected file:', fileError);
          toast.error('Error uploading selected file.');
          setIsLoading(false);
          return; // Keep modal open
        }
      } else if (finalImageUrl) { // Only try URL proxy if no file was selected
        try {
          const imagePath = await uploadImageToStorage(finalImageUrl);
          if (imagePath) {
            finalFileUrl = imagePath;
            finalImageUrl = ''; // Clear manual URL if proxy upload succeeds
          } else {
            console.warn('Failed to upload image to storage, continuing with external URL');
          }
        } catch (error) {
          console.error('Error handling image upload:', error);
          // Continue with external URL if there's an error with the image upload
        }
      }

      onSubmit({
        title: manualTitle || 'Manual Article',
        url: url, // Original URL
        content: manualContent || `Article from ${new URL(url).hostname}. Double-click to add notes.`,
        website_url: url, // Use original URL as website_url
        imageUrl: finalImageUrl || null,
        file_url: finalFileUrl, // This will contain the path from direct upload OR proxy
      });
      setIsLoading(false);
      onClose(); // Close after manual submission
      return;
    }

    // --- Automatic Fetch Logic ---
    if (!url.trim()) {
      return;
    }
    
    setIsLoading(true);
    setApiError(null);
    setLoadingStatus('Extracting data...');
    
    try {
      console.log(`Extracting data from: ${url}`);
      
      // Call the API to extract article data
      const response = await fetch('/api/board/extract-article', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ url }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to extract article data');
      }
      
      const articleData: ArticleApiResponse = await response.json();
      console.log('Extracted article data:', articleData);
      
      // --- Check for incomplete data --- 
      const missingFields: { title?: boolean, content?: boolean } = {};
      if (!articleData.title) missingFields.title = true;
      if (!articleData.description) missingFields.content = true; // Using description for content

      if (Object.keys(missingFields).length > 0) {
        setManualMode(true);
        setManualTitle(articleData.title || ''); // Pre-fill if available
        // Pre-fill content, create default if description is missing
        setManualContent(articleData.description 
          ? `${articleData.description}\n\nSource: ${articleData.domain}` 
          : `Article from ${articleData.domain}. Double-click to add notes.`);
        setManualImageUrl(articleData.imageUrl || ''); // Pre-fill image URL
        setFetchErrorDetails(missingFields);
        setApiError('Could not fully extract article data. Please review and complete the details below.');
        setIsLoading(false);
        return; // Stay in the modal
      }

      // --- Data seems complete, proceed with normal flow ---
      const content = articleData.description
        ? `${articleData.description}\n\nSource: ${articleData.domain}`
        : `Article from ${articleData.domain}. Double-click to add notes.`;
      
      // Create basic article data
      let formData: ArticleFormState = {
        title: articleData.title || 'Article Link',
        url: articleData.url,
        content,
        website_url: articleData.website_url,
        imageUrl: articleData.imageUrl,
      };
      
      // If there's an image URL, upload it via our proxy
      if (articleData.imageUrl) {
        try {
          const imagePath = await uploadImageToStorage(articleData.imageUrl);
          
          if (imagePath) {
            // Update the form data with the uploaded image path
            formData = {
              ...formData,
              file_url: imagePath,
              // Keep original imageUrl for immediate display
            };
            console.log(`Image uploaded successfully to: ${imagePath}`);
          } else {
            console.warn('Failed to upload image to storage, continuing with external URL');
          }
        } catch (error) {
          console.error('Error handling image upload:', error);
          // Continue with external URL if there's an error with the image upload
        }
      }
      
      // Submit the automatically fetched and processed data
      onSubmit(formData);
      
      // Close the modal
      onClose();
    } catch (error: any) {
      console.error('Error extracting article data:', error);

      // --- Handle Fetch Error: Switch to Manual Mode --- 
      setManualMode(true);
      setManualTitle(''); // Clear fields for manual input
      setManualContent('');
      setManualImageUrl('');
      setFetchErrorDetails({ title: true, content: true, image: true }); // Indicate general failure
      setApiError(error.message || 'Failed to extract article data. Please provide the details manually.');
      setIsLoading(false);
      return; // Stay in the modal
    }
  };

  // --- Direct File Upload Logic (for manual mode) ---
  const handleFileDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDraggingFile(true);
  };

  const handleFileDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDraggingFile(false);
  };

  const handleFileDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDraggingFile(false);
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      processFile(e.dataTransfer.files[0]);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      processFile(e.target.files[0]);
    }
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  const processFile = (file: File) => {
    if (!file.type.match('image.*')) {
      toast.error('Only image files are allowed!');
      return;
    }
    // Check file size
    const MAX_FILE_SIZE_MB = 10;
    const MAX_FILE_SIZE_BYTES = MAX_FILE_SIZE_MB * 1024 * 1024;
    if (file.size > MAX_FILE_SIZE_BYTES) {
        toast.error(`File is too large. Max size is ${MAX_FILE_SIZE_MB}MB.`);
        return;
    }
    setSelectedFile(file);
    // Generate preview
    const reader = new FileReader();
    reader.onload = (e) => {
      setLocalPreviewUrl(e.target?.result as string);
    };
    reader.readAsDataURL(file);
    // Clear manual image URL if a file is selected
    setManualImageUrl(''); 
  };

  // Uploads the selected File object
  const uploadDirectFile = async (file: File): Promise<string | null> => {
    setLoadingStatus('Uploading selected image...');
    
    try {
      // 1. Get the signed URL from our new API route
      const signedUrlResponse = await fetch('/api/storage/signed-upload-url', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          fileName: file.name,
          fileType: file.type,
          fileSize: file.size,
        }),
      });

      const signedUrlData = await signedUrlResponse.json();

      if (!signedUrlResponse.ok) {
        throw new Error(signedUrlData.message || 'Failed to get signed URL.');
      }
      
      const { signedUrl, path } = signedUrlData;

      // 2. Upload the file directly to Supabase Storage using the signed URL
      const uploadResponse = await fetch(signedUrl, {
        method: 'PUT',
        body: file,
        headers: {
            'Content-Type': file.type,
        },
      });

      if (!uploadResponse.ok) {
        const errorBody = await uploadResponse.text();
        console.error("Direct upload failed:", errorBody);
        throw new Error('Failed to upload image to storage.');
      }

      console.log('Direct file upload success:', { path });
      return path; // Return the path of the uploaded file

    } catch (error: any) {
      console.error('Direct file upload error:', error);
      toast.error(error.message || 'Failed to upload selected image.');
      return null;
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50">
      <div className="bg-noir-100 rounded-lg w-full max-w-md p-4 shadow-xl">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-white text-lg font-medium">Add Article Link</h2>
          <button 
            onClick={onClose}
            className="text-white/70 hover:text-white"
            aria-label="Close"
          >
            <X size={20} />
          </button>
        </div>
        
        <form onSubmit={handleSubmit}>
          <div className="mb-4">
            <label htmlFor="url" className="block text-white/70 mb-1 text-sm">
              Article URL
            </label>
            <div className="relative">
              <input
                type="url"
                id="url"
                name="url"
                value={url}
                onChange={handleUrlChange}
                className="w-full bg-noir-200 border border-noir-50 rounded p-2 pl-9 text-white"
                placeholder="https://example.com/article"
                required
                disabled={isLoading}
              />
              <Globe className="absolute left-2 top-2.5 text-white/50 pointer-events-none" size={18} />
            </div>
            {apiError && (
              <p className="mt-2 text-xs text-noir-accent">
                {apiError}
              </p>
            )}
            <p className={`mt-2 text-xs text-white/50 ${manualMode ? 'hidden' : ''}`}>
              Enter the URL of an article. We'll try to extract the title, description, and image automatically.
            </p>
            {!manualMode && (
              <button
                type="button"
                onClick={() => {
                  setManualMode(true);
                  // Optionally, clear URL when switching to manual from default prompt
                  // setUrl(''); 
                  // Optionally, pre-fill some default manual content if desired
                  // setManualContent('Please enter article details...');
                }}
                className="mt-2 text-sm text-noir-accent hover:text-opacity-80 underline"
                disabled={isLoading}
              >
                Or enter details manually
              </button>
            )}
          </div>
          
          {/* Manual Input Fields - Shown only in manual mode */}
          {manualMode && (
            <div className="space-y-3 mb-4">
              <div>
                <label htmlFor="manualTitle" className="block text-white/70 mb-1 text-sm">
                  Title {fetchErrorDetails.title && <span className="text-noir-accent">*</span>}
                </label>
                <input
                  type="text"
                  id="manualTitle"
                  value={manualTitle}
                  onChange={(e) => setManualTitle(e.target.value)}
                  className="w-full bg-noir-200 border border-noir-50 rounded p-2 text-white"
                  placeholder="Enter article title"
                  required // Make title required in manual mode
                  disabled={isLoading}
                />
              </div>
              <div>
                <label htmlFor="manualContent" className="block text-white/70 mb-1 text-sm">
                  Content / Description {fetchErrorDetails.content && <span className="text-noir-accent">*</span>}
                </label>
                <textarea
                  id="manualContent"
                  value={manualContent}
                  onChange={(e) => setManualContent(e.target.value)}
                  className="w-full h-24 bg-noir-200 border border-noir-50 rounded p-2 text-white resize-none"
                  placeholder="Enter article description or notes"
                  disabled={isLoading}
                ></textarea>
              </div>

              {/* Direct File Upload Area */}
              <div>
                <label className="block text-white/70 mb-1 text-sm">
                  Upload Image File (Optional)
                </label>
                <div 
                  className={`border-2 border-dashed rounded-lg p-4 mb-2 flex flex-col items-center justify-center cursor-pointer ${
                    isDraggingFile ? 'border-noir-accent bg-noir-50/20' : 'border-white/20'
                  } hover:border-noir-accent/70`}
                  onDragOver={handleFileDragOver}
                  onDragLeave={handleFileDragLeave}
                  onDrop={handleFileDrop}
                  onClick={triggerFileInput} // Allow click to select file
                >
                  {localPreviewUrl ? (
                    <div className="w-full max-h-40 overflow-hidden flex justify-center">
                      <img 
                        src={localPreviewUrl} 
                        alt="Preview" 
                        className="h-auto max-h-40 object-contain"
                      />
                    </div>
                  ) : (
                    <div className="text-center">
                      <Globe size={32} className="text-white/40 mx-auto mb-1" /> {/* Placeholder Icon */}
                      <p className="text-white/70 text-sm">
                        Drag & drop image here, or click to select
                      </p>
                      {selectedFile && (
                          <p className="text-xs text-noir-accent mt-1 truncate">{selectedFile.name}</p>
                      )}
                    </div>
                  )}
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleFileChange}
                    ref={fileInputRef}
                    className="hidden"
                    disabled={isLoading}
                  />
                </div>
              </div>

            </div>
          )}
          
          <div className="flex justify-end">
            <button
              type="button"
              onClick={onClose}
              className="mr-2 px-4 py-2 text-white/70 hover:text-white"
              disabled={isLoading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-noir-accent text-white rounded hover:bg-opacity-90 flex items-center gap-2"
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 size={16} className="animate-spin mr-2" />
                  {manualMode ? 'Saving...' : loadingStatus}
                </>
              ) : (
                manualMode ? 'Save Article' : 'Add Article'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ArticleForm; 