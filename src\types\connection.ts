/**
 * Types related to connections between board items
 */

import { Position } from './board';

/**
 * Represents a connection between two board items
 */
export interface Connection {
  id: string;
  fromId: string;
  toId: string;
  label?: string;
  style?: ConnectionStyle;
}

/**
 * Visual style options for connections
 */
export type ConnectionStyle = 'solid' | 'dashed' | 'dotted';

/**
 * Represents an endpoint of a connection with its position
 */
export interface ConnectionEndpoint {
  id: string;
  position: Position;
}

/**
 * Props for the Connection component
 */
export interface ConnectionProps {
  id: string;
  from: ConnectionEndpoint;
  to: ConnectionEndpoint;
  isSelected: boolean;
  onSelect: (id: string) => void;
  style?: ConnectionStyle;
  label?: string;
}

/**
 * Factory for creating new connections
 */
export interface ConnectionCreator {
  createConnection: (fromId: string, toId: string) => Connection;
}

/**
 * Manager for handling connections
 */
export interface ConnectionManager {
  getConnections: () => Connection[];
  addConnection: (connection: Connection) => void;
  updateConnection: (id: string, updates: Partial<Connection>) => void;
  deleteConnection: (id: string) => void;
  getConnectionsByItem: (itemId: string) => Connection[];
} 