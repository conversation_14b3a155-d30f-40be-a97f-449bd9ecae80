/**
 * Types related to modal dialogs in the application
 */

/**
 * State for the article form
 */
export interface ArticleFormState {
  title: string;
  url: string;
  content: string;
  imageUrl?: string | null;
  website_url?: string;
  file_url?: string | null;
}

/**
 * Props for the article form modal
 */
export interface ArticleFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (articleData: ArticleFormState) => void;
  initialData?: ArticleFormState;
}

/**
 * Props for the image upload modal
 */
export interface ImageUploadModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (imageData: { file_url: string, alt: string, localImageUrl?: string }) => void;
}

/**
 * Props for the save board modal
 */
export interface SaveBoardModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (boardName: string) => Promise<void>;
  initialBoardName: string;
  isNewBoard: boolean;
}

/**
 * Props for the exit confirmation modal
 */
export interface ExitConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSaveAndExit: () => void;
  onExitWithoutSaving: () => void;
  destination: string;
}

/**
 * State for all modals in the application
 */
export interface ModalState {
  showArticleForm: boolean;
  showSaveModal: boolean;
  showExitConfirmation: boolean;
  showImageUploadModal: boolean;
  exitDestination: string;
  articleForm: ArticleFormState;
}

/**
 * Interface for modal management
 */
export interface ModalManager {
  openArticleForm: (initialData?: ArticleFormState) => void;
  closeArticleForm: () => void;
  openSaveModal: () => void;
  closeSaveModal: () => void;
  openExitConfirmation: (destination: string) => void;
  closeExitConfirmation: () => void;
  openImageUploadModal: () => void;
  closeImageUploadModal: () => void;
  setArticleFormData: (data: Partial<ArticleFormState>) => void;
} 