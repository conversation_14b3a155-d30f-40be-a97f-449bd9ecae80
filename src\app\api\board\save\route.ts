import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import type { Database } from '@/lib/database.types';
import type { Tables } from '@/lib/database.types';

import { getAuthenticatedUser } from '@/utils/authUtils';
// --- Types ---
// Adjust based on frontend data structure
interface ElementPayload {
    id: string; // Frontend temporary ID
    type: string;
    title?: string;
    content?: string;
    url?: string;
    position: { x: number; y: number };
    isAiGenerated?: boolean;
}

interface ConnectionPayload {
    id: string; // Frontend temporary ID
    fromId: string; // Frontend temporary element ID
    toId: string; // Frontend temporary element ID
    type?: string;
    label?: string;
    isAiGenerated?: boolean;
}

interface BoardSavePayload {
    boardId?: string; // Present for updates, absent for creates
    boardName?: string;
    elements: ElementPayload[];
    connections: ConnectionPayload[];
    strokes: any[]; // Add strokes (type as any for now, refine if possible)
}

// Type for board sharing permissions (same as other routes)
interface BoardSharingPermission {
    user_id: string;
    permission_level: string;
}

// Type for the minimal element data needed for ID mapping
interface ElementMappingInfo {
    id: string; // DB ID
    title: string;
    position_x: number;
    position_y: number;
}


export async function POST(request: NextRequest) {
    try {
        // 1. Get User Session
        console.log("Attempting to get user session...");
        const { user, error: authError, supabase: supabaseUserClient } = await getAuthenticatedUser();

        if (authError || !user) {
            console.error("Save Board - User Session Error:", authError);
                return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
        }

            const userId = user.id;

            // 2. Parse Request Body
        const payload = await request.json() as BoardSavePayload;
        const { boardId, boardName, elements, connections, strokes } = payload;

        // --- Add Logging Here ---

        // Validate essential payload parts
        if (!Array.isArray(elements) || !Array.isArray(connections) || !Array.isArray(strokes)) {
             return NextResponse.json({ message: 'Elements, connections, and strokes must be arrays' }, { status: 400 });
        }
        // Ensure frontend IDs are present for mapping (Only if arrays are not empty)
        if (
            elements.length > 0 && elements.some(el => !el.id) || 
            connections.length > 0 && connections.some(c => !c.id || !c.fromId || !c.toId)
        ) {
             return NextResponse.json({ message: 'Frontend IDs (id, fromId, toId) are missing in payload' }, { status: 400 });
        }


        const { data: resultBoardId, error: rpcError } = await supabaseUserClient.rpc(
            'upsert_board_and_elements',
            {
                p_user_id: userId,
                p_board_id: (boardId || null) as any, // Cast to any
                p_board_name: (boardName || null) as any, // Cast to any
                p_elements: elements as any,       // Cast to any
                p_connections: connections as any, // Cast to any
                p_strokes: strokes as any // Pass strokes
            }
        );

        if (rpcError) {
            console.error('RPC Error (upsert_board_and_elements):', rpcError);
            // Check for specific permission error from the function
            if (rpcError.message.includes('does not have permission')) {
                 return NextResponse.json({ message: 'Permission denied to edit this board', error: rpcError.message }, { status: 403 });
            }
            if (rpcError.message.includes('not found')) {
                return NextResponse.json({ message: 'Board not found', error: rpcError.message }, { status: 404 });
            }
            // Generic server error for other RPC issues
            return NextResponse.json({ message: 'Failed to save board', error: rpcError.message }, { status: 500 });
        }

        if (!resultBoardId) {
             console.error('RPC Error: upsert_board_and_elements did not return a board ID.');
             return NextResponse.json({ message: 'Failed to save board (no board ID returned)' }, { status: 500 });
        }

        // 4. Return Success Response
        const message = boardId ? 'Board updated successfully' : 'Board created successfully';
        const status = boardId ? 200 : 201;

        return NextResponse.json({ message, boardId: resultBoardId }, { status });

    } catch (error: any) {
        // Catch errors from JSON parsing or unexpected issues
        console.error('Error processing board save request:', error);
        return NextResponse.json({ message: 'Server error processing board save', error: error.message }, { status: 500 });
    }
}

// Note: The createClient import is necessary for the createClient function used in the code.
// It's not used directly in the POST function, but it's required for the createClient function. 