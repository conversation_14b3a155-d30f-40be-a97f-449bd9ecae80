'use client';

import { useInfiniteQuery } from '@tanstack/react-query';

export interface PublicBoard {
  id: string;
  name: string;
  previewImageUrl: string | null;
  likes: number;
  publicSince: string;
  commentCount?: number;
  totalViews?: number;
}

interface FetchPublicBoardsParams {
  limit?: number;
  searchQuery?: string;
  sortBy?: string;
  sortOrder?: string;
  timePeriod?: string;
}

interface PublicBoardsResponse {
  boards: PublicBoard[];
  pagination: {
    total: number;
    offset: number;
    limit: number;
    hasMore: boolean;
  };
}

/**
 * Fetches public boards with pagination and filtering support
 */
export function usePublicBoards({
  limit = 12,
  searchQuery = '',
  sortBy = 'likes',
  sortOrder = 'desc',
  timePeriod = 'allTime'
}: FetchPublicBoardsParams = {}) {
  return useInfiniteQuery({
    queryKey: ['publicBoards', { searchQuery, sortBy, sortOrder, timePeriod, limit }],
    queryFn: async ({ pageParam = 0 }) => {
      const offset = pageParam;
      const queryParams = new URLSearchParams({
        limit: limit.toString(),
        offset: offset.toString(),
        search: searchQuery,
        sortBy,
        sortOrder,
        timePeriod
      });
      
      const response = await fetch(`/api/board/public?${queryParams}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch public boards');
      }
      
      return await response.json() as PublicBoardsResponse;
    },
    getNextPageParam: (lastPage) => {
      const { pagination } = lastPage;
      if (!pagination.hasMore) return undefined;
      return pagination.offset + pagination.limit;
    },
    initialPageParam: 0
  });
} 