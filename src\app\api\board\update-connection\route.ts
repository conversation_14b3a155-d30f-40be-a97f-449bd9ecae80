import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import type { Database } from '@/lib/database.types';

import { getAuthenticatedUser } from '@/utils/authUtils';
// --- Type for incoming connection data from frontend ---
// Adjust based on your frontend structure
interface ConnectionPayload {
    id?: string; // Optional for 'add', required for 'update'/'delete'
    fromId: string; // Required for 'add'
    toId: string; // Required for 'add'
    type?: string;
    label?: string;
    isAiGenerated?: boolean;
}

// --- Type for the specific board sharing data we query ---
// (Same as in update-elements route)
interface BoardSharingPermission {
    user_id: string; // Note: This corresponds to shared_with_user_id due to the select alias
    permission_level: string;
}

export async function POST(request: NextRequest) {
    try {
    const { user, error: authError, supabase } = await getAuthenticatedUser();
        if (authError || !user) {
            return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
        }
        const userId = user.id;

        // Parse request body
        const requestBody = await request.json();
        const { boardId, action, isBatch } = requestBody;
        
        // Basic validation
        if (!boardId || !action) {
            return NextResponse.json({ message: 'Board ID and action are required' }, { status: 400 });
        }

        // Handle batch operation for connections
        if (isBatch && requestBody.connections && Array.isArray(requestBody.connections)) {
            // Batch operation with connections array
            const connections = requestBody.connections;
            if (connections.length === 0) {
                return NextResponse.json({ message: 'Connections array is empty' }, { status: 400 });
            }
            
            // Process connections in parallel - only 'add' action supported for batch
            if (action !== 'add') {
                return NextResponse.json({ message: 'Batch operations only support "add" action for connections' }, { status: 400 });
            }
            
            // Process all connections
            const processPromises = connections.map(async (connection: ConnectionPayload) => {
                if (!connection.fromId || !connection.toId) {
                    console.warn('Skipping connection with missing fromId or toId');
                    return { error: 'Missing fromId or toId' };
                }
                
                try {
                    const { data, error } = await supabase.rpc(
                        'manage_connection',
                        {
                            p_user_id: userId,
                            p_board_id: boardId,
                            p_action: action,
                            p_connection: {
                                id: connection.id || undefined,
                                fromId: connection.fromId,
                                toId: connection.toId,
                                connection_type: connection.type || 'default',
                                label: connection.label || null,
                                is_ai_generated: connection.isAiGenerated || false
                            }
                        }
                    );
                    
                    if (error) {
                        console.error(`Error in manage_connection RPC:`, error);
                        return { error: error.message };
                    }
                    
                    return data;
                } catch (error) {
                    console.error(`Error processing connection:`, error);
                    return { error: 'Internal server error' };
                }
            });
            
            const results = await Promise.all(processPromises);
            
            // Filter successful results
            const successfulConnections = results.filter(result => !result.error);
            const errorCount = results.length - successfulConnections.length;
            
            if (errorCount > 0) {
                console.error(`Batch connection errors: ${errorCount} of ${connections.length} failed`);
            }
            
            return NextResponse.json({
                message: `Successfully processed ${successfulConnections.length} of ${connections.length} connections`,
                connections: successfulConnections
            }, { status: 200 });
        }

        // Handle single connection operation (legacy path)
        const connection = requestBody.connection;
        if (!connection) {
            return NextResponse.json({ message: 'Connection data is required' }, { status: 400 });
        }

        // Process regular single connection operation
        if (action === 'add' && (!connection.fromId || !connection.toId)) {
            return NextResponse.json({ message: 'fromId and toId are required for adding a connection' }, { status: 400 });
        }

        // Call RPC function
        const { data, error } = await supabase.rpc(
            'manage_connection',
            {
                p_user_id: userId,
                p_board_id: boardId,
                p_action: action,
                p_connection: {
                    id: connection.id || undefined,
                    fromId: connection.fromId, 
                    toId: connection.toId,
                    connection_type: connection.type || 'default',
                    label: connection.label || null,
                    is_ai_generated: connection.isAiGenerated || false
                }
            }
        );

        if (error) {
            console.error(`RPC Error (manage_connection - ${action}):`, error);
            
            // Check for permission errors
            if (error.message.includes('does not have permission')) {
                return NextResponse.json({ message: 'Permission denied to edit this board', error: error.message }, { status: 403 });
            }
            // Check for not found errors
            if (error.message.includes('not found')) {
                return NextResponse.json({ message: 'Connection or board not found', error: error.message }, { status: 404 });
            }
            
            return NextResponse.json({ message: `Failed to ${action} connection`, error: error.message }, { status: 500 });
        }

        return NextResponse.json({ 
            message: `Connection ${action} successful`, 
            connection: data 
        }, { status: 201 });

    } catch (error: any) {
        console.error('Error processing connection update:', error);
            return NextResponse.json({ message: 'Server error', error: error.message }, { status: 500 });
        }
    }