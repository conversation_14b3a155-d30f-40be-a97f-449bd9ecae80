import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import type { Database } from '@/lib/database.types';

export async function GET(request: NextRequest) {
  // Parse userId from query parameters
  const { searchParams } = new URL(request.url);
  const userId = searchParams.get('userId');
  if (!userId) {
    return NextResponse.json({ message: 'Missing userId query parameter' }, { status: 400 });
  }

  // Initialize Supabase client with auth helper
  const cookieStore = cookies();
  const supabase = createRouteHandlerClient<Database>({ cookies: () => cookieStore });

  try {
    // 1. Query board_shares table for shared boards by user
    const { data: shareRows, error: shareError } = await supabase
      .from('board_shares')
      .select('board_id')
      .eq('user_id', userId);

    if (shareError) {
      return NextResponse.json({ message: 'Failed to fetch shared boards', error: shareError.message }, { status: 500 });
    }

    const boardIds = shareRows?.map(row => row.board_id) || [];
    if (boardIds.length === 0) {
      return NextResponse.json({ sharedBoards: [] }, { status: 200 });
    }

    // 2. Query boards table for corresponding boards
    const { data: boards, error: boardsError } = await supabase
      .from('boards')
      .select('id, board_name, created_at, updated_at, preview_image_url, user_id')
      .in('id', boardIds);

    if (boardsError) {
      return NextResponse.json({ message: 'Failed to fetch boards', error: boardsError.message }, { status: 500 });
    }

    // 3. Format shared boards
    const sharedBoards = boards?.map(board => ({
      id: board.id,
      boardName: board.board_name,
      createdAt: board.created_at,
      updatedAt: board.updated_at,
      previewImageUrl: board.preview_image_url,
      ownerId: board.user_id,
    })) || [];

    // 4. Return shared boards
    return NextResponse.json({ sharedBoards }, { status: 200 });
  } catch (error: any) {
    return NextResponse.json({ message: 'An unexpected error occurred', error: error.message }, { status: 500 });
  }
}