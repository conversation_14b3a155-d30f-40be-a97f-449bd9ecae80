import { useState, useCallback, useRef, useEffect } from 'react';
import { 
  BoardItem, 
  Connection, 
  BoardSavePayload,
  UseAutoSaveReturn, 
  PenStroke,
  FullBoardState
} from '../types';
import { boardService } from '../services';
import { uploadBoardPreview } from '../utils/uploadBoardPreview';

// Define debounce function (or import from utils)
const debounce = <T extends (...args: any[]) => any>(func: T, delay: number): (...args: Parameters<T>) => void => {
  let timeoutId: NodeJS.Timeout | null = null;
  return (...args: Parameters<T>) => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    timeoutId = setTimeout(() => {
      func(...args);
    }, delay);
  };
};

/**
 * Custom hook for automatic saving of board changes
 */
export const useAutoSave = (
  boardId?: string,
  boardName: string = 'Untitled board'
): UseAutoSaveReturn => {
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);

  // Internal save function (accepts full state)
  const internalSaveBoard = useCallback(async (
    boardState: FullBoardState
  ): Promise<string | null> => {
    if (!boardId || boardId === 'new' || isSaving) { // Prevent saving if no ID or already saving
      return null;
    }

    setIsSaving(true);
    try {
      const payload: BoardSavePayload = {
        boardId: boardId, // Include ID for update
        boardName: boardName, // Include current name
        elements: boardState.elements,
        connections: boardState.connections,
        strokes: boardState.strokes // Include strokes
      };
      
      const result = await boardService.saveBoard(payload);
      
      if (result.success) {
        setLastSaved(new Date());
        if (result.boardId) {
          // No need to capture preview here, let debounced call handle it
          // capturePreview(true);
        }
        return result.boardId;
      }
      return null;
    } catch (error) {
      console.error('Error auto-saving board:', error);
      return null;
    } finally {
      setIsSaving(false);
    }
  }, [boardId, boardName, isSaving /* Removed capturePreview dependency */]);

  // Debounced version of the internal save function
  const debouncedSaveBoardState = useCallback(
    debounce((boardState: FullBoardState) => {
      // Create a payload with only the properties that are provided
      const payload: BoardSavePayload = {
        boardId: boardId,
        boardName: boardName // Always preserve the board name
      };

      // Only include properties that were passed in boardState
      if (boardState.elements) {
        payload.elements = boardState.elements;
      }
      if (boardState.connections) {
        payload.connections = boardState.connections;
      }
      if (boardState.strokes) {
        payload.strokes = boardState.strokes;
      }
      
      // If this is a strokes-only update (most common with drawing),
      // only send the strokes to avoid overwriting other data
      if (boardState.strokes && !boardState.elements && !boardState.connections) {
        console.log("Handling strokes-only update, preserving other board data");
        if (boardId) {
          boardService.updateStrokesOnly(boardId, boardState.strokes)
            .catch((err: Error) => console.error("Error updating strokes:", err));
          setLastSaved(new Date());
        }
        return;
      }

      // Otherwise, perform full update with available data
      internalSaveBoard(boardState).catch(() => {/* ignore */});
    }, 2000), // 2-second debounce delay
    [internalSaveBoard, boardId, boardName] // Dependencies for the debounced function
  );

  /**
   * Save an element change
   */
  const saveElementChange = useCallback(async (
    element: BoardItem,
    action: 'add' | 'update' | 'delete'
  ): Promise<void> => {
    if (!boardId || boardId === 'new' || isSaving) {
      return;
    }
    
    setIsSaving(true);
    
    try {
      await boardService.updateElement(boardId, element, action);
      setLastSaved(new Date());
      
      // Preview capture throttling moved to DetectiveBoard
    } catch (error) {
      console.error(`Error ${action}ing element:`, error);
    } finally {
      setIsSaving(false);
    }
  }, [boardId, isSaving]);

  /**
   * Save a connection change
   */
  const saveConnectionChange = useCallback(async (
    connection: Connection,
    action: 'add' | 'update' | 'delete'
  ): Promise<void> => {
    if (!boardId || boardId === 'new' || isSaving) {
      return;
    }
    
    setIsSaving(true);
    
    try {
      await boardService.updateConnection(boardId, connection, action);
      setLastSaved(new Date());
      
      // Preview capture throttling moved to DetectiveBoard
    } catch (error) {
      console.error(`Error ${action}ing connection:`, error);
    } finally {
      setIsSaving(false);
    }
  }, [boardId, isSaving]);

  return {
    // Note: The exported saveBoard might need clarification. 
    // Do we still need a way to trigger an *immediate* full save?
    // For now, let's keep the old signature but it won't save strokes unless passed explicitly.
    saveBoard: async () => { 
       console.warn("Direct saveBoard call from useAutoSave doesn't include strokes currently."); 
       // If needed, could call internalSaveBoard with current state here, but requires access to state.
       return null; // Or implement fetching state if necessary
    },
    saveElementChange,
    saveConnectionChange,
    debouncedSaveBoardState, // <-- Expose the debounced function
    isSaving,
    lastSaved
  };
}; 