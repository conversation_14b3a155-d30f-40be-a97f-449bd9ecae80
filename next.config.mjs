/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,
  env: {
    OPEN_ROUTER_API_KEY: process.env.OPEN_ROUTER_API_KEY,
  },
  webpack: (config, { isServer }) => {
    // Directly use punycode2 for both server and client
    config.resolve.alias = {
      ...config.resolve.alias,
      punycode: 'punycode2',
    };
    
    // Increase timeouts for chunk loading
    config.watchOptions = {
      ...config.watchOptions,
      aggregateTimeout: 300,
      poll: 1000,
    };
    
    return config;
  },
  // Redirect from /pages routes to new /app routes
  async redirects() {
    return [
      // Removed self-redirecting paths that were causing infinite redirect loops
    ];
  }
};

export default nextConfig; 