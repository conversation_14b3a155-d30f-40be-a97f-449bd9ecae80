import { NextRequest, NextResponse } from 'next/server';
import type { Database, Tables } from '@/lib/database.types';
import type { User } from '@supabase/supabase-js';
import { getAuthenticatedUser } from '@/utils/authUtils';

// Import shared utilities
import { 
    TransformedElement, 
    TransformedConnection, 
    processElementsInBatches, 
    // getPublicSignedUrlFromEdgeFunction is used internally by processElementsInBatches
} from '@/utils/apiBoardUtils';

export const runtime = 'nodejs';     // make sure this stays a Node.js function
export const maxDuration = 60;       // seconds – pick anything ≤ plan limit

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const boardId = params.id;

  const { searchParams } = new URL(request.url);
  const mode = searchParams.get('mode') || 'complete';
  const skipImages = searchParams.get('skipImages') === 'true';

  if (!boardId) {
    return NextResponse.json({ message: 'Board ID is required' }, { status: 400 });
  }

  try {
    // Get supabase client and user session
    const { user: currentUser, supabase } = await getAuthenticatedUser();
    
    const initialSelect = mode === 'skeleton' ? `
      id, board_name, user_id, created_at, updated_at, strokes,
      elements:elements(id),
      connections:connections(id),
      board_sharing:board_sharing(*)
    ` : `
      *, elements:elements(*), connections:connections(*), board_sharing:board_sharing(*)
    `;

    const { data: rawBoardDataAny, error: boardError } = await supabase.from('boards').select(initialSelect).eq('id', boardId).maybeSingle();

    if (boardError) {
      console.error(`DB error fetching board ${boardId}:`, boardError);
      return NextResponse.json({ message: 'Failed to fetch board data' }, { status: 500 });
    }

    if (!rawBoardDataAny) {
      console.log(`Board ${boardId} not found or access denied.`);
      return NextResponse.json({ message: 'Board not found or access denied' }, { status: 404 });
    }

    const rawBoardData = rawBoardDataAny as any;
    const board = rawBoardData as Tables<'boards'>;
    const elementCount = rawBoardData.elements?.length || 0;
    const connectionCount = rawBoardData.connections?.length || 0;
    const sharing: Tables<'board_sharing'>[] = rawBoardData.board_sharing ?? [];
    const ownerID = board.user_id;
    const isPublic = sharing.some((s: Tables<'board_sharing'>) => s.public_board === true);

    let hasLiked = false;
    if (currentUser?.id) {
      try {
          const { count, error: likeError } = await supabase.from('board_likes').select('id', { count: 'exact', head: true }).eq('user_id', currentUser.id).eq('board_id', boardId);
          if (likeError) console.error(`Like check error:`, likeError);
          else hasLiked = (count ?? 0) > 0;
      } catch(e) { console.error(`Like check exception:`, e); }
    }

    let canEdit = false;
    if (currentUser && board.user_id === currentUser.id) {
        canEdit = true;
    }

    let sharedWith = false;
    let permissionLevel: string | null = null;
    if (currentUser?.id) {
        const { data: shareRows, error: shareError } = await supabase.from('board_shares').select('user_id, permission_level').eq('board_id', boardId).eq('user_id', currentUser.id);
        if (shareError) console.error(`Share check error:`, shareError);
        else if (shareRows && shareRows.length > 0) {
            sharedWith = true;
            permissionLevel = shareRows[0].permission_level ?? null;
        }
    }

    if (mode === 'skeleton') {
        return NextResponse.json({
          id: board.id,
          boardName: board.board_name,
          userId: board.user_id,
          createdAt: board.created_at,
          updatedAt: board.updated_at,
          elementCount,
          connectionCount,
          strokes: board.strokes || [],
          sharing,
          liked: hasLiked,
          isPublic,
          canEdit,
          sharedWith,
          permissionLevel
        }, { status: 200 });
    }

    const elements: Tables<'elements'>[] = rawBoardData.elements ?? [];
    const connectionsData: Tables<'connections'>[] = rawBoardData.connections ?? [];

    const transformedElements = await processElementsInBatches(elements, supabase, boardId, isPublic, currentUser, ownerID, 15, skipImages);
    const transformedConnections: TransformedConnection[] = connectionsData.map(conn => ({
      id: conn.id,
      fromId: conn.from_element_id,
      toId: conn.to_element_id,
      type: conn.connection_type,
      label: conn.label ?? '',
      isAiGenerated: conn.is_ai_generated ?? false,
    }));

    return NextResponse.json({
      id: board.id,
      boardName: board.board_name,
      userId: board.user_id,
      createdAt: board.created_at,
      updatedAt: board.updated_at,
      elements: transformedElements,
      connections: transformedConnections,
      strokes: board.strokes || [],
      sharing,
      liked: hasLiked,
      isPublic,
      canEdit,
      sharedWith,
      permissionLevel
    }, { status: 200 });

  } catch (error: any) {
    console.error(`GET board error ${params.id}:`, error);
    return NextResponse.json({ message: 'Failed to fetch board', error: error.message }, { status: 500 });
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const boardId = params.id;

  console.log(`DELETE request received for board: ${boardId}`); // Add log

  try {
     const { user, error: authError, supabase } = await getAuthenticatedUser();
     if (authError || !user) {
       return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
     }

     // RLS policy on 'boards' for DELETE should enforce ownership.
     // We might need additional logic if related items (elements, connections, sharing) don't cascade delete.
     const { error: deleteError } = await supabase
       .from('boards')
       .delete()
       .eq('id', boardId); // RLS implicitly checks ownership based on session user ID

     if (deleteError) {
       console.error(`Error deleting board ${boardId}:`, deleteError);
       // Possible errors: RLS denial (403 implicitly), not found (no error but 0 rows affected), db error (500)
       // Supabase client might not return specific status codes easily here.
       // We can check error message/code if needed for more specific responses.
       return NextResponse.json({ message: 'Failed to delete board. Check permissions or if board exists.', error: deleteError.message }, { status: 403 }); // Assume permission issue or not found
     }

     console.log(`Board ${boardId} deleted successfully (or did not exist/no permission).`); // Add log
     return NextResponse.json({ message: 'Board deleted successfully' }, { status: 200 }); // Or 204 No Content

  } catch (error: any) {
     console.error(`Unexpected error deleting board ${boardId}:`, error);
     return NextResponse.json({ message: 'An unexpected error occurred during deletion.', error: error.message }, { status: 500 });
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const boardId = params.id;

  try {
     const { user, error: authError, supabase } = await getAuthenticatedUser();
     if (authError || !user) {
       return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
     }

     // Get updated data from request body
     const updates = await request.json();
     const { boardName, ...otherUpdates } = updates; // Example: only allow updating boardName for now

     if (!boardName || typeof boardName !== 'string') {
        return NextResponse.json({ message: 'Invalid data: boardName is required and must be a string.'}, { status: 400});
     }
      // You might add validation for other fields if allowing otherUpdates

     // RLS policy on 'boards' for UPDATE should enforce ownership or edit permissions.
     const { data: updatedData, error: updateError } = await supabase
       .from('boards')
       .update({ board_name: boardName, updated_at: new Date().toISOString() }) // Update specific fields
       .eq('id', boardId) // Filter by board ID
       .select() // Optionally select the updated row
       .single(); // Expect one row to be updated

     if (updateError) {
       console.error(`Error updating board ${boardId}:`, updateError);
        // Handle RLS denial or other errors
       return NextResponse.json({ message: 'Failed to update board. Check permissions or if board exists.', error: updateError.message }, { status: 403 }); // Assume permission/not found
     }

     if (!updatedData) {
         // This might happen if RLS prevented the update but didn't throw an error the client could catch easily
         return NextResponse.json({ message: 'Board not found or update failed.'}, {status: 404});
     }

     return NextResponse.json({ message: 'Board updated successfully', board: updatedData }, { status: 200 });

  } catch (error: any) {
     console.error(`Unexpected error updating board ${boardId}:`, error);
     return NextResponse.json({ message: 'An unexpected error occurred during update.', error: error.message }, { status: 500 });
  }
} 