'use client';

import { useState, useCallback } from 'react';

interface AsyncState<T> {
  data: T | null;
  loading: boolean;
  error: Error | null;
}

/**
 * Custom hook for handling async operations
 */
export function useAsync<T = any, P extends any[] = any[]>(
  asyncFunction: (...params: P) => Promise<T>
) {
  const [state, setState] = useState<AsyncState<T>>({
    data: null,
    loading: false,
    error: null,
  });

  /**
   * Execute the async function and update state accordingly
   */
  const execute = useCallback(
    async (...params: P) => {
      setState({
        data: null,
        loading: true,
        error: null,
      });

      try {
        const data = await asyncFunction(...params);
        setState({
          data,
          loading: false,
          error: null,
        });
        return { success: true, data };
      } catch (error) {
        setState({
          data: null,
          loading: false,
          error: error instanceof Error ? error : new Error(String(error)),
        });
        return { success: false, error };
      }
    },
    [asyncFunction]
  );

  /**
   * Reset state to initial values
   */
  const reset = useCallback(() => {
    setState({
      data: null,
      loading: false,
      error: null,
    });
  }, []);

  /**
   * Set data directly (useful for optimistic updates)
   */
  const setData = useCallback((data: T | null) => {
    setState((prevState) => ({
      ...prevState,
      data,
    }));
  }, []);

  return {
    ...state,
    execute,
    reset,
    setData,
  };
} 