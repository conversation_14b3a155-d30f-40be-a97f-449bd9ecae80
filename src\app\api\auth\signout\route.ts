import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';
import type { Database } from '@/lib/database.types'; // Ensure path is correct

export async function POST(request: NextRequest) {
  const cookieStore = cookies();
  // Create client using the request context
  const supabase = createRouteHandlerClient<Database>({ cookies: () => cookieStore });

  try {
    // Call signOut. This primarily invalidates the session on the server
    // and tells the helper to clear the session cookies in the response.
    const { error } = await supabase.auth.signOut();

    if (error) {
      console.error('API SignOut Error:', error);
      return NextResponse.json({ error: 'Failed to sign out', details: error.message }, { status: 500 });
    }

    // The route handler client automatically handles clearing the cookies
    // via the response headers when signOut is called.
    return NextResponse.json({ message: 'Signed out successfully' }, { status: 200 });

  } catch (e: any) {
    console.error('API SignOut Unexpected Error:', e);
    return NextResponse.json({ error: 'An unexpected error occurred during sign out', details: e.message }, { status: 500 });
  }
}

// Add a GET handler as well, as sometimes sign-out might be triggered by navigation
// Though POST is generally preferred for actions with side effects.
export async function GET(request: NextRequest) {
  // You might want different logic for GET, but for simplicity now:
  console.warn('Sign out called via GET request. Using POST logic.');
  return POST(request); // Re-use the POST logic
}