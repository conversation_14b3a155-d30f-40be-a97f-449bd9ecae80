import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import type { Database } from '@/lib/database.types';
import type { Tables } from '@/lib/database.types';

import { getAuthenticatedUser } from '@/utils/authUtils';
// Helper to extract relative path (e.g., "uuid/filename.png") from various URL formats
function extractRelativePath(url: string | null): string | null {
  if (!url) return null;
  try {
    // Check if it's already a relative path (doesn't start with http)
    if (!url.startsWith('http')) {
      if (url.includes('/')) {
        return url;
      } else {
        console.warn(`Provided path is not a URL and doesn't seem relative: ${url}`);
        return null;
      }
    }

    // If it's a full URL, parse it
    const parsedUrl = new URL(url);
    const pathSegments = parsedUrl.pathname.split('/');

    // Find the bucket name ('images' in this case)
    const bucketName = 'images';
    const bucketIndex = pathSegments.indexOf(bucketName);

    if (bucketIndex !== -1 && bucketIndex < pathSegments.length - 1) {
      // Join the segments after the bucket name
      return pathSegments.slice(bucketIndex + 1).join('/');
    }

    console.warn(`Could not extract relative path from URL: ${url}`);
    return null;
  } catch (error) {
    console.error(`Error parsing URL for relative path extraction: ${url}`, error);
    return null;
  }
}

// Helper function to call the edge function to get a public signed URL
async function getPublicSignedUrl(boardId: string, urlInput: string | null, userId?: string | null): Promise<string | null> {
  const relativePath = extractRelativePath(urlInput);
  if (!relativePath) {
    console.warn(`Skipping edge function call due to invalid path: ${urlInput}`);
    return null;
  }

  const edgeFunctionUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
    ? `${process.env.NEXT_PUBLIC_SUPABASE_URL}/functions/v1/get-public-image-url`
    : null;

  if (!edgeFunctionUrl) {
    console.error("Edge function URL (get-public-image-url) is not configured.");
    return null;
  }

  const anonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
  if (!anonKey) {
    console.error("Supabase Anon Key (NEXT_PUBLIC_SUPABASE_ANON_KEY) is not configured.");
    return null;
  }

  try {
    const response = await fetch(edgeFunctionUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${anonKey}`,
        'apikey': anonKey
      },
      body: JSON.stringify({ boardId, filePath: relativePath, userId }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: 'Failed to parse edge function error response' }));
      console.error(`Edge function call failed for board ${boardId}, path ${relativePath}:`, errorData.error || response.statusText);
      return null;
    }

    const data = await response.json();
    return data.signedUrl || null;

  } catch (error) {
    console.error(`Error calling edge function for board ${boardId}, path ${relativePath}:`, error);
    return null;
  }
}

// Get a preview of a board
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
  const { searchParams } = new URL(request.url);
  const boardId = searchParams.get('boardId');
  
  if (!boardId) {
    return NextResponse.json({ error: 'Board ID is required' }, { status: 400 });
  }

  const { user, error: authError, supabase } = await getAuthenticatedUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if the user has access to the board (either owner or shared with)
    const { data: boardAccess, error: accessError } = await supabase
      .from('boards')
      .select(`
        id,
        user_id,
        board_name,
        preview_image_url,
        board_shares!inner(user_id, permission_level)
      `)
      .eq('id', boardId)
      .or(`user_id.eq.${user.id},board_shares.user_id.eq.${user.id}`)
      .maybeSingle();

    // If no direct board found, check sharing table separately
    // (This accounts for a bug where the inner join above might not work correctly)
    let hasAccess = false;
    let board = null;

    if (!boardAccess || accessError) {
      // Try first to see if user is owner
      const { data: ownedBoard } = await supabase
        .from('boards')
        .select('id, user_id, board_name, preview_image_url')
        .eq('id', boardId)
        .eq('user_id', user.id)
        .maybeSingle();

      if (ownedBoard) {
        hasAccess = true;
        board = ownedBoard;
      } else {
        // Check if shared
        const { data: sharedAccess } = await supabase
          .from('board_shares')
          .select('board_id, user_id, permission_level')
          .eq('board_id', boardId)
          .eq('user_id', user.id)
          .maybeSingle();

        if (sharedAccess) {
          hasAccess = true;
          // Get the actual board
          const { data: sharedBoard } = await supabase
            .from('boards')
            .select('id, user_id, board_name, preview_image_url')
            .eq('id', boardId)
            .maybeSingle();
          
          board = sharedBoard;
        }
      }
    } else {
      hasAccess = true;
      board = boardAccess;
    }

    if (!hasAccess || !board) {
      return NextResponse.json({ error: 'Board not found or access denied' }, { status: 404 });
    }

    // Generate a signed URL for the preview image if it exists
    let signedPreviewUrl: string | null = null;
    if (board.preview_image_url) {
      try {
        // Extract the file path from the URL
        const pathMatch = board.preview_image_url.match(/\/images\/(.+)$/);
        if (pathMatch && pathMatch[1]) {
          const filePath = pathMatch[1];
          if (board.user_id === user.id) {
            // Owner: create a signed URL with 1 hour expiry
            console.log('filePath (owner)', filePath);
            const { data: signedUrlData, error: signedUrlError } = await supabase
              .storage
              .from('images')
              .createSignedUrl(filePath, 3600);
            if (signedUrlError) {
              console.error(`Error generating signed URL for preview: ${signedUrlError.message}`);
              signedPreviewUrl = board.preview_image_url;
            } else if (signedUrlData) {
              signedPreviewUrl = signedUrlData.signedUrl;
            }
          } else {
            // Shared: use edge function to retrieve a public signed URL
            console.log('Using edge function for shared preview URL', filePath);
            const url = await getPublicSignedUrl(board.id, board.preview_image_url, user.id);
            signedPreviewUrl = url || board.preview_image_url;
          }
        } else {
          console.warn(`Could not extract path from preview URL: ${board.preview_image_url}`);
          signedPreviewUrl = board.preview_image_url;
        }
      } catch (error) {
        console.error('Error generating signed URL or fallback:', error);
        signedPreviewUrl = board.preview_image_url;
      }
    }

    // Return the board data with the signed preview image URL
    return NextResponse.json({
      id: board.id,
      boardName: board.board_name,
      previewImageUrl: signedPreviewUrl
    }, { status: 200 });

  } catch (error) {
    console.error('Error fetching board preview data:', error);
    return NextResponse.json({ error: 'Server error' }, { status: 500 });
  }
} 