'use client';

import React from 'react';
import { twMerge } from 'tailwind-merge';

interface MainLayoutProps {
  children: React.ReactNode;
  className?: string;
}

const MainLayout: React.FC<MainLayoutProps> = ({ children, className = '' }) => {
  return (
    <div className="min-h-screen bg-noir film-grain">
      <main className={twMerge("relative", className)}>
        {children}
      </main>
    </div>
  );
};

export default MainLayout; 