// src/hooks/usePenTool.ts
import { useState, useCallback, useRef } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { Position, PenStroke } from '../types/board'; // Assuming types are in board.ts
import { UsePenToolReturn } from '../types'; // Update import

interface UsePenToolProps {
  addStroke: (stroke: PenStroke) => void; // Function to add the completed stroke to the board state
  removeStrokesNearPoint: (point: Position, radius: number) => void; // <-- Add prop
  boardRef: React.RefObject<HTMLDivElement>; // Ref to the board container for coordinate calculations
  scale: number; // Current zoom scale of the board
  offset: Position; // Current pan offset of the board
  isErasing: boolean; // <-- Add isErasing prop
  strokes: PenStroke[]; // <-- Add strokes array prop
}

// Default values
const DEFAULT_PEN_COLOR = '#000000'; // Black
const DEFAULT_STROKE_WIDTH = 2;

// Helper function for distance (if not already present)
const distSq = (p1: Position, p2: Position): number => {
  return Math.pow(p1.x - p2.x, 2) + Math.pow(p1.y - p2.y, 2);
};

export const usePenTool = ({
  addStroke,
  removeStrokesNearPoint, // <-- Destructure prop
  boardRef,
  scale,
  offset,
  isErasing, // <-- Destructure isErasing prop
  strokes // <-- Destructure strokes prop
}: UsePenToolProps): UsePenToolReturn => {
  const [isDrawing, setIsDrawing] = useState(false); // For pen or eraser active drag
  const [currentStroke, setCurrentStroke] = useState<PenStroke | null>(null);
  const currentPointsRef = useRef<Position[]>([]);

  // Add state for pen properties
  const [penColor, setPenColor] = useState<string>(DEFAULT_PEN_COLOR);
  const [penStrokeWidth, setPenStrokeWidth] = useState<number>(DEFAULT_STROKE_WIDTH);
  const [hoveredStrokeId, setHoveredStrokeId] = useState<string | null>(null); // <-- Add hover state

  // Toggle Eraser Mode
  const toggleEraserMode = useCallback(() => {
    // No need to change isErasing state, it's already passed as a prop
  }, []);

  // Helper to convert screen coordinates to board coordinates
  const getBoardCoordinates = useCallback((event: React.PointerEvent): Position | null => {
    if (!boardRef.current) return null;
    const rect = boardRef.current.getBoundingClientRect();
    const x = (event.clientX - rect.left - offset.x) / scale;
    const y = (event.clientY - rect.top - offset.y) / scale;
    return { x, y };
  }, [boardRef, scale, offset]);

  const handlePointerDown = useCallback((event: React.PointerEvent) => {
    if (event.button !== 0) return;
    event.preventDefault();
    event.stopPropagation();

    const point = getBoardCoordinates(event);
    if (!point) return;

    setIsDrawing(true); // Generic drawing/erasing active flag
    (event.target as Element).setPointerCapture(event.pointerId);

    if (isErasing) {
      // Start erasing immediately on down press
      // Use penStrokeWidth as the base radius, maybe scaled slightly
      const eraserRadius = penStrokeWidth * 1.5 / scale; // Adjust multiplier as needed, account for scale
      removeStrokesNearPoint(point, eraserRadius);
    } else {
      // Start drawing a stroke
      currentPointsRef.current = [point];
      setCurrentStroke({
        id: `drawing-${uuidv4()}`,
        points: [point],
        color: penColor, // Use state
        strokeWidth: penStrokeWidth, // Use state
      });
    }

  }, [getBoardCoordinates, penColor, penStrokeWidth, isErasing, removeStrokesNearPoint, scale]); // Add eraser state/func/scale

  const handlePointerMove = useCallback((event: React.PointerEvent) => {
    if (!isDrawing) return;
    event.preventDefault();
    event.stopPropagation();

    const point = getBoardCoordinates(event);
    if (!point) return;

    if (isErasing) {
      const eraserRadius = penStrokeWidth * 1.5 / scale;
      const radiusSq = eraserRadius * eraserRadius;
      let foundHover: string | null = null;
      // Find the first intersecting stroke
      for (const stroke of strokes) {
          const intersects = stroke.points.some(strokePoint => 
              distSq(strokePoint, point) <= radiusSq
          );
          if (intersects) {
              foundHover = stroke.id;
              break; // Stop after finding the first one
          }
      }
      // Update hovered state only if it changed
      if (foundHover !== hoveredStrokeId) {
         setHoveredStrokeId(foundHover);
      }
      // Still perform erasing action
      removeStrokesNearPoint(point, eraserRadius);
    } else {
      // Update drawing stroke
      currentPointsRef.current = [...currentPointsRef.current, point];
      setCurrentStroke(prevStroke => {
        if (!prevStroke) return null; // Should not happen if isDrawing is true
        return {
          ...prevStroke,
          points: [...prevStroke.points, point]
        };
      });
       setHoveredStrokeId(null); // Ensure hover state is cleared when drawing
    }

  }, [isDrawing, getBoardCoordinates, isErasing, removeStrokesNearPoint, penStrokeWidth, scale, strokes, hoveredStrokeId]); // Add strokes and hoveredStrokeId

  const handlePointerUp = useCallback((event: React.PointerEvent) => {
    if (!isDrawing || event.button !== 0) return;
    event.preventDefault();
    event.stopPropagation();

    setIsDrawing(false);
    (event.target as Element).releasePointerCapture(event.pointerId);

    if (!isErasing && currentStroke && currentStroke.points.length > 1) {
      // Finish drawing: Save the stroke
      const finalStroke: PenStroke = { ...currentStroke, id: uuidv4() };
      addStroke(finalStroke);
    }
    
    // Clear temporary drawing stroke regardless
    setCurrentStroke(null);
    currentPointsRef.current = [];
    setHoveredStrokeId(null); // Clear hover on pointer up

  }, [isDrawing, addStroke, currentStroke, isErasing]); // Add isErasing

  return {
    isDrawing,
    isErasing, // <-- Return erasing state
    toggleEraserMode, // <-- Return toggle function
    currentStroke,
    penColor,
    penStrokeWidth,
    setPenColor,
    setPenStrokeWidth,
    handlePointerDown,
    handlePointerMove,
    handlePointerUp,
    hoveredStrokeId // <-- Return hover state
  };
};

export * from './usePenTool';