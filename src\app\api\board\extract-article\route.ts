import { NextResponse } from 'next/server';
import axios from 'axios';
import * as cheerio from 'cheerio';

type ArticleData = {
  title: string;
  description: string;
  imageUrl: string | null;
  domain: string;
  url: string;
  website_url: string;
};

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { url } = body;

    if (!url || typeof url !== 'string') {
      return NextResponse.json(
        { message: 'URL is required' },
        { status: 400 }
      );
    }

    console.log(`Extracting data from: ${url}`);
    
    // Extract article data
    const articleData = await extractArticleData(url);
    
    console.log('Extracted article data:', articleData);
    
    return NextResponse.json(articleData);
  } catch (error: any) {
    console.error('Error extracting article data:', error.message);
    return NextResponse.json(
      { message: 'Failed to extract article data', error: error.message },
      { status: 500 }
    );
  }
}

async function extractArticleData(url: string): Promise<ArticleData> {
  try {
    // Parse URL to get domain
    const urlObj = new URL(url);
    const domain = urlObj.hostname.replace('www.', '');
    
    // Fetch the URL content
    const response = await axios.get(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      },
    });
    
    const html = response.data;
    const $ = cheerio.load(html);
    
    // Extract title - try open graph title first, then regular title
    let title = $('meta[property="og:title"]').attr('content') || 
                $('meta[name="twitter:title"]').attr('content') ||
                $('title').text() || 
                '';
    
    // Clean up title
    title = title.trim();
    
    // Extract description
    let description = $('meta[property="og:description"]').attr('content') || 
                      $('meta[name="twitter:description"]').attr('content') ||
                      $('meta[name="description"]').attr('content') || 
                      '';
    
    // Clean up description
    description = description.trim();
    
    // Extract image URL - first try open graph image, then twitter image
    let imageUrl = $('meta[property="og:image"]').attr('content') ||
                   $('meta[name="twitter:image"]').attr('content') ||
                   null;
    
    // If no OG image, look for the first large image
    if (!imageUrl) {
      $('img').each((i, img) => {
        const src = $(img).attr('src');
        const width = parseInt($(img).attr('width') || '0');
        const height = parseInt($(img).attr('height') || '0');
        
        // Only use reasonably sized images
        if (src && ((width > 200 && height > 200) || (!width && !height))) {
          // Make relative URLs absolute
          imageUrl = src.startsWith('http') ? src : new URL(src, url).href;
          return false; // Break the loop
        }
      });
    } else if (imageUrl && !imageUrl.startsWith('http')) {
      // Make relative URLs absolute
      imageUrl = new URL(imageUrl, url).href;
    }
    
    // If all else fails, use the domain as title
    if (!title) {
      title = domain.charAt(0).toUpperCase() + domain.slice(1);
    }
    
    return {
      title,
      description,
      imageUrl,
      domain,
      url,
      website_url: url
    };
  } catch (error) {
    console.error('Error in extractArticleData:', error);
    throw error;
  }
} 