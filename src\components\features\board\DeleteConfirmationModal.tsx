import React from 'react';
import { X, AlertTriangle } from 'lucide-react';
import { Modal } from '@/components/ui/modal';
import { Button } from '@/components/ui/button';

interface DeleteConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  boardName: string;
  isDeleting: boolean;
}

/**
 * Modal for confirming deletion of a board
 */
const DeleteConfirmationModal: React.FC<DeleteConfirmationModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  boardName,
  isDeleting
}) => {
  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Delete Board">
      <div className="py-2">
        <div className="flex items-center justify-center mb-4 text-red-500">
          <AlertTriangle size={48} />
        </div>
        
        <p className="text-white/90 text-center mb-2">
          Are you sure you want to delete <span className="font-bold text-white">{boardName}</span>?
        </p>
        
        <p className="text-white/70 text-center mb-6 text-sm">
          This action cannot be undone. All content and connections will be permanently removed.
        </p>
        
        <div className="flex justify-end space-x-3 mt-6">
          <Button
            variant="outline"
            onClick={onClose}
            disabled={isDeleting}
            className="border-noir-100 text-white hover:bg-noir-100"
          >
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={onConfirm}
            disabled={isDeleting}
            className="bg-red-600 hover:bg-red-700 text-white"
          >
            {isDeleting ? (
              <>
                <span className="mr-2">Deleting</span>
                <span className="animate-spin">
                  <svg className="h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                </span>
              </>
            ) : (
              "Delete Board"
            )}
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default DeleteConfirmationModal; 