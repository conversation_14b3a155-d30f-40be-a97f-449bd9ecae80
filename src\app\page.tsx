'use client';

import React, { useState } from 'react';
import MainLayout from '@/components/layout/MainLayout';
import Dashboard from '@/components/features/dashboard/Dashboard';
import { VerificationModal } from '@/components/features/auth/VerificationModal';

export default function HomePage() {
  const [isVerificationModalOpen, setIsVerificationModalOpen] = useState(false);
  const [verificationEmail, setVerificationEmail] = useState('');
  const [verificationUserId, setVerificationUserId] = useState<string | undefined>(undefined);
  
  const handleVerificationNeeded = (email: string, userId?: string) => {
    setVerificationEmail(email);
    setVerificationUserId(userId);
    setIsVerificationModalOpen(true);
  };

  const handleVerificationSuccess = () => {
    // Reload the page to refresh the authentication state
    window.location.reload();
  };

  return (
    <MainLayout>
      <Dashboard />
      
      {isVerificationModalOpen && (
        <VerificationModal
          isOpen={isVerificationModalOpen}
          onClose={() => setIsVerificationModalOpen(false)}
          email={verificationEmail}
          userId={verificationUserId}
          onSuccess={handleVerificationSuccess}
        />
      )}
    </MainLayout>
  );
} 