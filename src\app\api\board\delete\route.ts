import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import type { Database } from '@/lib/database.types';

import { getAuthenticatedUser } from '@/utils/authUtils';
// Use the same bucket name constant as the dedicated image deletion route
const BUCKET_NAME = 'images';

export async function DELETE(request: NextRequest) {
  try {
    // Get authenticated user
    const { user, error: authError, supabase } = await getAuthenticatedUser();

    if (authError || !user) {
      console.warn('DELETE /api/board/delete: Unauthorized access attempt.');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get boardId from query params
    const { searchParams } = new URL(request.url);
    const boardId = searchParams.get('boardId');

    if (!boardId) {
      return NextResponse.json({ error: 'Board ID is required' }, { status: 400 });
    }
    console.log(`DELETE /api/board/delete: Attempting deletion for board ${boardId} by user ${user.id}`);


    // Check if the user owns the board
    const { data: board, error: boardError } = await supabase
      .from('boards')
      .select('user_id, preview_image_url')
      .eq('id', boardId)
      .single();

    if (boardError) {
      console.error(`DELETE /api/board/delete: Error fetching board ${boardId}:`, boardError);
      return NextResponse.json({ error: 'Failed to fetch board' }, { status: 500 });
    }

    if (!board) {
        console.warn(`DELETE /api/board/delete: Board ${boardId} not found.`);
      return NextResponse.json({ error: 'Board not found' }, { status: 404 });
    }

    // Log the entire board object to see what we got
    // console.log('Board data from database:', JSON.stringify(board, null, 2)); // Keep commented unless debugging

    // Only allow deletion if user is the owner
    if (board.user_id !== user.id) {
        console.warn(`DELETE /api/board/delete: Permission denied for user ${user.id} on board ${boardId} (owner: ${board.user_id})`);
      return NextResponse.json({ error: 'Permission denied. Only the board owner can delete it.' }, { status: 403 });
    }

    // --- Start File Deletion Logic ---
    const fileResults = {
      total: 0,
      deleted: 0,
      failed: 0,
      boardPreview: null as { success: boolean; path: string; error?: unknown } | null,
      elementFiles: {
          total: 0,
          deleted: 0,
          failed: 0,
          errors: [] as { path: string; error: unknown }[]
      }
    };

    // Debug log the board preview image URL
    console.log(`DELETE /api/board/delete: Board ${boardId} preview_image_url: ${board.preview_image_url}`);

    // Delete board preview image if it exists
    if (board.preview_image_url) {
      let previewPath = board.preview_image_url as string;
      const originalPreviewPath = previewPath; // Keep original for error reporting

      try {
          console.log(`DELETE /api/board/delete: Processing preview image path: ${previewPath}`);
          
          // Simplified Path Extraction Logic (prefer this)
          // Assumes URLs are like: https://<project>.supabase.co/storage/v1/object/public/images/<user_id>/<filename>
          // Or potentially just: <user_id>/<filename> if stored differently
          const urlParts = previewPath.split('/' + BUCKET_NAME + '/');
          if (urlParts.length > 1) {
              previewPath = decodeURIComponent(urlParts[1].split('?')[0]); // Get path after bucket name, remove query params
              console.log(`DELETE /api/board/delete: Extracted preview path: ${previewPath}`);
          } else if (!previewPath.includes('/')) {
              // Maybe it's just a filename? Or user_id/filename? Needs context.
              // Let's assume it might be relative path if no standard URL structure.
               console.log(`DELETE /api/board/delete: Preview path "${previewPath}" doesn't contain expected structure. Using as is.`);
          }
          // If it starts with the bucket name (unlikely but possible), remove it
          if (previewPath.startsWith(BUCKET_NAME + '/')) {
              previewPath = previewPath.substring(BUCKET_NAME.length + 1);
              console.log(`DELETE /api/board/delete: Removed leading bucket name from preview path: ${previewPath}`);
          }


          fileResults.total += 1; // Count the preview image attempt

          console.log(`DELETE /api/board/delete: Attempting to delete preview file: ${previewPath}`);
          const { error: deleteError } = await supabase
              .storage
              .from(BUCKET_NAME)
              .remove([previewPath]);

          if (deleteError) {
              // Supabase returns a 400 error usually if the file is not found, treat this as 'success' in deletion context
              if (deleteError.message?.includes('Not Found') || (deleteError as any).statusCode === 400) {
                   console.log(`DELETE /api/board/delete: Preview file "${previewPath}" not found (or deletion successful), assuming already deleted or path mismatch.`);
                   fileResults.deleted += 1;
                   fileResults.boardPreview = { success: true, path: previewPath };
              } else {
                  console.error(`DELETE /api/board/delete: Error deleting board preview image "${previewPath}":`, deleteError);
                  fileResults.failed += 1;
                  fileResults.boardPreview = { success: false, path: previewPath, error: deleteError };
              }
          } else {
              console.log(`DELETE /api/board/delete: Successfully deleted preview file: ${previewPath}`);
              fileResults.deleted += 1;
              fileResults.boardPreview = { success: true, path: previewPath };
          }
      } catch (err) {
          console.error(`DELETE /api/board/delete: Exception deleting board preview image "${originalPreviewPath}" (Processed path: ${previewPath}):`, err);
          fileResults.failed += 1;
          fileResults.boardPreview = { success: false, path: previewPath, error: err };
      }
    }


    // Fetch all elements with file_url to delete their files
    const { data: fileElements, error: fileElementsError } = await supabase
      .from('elements')
      .select('id, element_type, file_url')
      .eq('board_id', boardId)
      .in('element_type', ['image', 'article']) // Consider other types if they can have files
      .not('file_url', 'is', null);

    if (fileElementsError) {
      console.error(`DELETE /api/board/delete: Error fetching element files for board ${boardId}:`, fileElementsError);
      // Non-fatal: Log and continue with database deletion
    }

    fileResults.elementFiles.total = fileElements?.length || 0;

    // Delete element files from storage if any were found
    if (fileElements && fileElements.length > 0) {
      console.log(`DELETE /api/board/delete: Found ${fileElements.length} element files to potentially delete for board ${boardId}.`);

      const deletePromises = fileElements
        .filter(elem => elem.file_url) // Ensure file_url exists
        .map(async (elem) => {
          let filePath = elem.file_url as string;
          const originalFilePath = filePath; // Keep original for error reporting

          try {
              console.log(`DELETE /api/board/delete: [Element ${elem.id}] Processing file path: ${filePath}`);
              
              // Simplified Path Extraction Logic
              const urlParts = filePath.split('/' + BUCKET_NAME + '/');
               if (urlParts.length > 1) {
                  filePath = decodeURIComponent(urlParts[1].split('?')[0]);
                  console.log(`DELETE /api/board/delete: [Element ${elem.id}] Extracted file path: ${filePath}`);
              } else if (!filePath.includes('/')) {
                  console.log(`DELETE /api/board/delete: [Element ${elem.id}] Path "${filePath}" doesn't contain expected structure. Using as is.`);
              }
              if (filePath.startsWith(BUCKET_NAME + '/')) {
                  filePath = filePath.substring(BUCKET_NAME.length + 1);
                  console.log(`DELETE /api/board/delete: [Element ${elem.id}] Removed leading bucket name: ${filePath}`);
              }

              console.log(`DELETE /api/board/delete: [Element ${elem.id}] Attempting to delete file: ${filePath}`);
              const { error: deleteError } = await supabase
                  .storage
                  .from(BUCKET_NAME)
                  .remove([filePath]);

              if (deleteError) {
                   if (deleteError.message?.includes('Not Found') || (deleteError as any).statusCode === 400) {
                       console.log(`DELETE /api/board/delete: [Element ${elem.id}] File "${filePath}" not found, assuming already deleted.`);
                       fileResults.elementFiles.deleted += 1;
                  } else {
                      console.error(`DELETE /api/board/delete: [Element ${elem.id}] Error deleting file "${filePath}":`, deleteError);
                      fileResults.elementFiles.failed += 1;
                      fileResults.elementFiles.errors.push({ path: filePath, error: deleteError });
                   }
              } else {
                  console.log(`DELETE /api/board/delete: [Element ${elem.id}] Successfully deleted file: ${filePath}`);
                  fileResults.elementFiles.deleted += 1;
              }
          } catch (err) {
              console.error(`DELETE /api/board/delete: [Element ${elem.id}] Exception deleting file "${originalFilePath}" (Processed: ${filePath}):`, err);
              fileResults.elementFiles.failed += 1;
              fileResults.elementFiles.errors.push({ path: filePath, error: err });
          }
        });

      // Wait for all storage deletions to attempt
      await Promise.all(deletePromises);
      console.log(`DELETE /api/board/delete: Finished attempting element file deletions for board ${boardId}. Results: ${fileResults.elementFiles.deleted} deleted, ${fileResults.elementFiles.failed} failed.`);
    }
    // --- End File Deletion Logic ---

    // --- Start Database Deletion Logic ---
    // NOTE: Ensure RLS policies allow the user to delete related items,
    // or consider using a service_role client if necessary, or rely on CASCADE deletes.

    console.log(`DELETE /api/board/delete: Starting database record deletion for board ${boardId}.`);

    // 1. Get Comment IDs associated with the board
    const { data: comments, error: commentFetchError } = await supabase
      .from('comments')
      .select('id')
      .eq('board_id', boardId);

    if (commentFetchError) {
        console.error(`DELETE /api/board/delete: Failed to fetch comments for board ${boardId}:`, commentFetchError);
        // Decide if this is fatal. If reactions MUST be deleted, maybe throw. Otherwise, continue.
        // Let's continue for now.
    }

    // 2. Delete Comment Reactions if comments were found
    if (comments && comments.length > 0) {
        const commentIds = comments.map(c => c.id);
        console.log(`DELETE /api/board/delete: Found ${commentIds.length} comments for board ${boardId}. Deleting associated reactions.`);
        const { error: reactionDeleteError } = await supabase
            .from('comment_reactions')
            .delete()
            .in('comment_id', commentIds);

        if (reactionDeleteError) {
            console.error(`DELETE /api/board/delete: Failed to delete comment reactions for board ${boardId} (comments: ${commentIds.join(', ')}):`, reactionDeleteError);
            // Non-fatal, continue deletion process
        } else {
            console.log(`DELETE /api/board/delete: Successfully deleted comment reactions for board ${boardId}.`);
        }
    } else {
        console.log(`DELETE /api/board/delete: No comments found for board ${boardId}, skipping reaction deletion.`);
    }

    // 3. Delete Comments
    const { error: commentDeleteError } = await supabase
        .from('comments')
        .delete()
        .eq('board_id', boardId);
     if (commentDeleteError) {
        console.error(`DELETE /api/board/delete: Failed to delete comments for board ${boardId}:`, commentDeleteError);
        // Non-fatal
    } else {
        console.log(`DELETE /api/board/delete: Successfully deleted comments for board ${boardId}.`);
    }


    // 4. Delete Board Sharing entries
     const { error: sharingDeleteError } = await supabase
        .from('board_sharing')
        .delete()
        .eq('board_id', boardId);
    if (sharingDeleteError) {
        console.error(`DELETE /api/board/delete: Failed to delete board_sharing for board ${boardId}:`, sharingDeleteError);
        // Non-fatal
    } else {
        console.log(`DELETE /api/board/delete: Successfully deleted board_sharing for board ${boardId}.`);
    }

    // 5. Delete Board Views
    const { error: viewsDeleteError } = await supabase
        .from('board_views')
        .delete()
        .eq('board_id', boardId);
    if (viewsDeleteError) {
        console.error(`DELETE /api/board/delete: Failed to delete board_views for board ${boardId}:`, viewsDeleteError);
        // Non-fatal
    } else {
        console.log(`DELETE /api/board/delete: Successfully deleted board_views for board ${boardId}.`);
    }

    // 6. Delete Connections
    const { error: connectionsDeleteError } = await supabase
        .from('connections')
        .delete()
        .eq('board_id', boardId);
    if (connectionsDeleteError) {
        console.error(`DELETE /api/board/delete: Failed to delete connections for board ${boardId}:`, connectionsDeleteError);
        // Non-fatal
    } else {
        console.log(`DELETE /api/board/delete: Successfully deleted connections for board ${boardId}.`);
    }

    // 7. Delete Elements
    const { error: elementsDeleteError } = await supabase
        .from('elements')
        .delete()
        .eq('board_id', boardId);
     if (elementsDeleteError) {
        console.error(`DELETE /api/board/delete: Failed to delete elements for board ${boardId}:`, elementsDeleteError);
        // Non-fatal
    } else {
        console.log(`DELETE /api/board/delete: Successfully deleted elements for board ${boardId}.`);
    }

    // 8. Finally, delete the board itself
    console.log(`DELETE /api/board/delete: Proceeding to delete board record ${boardId}.`);
    const { error: deleteBoardError } = await supabase
      .from('boards')
      .delete()
      .eq('id', boardId);

    if (deleteBoardError) {
      console.error(`DELETE /api/board/delete: Failed to delete the main board record ${boardId}:`, deleteBoardError);
      // This IS likely fatal for the operation's goal
      return NextResponse.json({ error: 'Failed to delete board record after attempting cleanup' }, { status: 500 });
    }
    // --- End Database Deletion Logic ---

    console.log(`DELETE /api/board/delete: Successfully deleted board ${boardId} and related data/files.`);
    return NextResponse.json({ message: 'Board deleted successfully', fileResults }, { status: 200 });

  } catch (err) {
    console.error('DELETE /api/board/delete: Unexpected error during board deletion:', err);
    // Extract boardId if possible for logging
    const boardId = new URL(request.url).searchParams.get('boardId');
    console.error(`DELETE /api/board/delete: Error context - Board ID was: ${boardId || 'Not Found'}`);
    return NextResponse.json({ error: 'Internal Server Error during board deletion' }, { status: 500 });
  }
}