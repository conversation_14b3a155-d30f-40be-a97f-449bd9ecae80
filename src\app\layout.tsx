import React from 'react';
import dynamic from 'next/dynamic';
import '../styles/globals.css';
import type { Metadata, Viewport } from 'next';
import { Analytics } from "@vercel/analytics/next"
import { createServerComponentClient, Session } from '@supabase/auth-helpers-nextjs';
import { cookies, headers } from 'next/headers';
import type { Database } from '@/lib/database.types';
import { GoogleGsiClientLoader } from '@/utils/GoogleGsiClientLoader';

// Dynamically import client components with no SSR
const Toaster = dynamic(() => import('sonner').then(mod => mod.Toaster), { ssr: false });
const AuthProvider = dynamic(() => import('../context/AuthContext').then(mod => mod.AuthProvider), { ssr: false });
const PasswordProtectionWrapper = dynamic(() => import('../components/features/auth/PasswordProtectionWrapper'), { ssr: false });
const QueryProvider = dynamic(() => import('../components/providers/QueryProvider').then(mod => mod.QueryProvider), { ssr: false });

// --- Define a type for the profile data fetched here ---
interface ServerUserProfile {
  id: string;
  username: string | null;
  is_verified: boolean;
}

export const metadata: Metadata = {
  title: 'Detective Board Maker | Build Digital Evidence Boards Online',
  description: 'Create your own digital detective board with our interactive, easy-to-use online tool. Perfect for games, investigations, writers, and mystery fans.',
  openGraph: {
    title: 'Detective Board Maker',
    description: 'Design and connect your clues with our virtual detective board tool.',
    url: 'https://detectiveboard.io'
  }
};


export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1, // Prevent browser page zoom
  userScalable: false, // Prevent browser page zoom
};

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const cookieStore = cookies();
  const supabase = createServerComponentClient<Database>({ cookies: () => cookieStore });

  // --- 1. Get session directly (middleware already validated it) ---
  const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
  
  // Initialize session and profile to null
  let session: Session | null = null;
  let userProfile: ServerUserProfile | null = null;

  // --- 2. If session exists, user is validated ---
  if (!sessionError && sessionData.session) {
    session = sessionData.session;

    // --- Fetch profile directly from database to avoid API overhead ---
    try {
      const { data: profileData, error: profileError } = await supabase
        .from('users')
        .select('id, username, is_verified, email, created_at, updated_at')
        .eq('id', session.user.id)
        .single();

      if (profileError) {
        if (profileError.code !== 'PGRST116') { // Not "not found"
          console.error('Error fetching user profile in layout:', profileError.message);
        }
      } else if (profileData) {
        userProfile = profileData;
      }
    } catch (error: any) {
      console.error('Error fetching user profile in layout:', error.message);
    }
  }
  // If getSession had an error, log it
  else if (sessionError) {
    console.error('Error fetching session in layout:', sessionError.message);
  }

  // --- 3. Pass session and profile to AuthProvider ---
  return (
    <html lang="en">
      <body>
        <QueryProvider>
          <AuthProvider serverSession={session} serverProfile={userProfile}>
            <PasswordProtectionWrapper>
              {children}
              <Toaster position="top-right" />
            </PasswordProtectionWrapper>
          </AuthProvider>
        </QueryProvider>
        <GoogleGsiClientLoader />
        <Analytics />
      </body>
    </html>
  );
} 