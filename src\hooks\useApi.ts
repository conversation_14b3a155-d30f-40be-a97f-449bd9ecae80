'use client';

import { useState, useCallback } from 'react';
import { useAuth } from '@/context/AuthContext';

interface ApiOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  body?: any;
  headers?: Record<string, string>;
  requireAuth?: boolean;
}

interface ApiResponse<T> {
  data: T | null;
  loading: boolean;
  error: Error | null;
  fetch: (url: string, options?: ApiOptions) => Promise<T | null>;
}

/**
 * Custom hook for making API calls with authentication
 */
export function useApi<T = any>(): ApiResponse<T> {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);
  const { isAuthenticated } = useAuth();

  const fetch = useCallback(
    async (url: string, options: ApiOptions = {}): Promise<T | null> => {
      const {
        method = 'GET',
        body,
        headers = {},
        requireAuth = true,
      } = options;

      // Reset state
      setLoading(true);
      setError(null);

      try {
        // Prepare headers
        const fetchHeaders: HeadersInit = {
          'Content-Type': 'application/json',
          ...headers,
        };

        // Add auth token if required and available
        if (requireAuth && isAuthenticated && typeof window !== 'undefined') {
          const token = localStorage.getItem('auth_token');
          if (token) {
            fetchHeaders.Authorization = `Bearer ${token}`;
          }
        }

        // Prepare request
        const requestOptions: RequestInit = {
          method,
          headers: fetchHeaders,
        };

        // Add body for non-GET requests
        if (method !== 'GET' && body) {
          requestOptions.body = JSON.stringify(body);
        }

        // Make the fetch call
        const response = await window.fetch(url, requestOptions);

        // Handle non-2xx responses as errors
        if (!response.ok) {
          let errorMessage = `Request failed with status ${response.status}`;
          try {
            const errorData = await response.json();
            errorMessage = errorData.message || errorMessage;
          } catch (e) {
            // Ignore JSON parsing errors in error responses
          }
          throw new Error(errorMessage);
        }

        // Parse response
        const result = await response.json();
        setData(result);
        setLoading(false);
        return result;
      } catch (e) {
        const fetchError = e instanceof Error ? e : new Error(String(e));
        setError(fetchError);
        setLoading(false);
        return null;
      }
    },
    [isAuthenticated]
  );

  return { data, loading, error, fetch };
} 