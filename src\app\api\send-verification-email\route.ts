import { NextResponse } from 'next/server';

interface EmailRequest {
  email: string;
  code: string;
}

export async function POST(request: Request) {
  try {
    const body = await request.json() as EmailRequest;
    const { email, code } = body;

    if (!email || !code) {
      return NextResponse.json({ error: 'Email and code are required' }, { status: 400 });
    }

    const MAILERSEND_API_KEY = process.env.MAILERSEND_API_KEY;
    const EMAIL_FROM = process.env.EMAIL_FROM;

    if (!MAILERSEND_API_KEY || !EMAIL_FROM) {
      return NextResponse.json({ error: 'Email configuration missing' }, { status: 500 });
    }

    try {
      const response = await fetch('https://api.mailersend.com/v1/email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${MAILERSEND_API_KEY}`,
        },
        body: JSON.stringify({
          from: {
            email: EMAIL_FROM,
            name: 'Detective Board'
          },
          to: [{
            email: email
          }],
          subject: 'Verify your Detective Board account',
          html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background-color: #111111; color: #ffffff; padding: 30px; border-radius: 10px;">
              <h1 style="text-align: center; margin-bottom: 20px; font-size: 32px;">
                <span style="color: #E94057; font-weight: bold;">DETECTIVE</span> BOARD
              </h1>
              <p style="color: #cccccc; text-align: center; margin-bottom: 30px; font-size: 16px;">
                Please verify your email address to complete your registration.
              </p>
              <div style="background-color: #1a1a1a; padding: 25px; border-radius: 8px; text-align: center; margin: 25px 0; border: 1px solid rgba(255, 255, 255, 0.1);">
                <p style="font-size: 28px; letter-spacing: 8px; color: #ffffff; margin: 0; font-weight: bold;">${code}</p>
              </div>
              <div style="margin-top: 30px; text-align: center;">
                <p style="color: #999999; font-size: 14px; margin-bottom: 10px;">
                  This code will expire in 30 minutes.
                </p>
                <p style="color: #999999; font-size: 14px;">
                  If you didn't request this verification, please ignore this email.
                </p>
              </div>
            </div>
          `
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to send email');
      }

      return NextResponse.json({ message: 'Verification email sent' });
    } catch (error) {
      console.error('Error sending verification email:', error);
      return NextResponse.json({ error: 'Failed to send verification email' }, { status: 500 });
    }
  } catch (error) {
    console.error('Request parsing error:', error);
    return NextResponse.json({ error: 'Invalid request format' }, { status: 400 });
  }
} 