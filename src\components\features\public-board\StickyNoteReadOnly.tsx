import React from 'react';
import { Position } from '@/types';
import AutoFitText from '@/components/ui/AutoFitText';

interface StickyNoteReadOnlyProps {
  id: string;
  content: string;
  position: Position;
  scale: number;
  color: string;
  width?: number;
  height?: number;
}

const StickyNoteReadOnly: React.FC<StickyNoteReadOnlyProps> = ({
  content,
  position,
  color,
  width,
  height,
}) => {
  const getColorClass = () => {
    switch (color) {
      case 'yellow': return 'bg-noir-paper from-[#D1BCAA] to-[#C1AC9A]';
      case 'red': return 'bg-gradient-to-br from-noir-accent to-[#b52a2b]';
      case 'blue': return 'bg-gradient-to-br from-noir-teal to-[#008b91]';
      default: return 'bg-gradient-to-br from-noir-paper to-[#C1AC9A]';
    }
  };

  return (
    <div
      className="absolute detective-node"
      style={{
        left: `${position.x}px`,
        top: `${position.y}px`,
        width: `${width ?? 256}px`,
        height: `${height ?? 256}px`,
      }}
    >
      <div 
        className={`${getColorClass()} w-full h-full p-6 rounded-sm sticky-shadow flex flex-col`}
      >
        <div className="flex justify-between mb-2">
          <div className="w-12 h-1 bg-black/20 rounded-full" />
        </div>
        
        <div 
          className="flex-1 detective-text p-2 text-black/80 overflow-hidden"
          style={{ height: '100%' }}
        >
          <AutoFitText 
            mode="multi"
            className="handwritten-font"
            style={{ width: '100%', height: '100%' }}
            parentWidth={width}
            parentHeight={height}
          >
            {content}
          </AutoFitText>
        </div>
      </div>
    </div>
  );
};

export default StickyNoteReadOnly; 