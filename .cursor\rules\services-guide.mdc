---
description: overview of services folder
globs: 
alwaysApply: false
---
# Services Guide

This directory contains implementations for various services used across the application.

- [index.ts](mdc:src/services/index.ts): Re-exports all services from this directory for convenient importing elsewhere in the application.
- [authService.ts](mdc:src/services/authService.ts): Provides functionality for user authentication, including checking authentication status and redirecting to authentication pages.
- [boardService.ts](mdc:src/services/boardService.ts): Handles interactions with the backend API for board-related operations. This includes fetching, saving, updating, and deleting boards, elements (items), and connections.
- [itemRegistry.ts](mdc:src/services/itemRegistry.ts): Manages the different types of items that can be placed on the board (e.g., sticky notes, text, images, articles). It allows registering new item types (plugins) and provides a factory for creating new item instances.
- [operationHistory.ts](mdc:src/services/operationHistory.ts): Implements an undo/redo system for board actions. It tracks user operations (like adding, deleting, or updating items/connections) and allows reverting or reapplying these actions.

