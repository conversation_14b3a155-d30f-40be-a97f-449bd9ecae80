'use client';

import React, { useEffect, useState } from 'react';
import { Position } from '@/types';
import Image from 'next/image';
import AutoFitText from '@/components/ui/AutoFitText';
import { useBatchSignedImageUrls } from '@/hooks/useBatchSignedImageUrls';

// Add the CSS styles for the Polaroid frame
const polaroidStyle = `
  @font-face {
    font-family: 'DryWhiteboardMarker';
    src: url('/fonts/DryWhiteboardMarker-Regular.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
  }

  .polaroid-frame-readonly {
    padding: 15px 15px 30px 15px;
    background-color: white;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    width: 100%;
    height: 100%;
    border-radius: 0.375rem; /* Equivalent to rounded-md */
  }
  
  .polaroid-image-container-readonly {
    border: 4px solid white;
    overflow: hidden;
    background-color: white;
    flex: none; /* Keep image aspect ratio, don't let it grow/shrink with flex */
    position: relative; /* Added for Next Image fill */
    width: 100%; /* Ensure container takes full width */
    aspect-ratio: 4 / 3; /* Or your desired aspect ratio, adjust as needed */
  }
  
  .polaroid-image-readonly {
    object-fit: cover; /* Changed from contain to cover for better fill */
    display: block;
  }
  
  .polaroid-text-readonly {
    margin-top: 15px;
    position: relative;
    z-index: 5;
    color: #000;
    font-weight: 500;
    flex: 1;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: 'DryWhiteboardMarker', sans-serif;
    width: 100%;
    box-sizing: border-box;
    overflow: hidden;
    min-height: 30px; /* Ensure some space for text even if short */
  }
  
  .polaroid-text-readonly > div {
    padding: 0 10px;
  }
  
  .whiteboard-text {
    font-family: 'DryWhiteboardMarker', sans-serif;
  }
`;

interface OptimizedImageNodeProps {
  id: string;
  boardId: string;
  content: string;
  position: Position;
  scale: number;
  width?: number;
  height?: number;
  imageUrl?: string; // Original URL from the server
  caption?: string;
  alt?: string;
  signedUrls?: Record<string, string>; // Mapping of original URLs to signed URLs
  isPlaceholder?: boolean;
  priority?: boolean;
  lazy?: boolean;
}

const OptimizedImageNode: React.FC<OptimizedImageNodeProps> = ({
  id,
  boardId,
  position,
  width = 250,
  height = 250,
  imageUrl,
  caption,
  content,
  alt,
  signedUrls,
  isPlaceholder = false,
  priority = false,
  lazy = true,
}) => {
  const [imageSource, setImageSource] = useState<string | null>(null);
  const imageCaption = caption || content;
  
  // Find signed URL from props or use original
  useEffect(() => {
    if (!imageUrl) {
      setImageSource(null);
      return;
    }
    
    // If we have a pre-fetched signed URL, use it
    if (signedUrls && imageUrl in signedUrls) {
      setImageSource(signedUrls[imageUrl]);
      return;
    }
    
    // Otherwise use the original URL as fallback
    setImageSource(imageUrl);
  }, [imageUrl, signedUrls]);

  if (isPlaceholder) {
    return (
      <div
        className="absolute detective-node animate-pulse"
        style={{
          left: `${position.x}px`,
          top: `${position.y}px`,
          width: `${width}px`,
          height: `${height}px`,
        }}
      >
        <div className="polaroid-frame-readonly">
          <div className="polaroid-image-container-readonly bg-gray-200" />
          <div className="polaroid-text-readonly">
            <div className="h-4 bg-gray-200 rounded w-3/4 mx-auto" />
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      <style>{polaroidStyle}</style>
      <div
        className="absolute detective-node"
        style={{
          left: `${position.x}px`,
          top: `${position.y}px`,
          width: `${width}px`,
          height: `${height}px`,
        }}
      >
        <div className="polaroid-frame-readonly">
          {imageSource ? (
            <div className="polaroid-image-container-readonly">
              <Image
                src={imageSource}
                alt={alt || "Image evidence"}
                fill
                className="polaroid-image-readonly"
                priority={priority}
                loading={lazy && !priority ? "lazy" : "eager"}
                sizes={`${width}px`}
              />
            </div>
          ) : (
            <div className="flex-grow relative flex items-center justify-center bg-gray-200 text-gray-500">
              No image
            </div>
          )}
          
          {imageCaption && (
            <div className="polaroid-text-readonly">
              <AutoFitText
                mode="multi"
                className="whiteboard-text"
                style={{ width: '100%', height: '100%' }}
                parentWidth={width - 30}
                parentHeight={(height / 5)}
              >
                {imageCaption}
              </AutoFitText>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default OptimizedImageNode; 