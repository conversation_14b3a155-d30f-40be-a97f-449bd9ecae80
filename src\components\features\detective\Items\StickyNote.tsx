import React, { useState, useRef, useEffect } from 'react';
import BoardItem, { BoardItemProps } from '../BoardItem';
import { Trash2 } from 'lucide-react';
import AutoFitText from '../../../ui/AutoFitText';

interface StickyNoteProps extends Omit<BoardItemProps, 'type' | 'children'> {
  onDelete: (id: string) => void;
  onContentChange: (id: string, content: string) => void;
  color: string;
  scale: number;
  connectMode?: boolean;
  connectStart?: string | null;
  onConnectionStart?: (id: string) => void;
  onConnectionComplete?: (id: string) => void;
}

const StickyNote: React.FC<StickyNoteProps> = ({
  id,
  content,
  position,
  width,
  height,
  onSizeChange,
  color,
  onPositionChange,
  onSelect,
  isSelected,
  onDelete,
  onContentChange,
  scale,
  connectMode = false,
  connectStart = null,
  onConnectionStart,
  onConnectionComplete,
  ...rest
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [noteContent, setNoteContent] = useState(content);
  const originalContentRef = useRef<string | null>(null);

  // Keep local state in sync with external updates (e.g., realtime)
  useEffect(() => {
    if (!isEditing && content !== noteContent) {
      setNoteContent(content);
    }
  }, [content, isEditing]);

  const handleDoubleClick = (e: React.MouseEvent) => {
    if (connectMode) {
      e.preventDefault();
      return;
    }
    originalContentRef.current = content; // Store original content when editing starts
    setIsEditing(true);
  };

  const handleBlur = () => {
    const wasEditing = isEditing; // Check if we were editing *before* setting state
    setIsEditing(false);
    
    // Only compare and update content if we were actually editing AND content changed
    if (wasEditing) {
      if (noteContent !== content) {
          onContentChange(id, noteContent);
      }
    }
    // Always clear the ref on blur
    originalContentRef.current = null;
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setNoteContent(e.target.value);
  };

  const handleTextareaFocus = (e: React.FocusEvent<HTMLTextAreaElement>) => {
    e.target.select();
  };

  const preventScrollPropagation = (e: React.UIEvent) => {
    e.stopPropagation();
  };

  const getColorClass = () => {
    switch (color) {
      case 'yellow': return 'bg-noir-paper from-[#D1BCAA] to-[#C1AC9A]';
      case 'red': return 'bg-gradient-to-br from-noir-accent to-[#b52a2b]';
      case 'blue': return 'bg-gradient-to-br from-noir-teal to-[#008b91]';
      default: return 'bg-gradient-to-br from-noir-paper to-[#C1AC9A]';
    }
  };

  return (
    <BoardItem
      id={id}
      type="sticky"
      content={content}
      position={position}
      width={width}
      height={height}
      onSizeChange={onSizeChange}
      onPositionChange={onPositionChange}
      onSelect={onSelect}
      isSelected={isSelected}
      scale={scale}
      isEditMode={isEditing}
      connectMode={connectMode}
      connectStart={connectStart}
      onConnectionStart={onConnectionStart}
      onConnectionComplete={onConnectionComplete}
      {...rest}
    >
      <div
        className={`${getColorClass()} p-6 rounded-sm sticky-shadow flex flex-col w-full h-full ${rest.isMultiSelected ? 'border-2 border-blue-500 ring-2 ring-blue-300' : ''}`}
      >
        <div className="flex justify-between mb-2">
          <div className="w-12 h-1 bg-black/20 rounded-full" />
          {!connectMode && (
            <button 
              onClick={() => onDelete(id)} 
              className="text-black/50 hover:text-noir-accent transition-colors"
              data-nodrag="true"
            >
              <Trash2 size={18} />
            </button>
          )}
        </div>
        
        {isEditing ? (
          <textarea
            autoFocus
            className="flex-1 bg-transparent resize-none outline-none detective-text p-2 text-black/80 handwritten-font"
            value={noteContent}
            onChange={handleInputChange}
            onBlur={handleBlur}
            onFocus={handleTextareaFocus}
            onWheel={preventScrollPropagation}
            data-nodrag="true"
          />
        ) : (
          <div 
            className={`flex-1 detective-text p-2 text-black/80 overflow-hidden`}
            onDoubleClick={handleDoubleClick}
            onWheel={preventScrollPropagation}
            style={{ height: '100%', cursor: connectMode ? 'inherit' : 'text' }}
            data-nodrag="true"
          >
            <AutoFitText 
              mode="multi"
              className="handwritten-font"
              style={{ width: '100%', height: '100%' }}
              parentWidth={width}
              parentHeight={height}
            >
              {noteContent}
            </AutoFitText>
          </div>
        )}
      </div>
    </BoardItem>
  );
};

export default StickyNote;
