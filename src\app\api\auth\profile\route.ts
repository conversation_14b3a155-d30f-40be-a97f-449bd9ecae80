import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { NextResponse } from 'next/server';
import type { Database } from '@/lib/database.types';

import { getAuthenticatedUser } from '@/utils/authUtils';

export async function GET(request: Request) {
  try {
    const { user, error: authError, supabase } = await getAuthenticatedUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'User not authenticated' }, { status: 401 });
    }

    const { data: profileData, error: profileError } = await supabase
      .from('users')
      .select('id, username, is_verified, email, created_at, updated_at')
      .eq('id', user.id)
      .single();

    if (profileError) {
      if (profileError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Profile not found' }, { status: 404 });
      }
      console.error('Error fetching user profile in /api/auth/profile:', profileError.message);
      return NextResponse.json({ error: 'Error fetching user profile' }, { status: 500 });
    }

    if (!profileData) {
      return NextResponse.json({ error: 'Profile not found' }, { status: 404 });
    }

    const response = NextResponse.json(profileData);

    // Add caching headers to reduce redundant requests
    // Cache for 5 minutes for complete profiles, 30 seconds for incomplete ones
    const cacheTime = profileData.username && profileData.is_verified ? 300 : 30;
    response.headers.set('Cache-Control', `private, max-age=${cacheTime}, stale-while-revalidate=60`);

    // Add ETag for conditional requests
    const etag = `"${profileData.id}-${profileData.updated_at}"`;
    response.headers.set('ETag', etag);

    // Check if client has current version
    const ifNoneMatch = request.headers.get('if-none-match');
    if (ifNoneMatch === etag) {
      return new NextResponse(null, { status: 304 });
    }

    return response;
  } catch (error) {
    console.error('Unexpected error in /api/auth/profile route:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}