---
description: overview of types folder
globs: 
alwaysApply: false
---
# TypeScript Types Guide

This directory contains all the TypeScript type definitions and interfaces used throughout the Detective Board application.

- [index.ts](mdc:src/types/index.ts): The main entry point that re-exports all types from other files in this directory for easier importing.
- [board.ts](mdc:src/types/board.ts): Defines core types related to the board itself, including `Board`, `Position`, `PenStroke`, `BoardViewState`, and related payload/response types for board operations.
- [item.ts](mdc:src/types/item.ts): Defines types for different kinds of items that can be placed on the board (e.g., `BoardItem`, `StickyNoteItem`, `TextItem`, `ArticleItem`, `ImageItem`) and their associated props and type guards.
- [connection.ts](mdc:src/types/connection.ts): Defines types related to connections between board items, such as `Connection`, `ConnectionEndpoint`, and `ConnectionProps`.
- [hooks.ts](mdc:src/types/hooks.ts): Contains the return types for various custom React hooks used in the application (e.g., `useBoardState`, `useZoomAndPan`, `useConnections`, `useModalState`, `usePenTool`, `useAutoSave`).
- [modals.ts](mdc:src/types/modals.ts): Defines types and props for modal dialogs used in the application, like article forms, save dialogs, and confirmation modals.
- [services.ts](mdc:src/types/services.ts): Defines interfaces for various application services, such as `BoardService`, `AuthService`, `ItemRegistry`, and `OperationHistory`.

