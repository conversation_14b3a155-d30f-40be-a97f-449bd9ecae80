import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import type { Database } from '@/lib/database.types';
import { getAuthenticatedUser } from '@/utils/authUtils';

// Helper function to set cache headers based on content type and query parameters
function setCacheHeaders(response: NextResponse, type: 'list' | 'single' = 'list', params?: URLSearchParams): NextResponse {
  // For list views, cache for 5 minutes
  // For single board view, cache for 15 minutes (detail pages viewed less frequently)
  let maxAge = type === 'list' ? 60 * 5 : 60 * 15;
  
  // If there's a search query, reduce cache time since results may change more frequently
  if (params?.get('search')) {
    maxAge = 60 * 3; // 3 minutes for search results
  }
  
  response.headers.set('Cache-Control', `public, max-age=${maxAge}, s-maxage=${maxAge * 2}, stale-while-revalidate=${maxAge * 4}`);
  
  // Add Vary header to ensure proper caching based on query parameters
  response.headers.set('Vary', 'Accept-Encoding, Origin');
  
  return response;
}

// Helper to extract relative path (e.g., "uuid/filename.png") from various URL formats
function extractRelativePath(url: string | null): string | null {
  if (!url) return null;
  try {
    // Check if it's already a relative path (doesn't start with http)
    if (!url.startsWith('http')) {
        // Basic check if it resembles uuid/filename format
        if (url.includes('/')) {
            return url;
        } else {
            console.warn(`Provided path is not a URL and doesn't seem relative: ${url}`);
            return null;
        }
    }

    // If it's a full URL, parse it
    const parsedUrl = new URL(url);
    // Examples:
    // - /storage/v1/object/public/images/uuid/filename.png -> images/uuid/filename.png
    // - /storage/v1/object/sign/images/uuid/filename.png?token=... -> images/uuid/filename.png
    const pathSegments = parsedUrl.pathname.split('/');

    // Find the bucket name ('images' in this case)
    const bucketName = 'images'; // Assuming your bucket is named 'images'
    const bucketIndex = pathSegments.indexOf(bucketName);

    if (bucketIndex !== -1 && bucketIndex < pathSegments.length - 1) {
      // Join the segments after the bucket name
      return pathSegments.slice(bucketIndex + 1).join('/');
    }

    console.warn(`Could not extract relative path from URL: ${url}`);
    return null; // Return null if extraction fails
  } catch (error) {
    console.error(`Error parsing URL for relative path extraction: ${url}`, error);
    return null;
  }
}

// --- Helper Function to call the Edge Function with batch support ---
async function getPublicSignedUrls(boardId: string, urlInputs: (string | null)[], userId?: string | null): Promise<(string | null)[]> {
  // Extract all valid relative paths
  const filePaths = urlInputs.map(url => ({ 
    originalUrl: url, 
    relativePath: extractRelativePath(url)
  })).filter(item => item.relativePath);
  
  if (filePaths.length === 0) {
    return urlInputs.map(() => null);
  }

  // Ensure the Edge Function URL is configured
  const edgeFunctionUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
    ? `${process.env.NEXT_PUBLIC_SUPABASE_URL}/functions/v1/get-public-image-urls`
    : null;

  if (!edgeFunctionUrl) {
    console.error("Edge function URL (get-public-image-urls) is not configured.");
    return urlInputs.map(() => null);
  }

  // Ensure the Anon Key is configured
  const anonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
  if (!anonKey) {
    console.error("Supabase Anon Key (NEXT_PUBLIC_SUPABASE_ANON_KEY) is not configured.");
    return urlInputs.map(() => null);
  }

  try {
    const response = await fetch(edgeFunctionUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${anonKey}`,
        'apikey': anonKey
      },
      body: JSON.stringify({ 
        boardId, 
        filePaths: filePaths.map(f => f.relativePath), 
        userId 
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: 'Failed to parse edge function error response' }));
      console.error(`Batch edge function call failed for board ${boardId}: ${response.status}`, errorData.error || response.statusText);
      
      // If we spot a 404 error, specifically for preview images, try to refresh the URL
      if (response.status === 404 && 
          errorData.error && 
          typeof errorData.error === 'string' && 
          errorData.error.includes('Object not found') && 
          errorData.error.includes('preview-')) {
        console.log(`Attempting to refresh preview image for board ${boardId} due to 404 error`);
        
        // Try to get the latest preview image URL from the database
        const refreshedPreviewUrl = await refreshBoardPreviewImageUrl(boardId);
        
        if (refreshedPreviewUrl) {
          // Create a new result array with the refreshed URL in the right position
          // Identify which position in the array might be the preview image
          let previewIndex = -1;
          for (let i = 0; i < urlInputs.length; i++) {
            if (urlInputs[i] && 
                extractRelativePath(urlInputs[i])?.includes(`preview-${boardId}`)) {
              previewIndex = i;
              break;
            }
          }
          
          if (previewIndex >= 0) {
            // Create a copy of the result array
            const results = generatePublicUrlFallbacks(urlInputs);
            
            // Extract the relative path
            const refreshedRelativePath = extractRelativePath(refreshedPreviewUrl);
            
            if (refreshedRelativePath) {
              // Replace the problematic URL with the fresh one
              const cookieStore = cookies();
              const supabase = createRouteHandlerClient<Database>({ cookies: () => cookieStore });
              const { data } = supabase.storage.from('images').getPublicUrl(refreshedRelativePath);
              
              if (data?.publicUrl) {
                results[previewIndex] = data.publicUrl;
                return results;
              }
            }
          }
        }
      }
      
      // Fallback to public URLs if refresh attempt failed or wasn't applicable
      return generatePublicUrlFallbacks(urlInputs);
    }

    const data = await response.json();
    const signedUrlMap = new Map(Object.entries(data.signedUrls || {}));
    
    // Map original URLs to signed URLs, preserving the original order
    return urlInputs.map(url => {
      const relPath = extractRelativePath(url);
      const signedUrl = relPath ? signedUrlMap.get(relPath) : null;
      return relPath && typeof signedUrl === 'string' ? signedUrl : null;
    });
  } catch (error) {
    console.error(`Error calling batch edge function for board ${boardId}:`, error);
    return generatePublicUrlFallbacks(urlInputs);
  }
}

// Helper for single image URL signing (fallback to original function)
async function getPublicSignedUrl(boardId: string, urlInput: string | null, userId?: string | null): Promise<string | null> {
  const results = await getPublicSignedUrls(boardId, [urlInput], userId);
  return results[0];
}

// Helper to generate public URLs as fallback
function generatePublicUrlFallbacks(urlInputs: (string | null)[]): (string | null)[] {
  const cookieStore = cookies();
  const supabase = createRouteHandlerClient<Database>({ cookies: () => cookieStore });
  
  return urlInputs.map(url => {
    try {
      const relativePath = extractRelativePath(url);
      if (relativePath) {
        return supabase.storage.from('images').getPublicUrl(relativePath).data.publicUrl as string;
      }
      return null;
    } catch (error) {
      console.error("Error generating public URL fallback:", error);
      return null;
    }
  });
}

// Attempt to refresh a board's preview image URL from the database when signing fails
// This handles the case where the preview has been updated but the cached URL is stale
async function refreshBoardPreviewImageUrl(boardId: string): Promise<string | null> {
  try {
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient<Database>({ cookies: () => cookieStore });
    
    // Fetch the latest preview image URL directly from the database
    const { data, error } = await supabase
      .from('boards')
      .select('preview_image_url')
      .eq('id', boardId)
      .single();
    
    if (error || !data) {
      console.error(`Failed to refresh preview image URL for board ${boardId}:`, error);
      return null;
    }
    
    if (!data.preview_image_url) {
      console.warn(`Board ${boardId} has no preview image URL after refresh attempt`);
      return null;
    }
    
    console.log(`Successfully refreshed preview image URL for board ${boardId}`);
    return data.preview_image_url;
  } catch (error) {
    console.error(`Error in refreshBoardPreviewImageUrl for ${boardId}:`, error);
    return null;
  }
}

// Helper to extract a board ID from a preview image URL pattern
function extractBoardIdFromPreviewImage(url: string | null): string | null {
  if (!url) return null;
  
  try {
    // Look for pattern like: preview-{boardId}-{timestamp}.jpg
    const previewRegex = /preview-([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})-\d+\.\w+$/i;
    const match = url.match(previewRegex);
    
    if (match && match[1]) {
      return match[1]; // Return the extracted board ID
    }
    
    return null;
  } catch (error) {
    console.error(`Error extracting board ID from preview URL: ${url}`, error);
    return null;
  }
}

// --- GET Handler ---
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);

  const { user: currentUser, supabase } = await getAuthenticatedUser();
  const userId = currentUser?.id;

  try {
    const specificBoardId = searchParams.get('boardId');

    // --- Scenario 1: Fetching a SPECIFIC Public Board ---
    if (specificBoardId) {
      // Single board query - join boards, elements, connections in one query
      // Using a more efficient single query approach
      const { data: boardData, error: boardError } = await supabase
        .from('board_sharing')
        .select(`
          id,
          likes,
          created_at,
          updated_at,
          public_board,
          board_id,
          total_views,
          boards!inner (
            id,
            board_name,
            preview_image_url,
            created_at,
            updated_at,
            user_id,
            strokes,
            elements (
              id, 
              element_type, 
              title, 
              text_content, 
              position_x, 
              position_y, 
              file_url, 
              website_url, 
              color,
              alt_text,
              is_ai_generated
            ),
            connections (
              id, 
              from_element_id, 
              to_element_id, 
              label, 
              connection_type,
              is_ai_generated
            )
          )
        `)
        .eq('board_id', specificBoardId)
        .eq('public_board', true)
        .single();

      if (boardError || !boardData || !boardData.boards) {
        console.error('Error fetching specific public board or board data missing:', boardError);
        return NextResponse.json({ error: 'Board not found or not accessible' }, { status: 404 });
      }

      const board = boardData.boards;
      
      // Get all image URLs that need to be signed in a single batch
      const allImageUrls: (string | null)[] = [];
      const urlIndexMap = new Map<number, { elementIndex: number, isArticle?: boolean }>();
      
      // Collect URLs from elements
      let index = 0;
      board.elements.forEach((element: any, elementIndex: number) => {
        if ((element.element_type === 'image' || element.element_type === 'image-invisible' || 
             element.element_type === 'article') && element.file_url) {
          allImageUrls.push(element.file_url);
          urlIndexMap.set(index++, { elementIndex, isArticle: element.element_type === 'article' });
        }
      });
      
      // Add preview image URL if it exists - ensure it's not empty
      if (board.preview_image_url && board.preview_image_url.trim() !== '') {
        // Verify the preview image URL format - it should contain the board ID
        // This helps catch potentially outdated preview URLs
        const previewImageUrl = board.preview_image_url;
        const extractedBoardId = extractBoardIdFromPreviewImage(previewImageUrl);
        
        // Only use the preview if it matches the board ID pattern or if we can't detect a board ID
        if (!extractedBoardId || extractedBoardId === specificBoardId) {
          allImageUrls.push(previewImageUrl);
          urlIndexMap.set(index, { elementIndex: -1 }); // -1 indicates preview image
        } else {
          console.warn(`Preview image URL appears to be for different board: ${previewImageUrl}`);
          // We'll skip this URL as it's probably outdated
        }
      }
      
      // Batch request for all URLs
      let signedUrls: (string | null)[] = [];
      if (allImageUrls.length > 0) {
        signedUrls = await getPublicSignedUrls(specificBoardId, allImageUrls, userId);
      }
      
      // Map the elements with signed URLs
      const elements = board.elements.map((element: any, elementIndex: number) => {
        const baseElement = {
          id: element.id,
          type: element.element_type,
          title: element.title || '',
          content: element.text_content || '',
          position: { x: element.position_x, y: element.position_y },
          website_url: element.website_url || undefined,
          color: element.color,
          url: '', // Will be set based on type
          alt: element.alt_text || '',
          imageUrl: undefined as string | undefined,
          isAiGenerated: element.is_ai_generated || false,
        };

        // Find the URL for this element if it exists
        for (const [urlIndex, info] of urlIndexMap.entries()) {
          if (info.elementIndex === elementIndex) {
            const signedUrl = signedUrls[urlIndex];
            if (signedUrl) {
              baseElement.url = signedUrl;
              if (element.element_type.startsWith('image') || info.isArticle) {
                baseElement.imageUrl = signedUrl;
              }
            }
            break;
          }
        }

        // If article has no file_url, url should be the website_url
        if (element.element_type === 'article' && !baseElement.imageUrl) {
          baseElement.url = element.website_url || element.url || '';
        }

        return baseElement;
      });

      // Process connections
      const connections = board.connections.map((connection: any) => ({
        id: connection.id,
        fromId: connection.from_element_id,
        toId: connection.to_element_id,
        label: connection.label || '',
        type: connection.connection_type || 'default',
        isAiGenerated: connection.is_ai_generated || false,
      }));

      // Process strokes
      const strokes: any[] = Array.isArray(board.strokes) ? board.strokes : []; 

      // Find preview image URL if it was requested
      let previewImageUrl = null;
      for (const [urlIndex, info] of urlIndexMap.entries()) {
        if (info.elementIndex === -1) {
          previewImageUrl = signedUrls[urlIndex];
          break;
        }
      }

      return setCacheHeaders(NextResponse.json({
        id: board.id,
        name: board.board_name,
        elements,
        connections,
        strokes,
        likes: boardData.likes || 0,
        publicSince: boardData.created_at,
        previewImageUrl, // Add the preview image URL if available
        totalViews: boardData.total_views || 0,
      }, { status: 200 }), 'single');
    }

    // --- Scenario 2: Fetching a LIST of Public Boards ---
    else {
      const limit = parseInt(searchParams.get('limit') || '10', 10);
      const offset = parseInt(searchParams.get('offset') || '0', 10);
      const searchQuery = searchParams.get('search') || '';
      const sortOrder = searchParams.get('sortOrder') || 'desc';
      const sortBy = searchParams.get('sortBy') || 'popularity_score'; // Default to popularity if not specified
      const timePeriod = searchParams.get('timePeriod') || 'allTime'; // Use 'allTime' to match frontend/SQL func
      const minLikes = parseInt(searchParams.get('minLikes') || '0', 10);
      const minComments = parseInt(searchParams.get('minComments') || '0', 10);
      const showPopular = searchParams.get('showPopular') === 'true';

      let processedBoards: any[] = [];
      let totalBoardsForPagination = 0;
      let popularBoardsList: any[] = []; // Keep for non-search, clear for search

      // Get time filter date
      const getTimeFilterDate = (period: string): string | null => {
        if (period === 'allTime' || period === 'all') return null;
        
        const now = new Date();
        if (period === 'last7days') {
          const date = new Date(now);
          date.setDate(date.getDate() - 7);
          return date.toISOString();
        } else if (period === 'last30days') {
          const date = new Date(now);
          date.setDate(date.getDate() - 30);
          return date.toISOString();
        }
        return null;
      };
      
      const timeFilterDate = getTimeFilterDate(timePeriod);

      // Helper for batch processing board preview image URLs
      const processBoardsWithBatchImageSigning = async (boardSharingItems: any[]) => {
        if (!boardSharingItems || boardSharingItems.length === 0) return [];
        
        // Extract all preview image URLs and their corresponding board IDs
        const previewUrls: { boardId: string, url: string | null }[] = [];
        boardSharingItems.forEach(item => {
          if (item.boards?.preview_image_url && item.boards.preview_image_url.trim() !== '') {
            // Optional validation: check if the preview URL seems valid for this board
            const previewUrl = item.boards.preview_image_url;
            const extractedBoardId = extractBoardIdFromPreviewImage(previewUrl);
            
            // Only use if no board ID detected in URL or if it matches the current board
            if (!extractedBoardId || extractedBoardId === item.boards.id) {
              previewUrls.push({
                boardId: item.boards.id,
                url: previewUrl
              });
            } else {
              console.warn(`Skipping potentially outdated preview image in listing: ${previewUrl}`);
            }
          }
        });
        
        // Get all signed URLs in one batch
        const signedUrlsByBoardId = new Map<string, string | null>();
        
        if (previewUrls.length > 0) {
          // Group URLs by board ID for batch processing
          const boardGroups = new Map<string, (string | null)[]>();
          previewUrls.forEach(item => {
            if (!boardGroups.has(item.boardId)) {
              boardGroups.set(item.boardId, []);
            }
            boardGroups.get(item.boardId)!.push(item.url);
          });
          
          // Process each board's URLs
          await Promise.all(Array.from(boardGroups.entries()).map(async ([boardId, urls]) => {
            const signedBatch = await getPublicSignedUrls(boardId, urls, userId);
            // Map each signed URL back to its board
            urls.forEach((url, index) => {
              const relativePath = extractRelativePath(url);
              if (relativePath) {
                const signedUrl = signedBatch[index];
                // Only use valid signed URLs
                if (signedUrl) {
                  signedUrlsByBoardId.set(boardId, signedUrl);
                }
              }
            });
          }));
        }
        
        // Map the signed URLs to each board
        return boardSharingItems.map(item => {
          const board = item.boards;
          if (!board) return null;
          
          // Get the signed URL for this board's preview image
          const signedPreviewUrl = signedUrlsByBoardId.get(board.id) || null;
          
          const commentCountResult = board.comments as unknown as ({ count: number }[] | null);
          const commentCount = commentCountResult && commentCountResult.length > 0 ? commentCountResult[0].count : 0;
          
          return {
            id: board.id,
            name: board.board_name,
            previewImageUrl: signedPreviewUrl,
            createdAt: board.created_at,
            updatedAt: board.updated_at,
            likes: item.likes || 0,
            publicSince: item.created_at,
            commentCount: commentCount,
            popularityScore: board.popularity_score || 0,
            totalViews: item.total_views || 0,
          };
        }).filter(b => b !== null);
      };

      if (searchQuery) {
        // --- FTS Path ---
        const { data: ftsResults, error: ftsError } = await supabase.rpc(
          'search_public_boards_fts',
          {
            query_text: searchQuery,
            result_limit: limit,
            result_offset: offset,
            time_period_filter: timePeriod
          }
        );

        if (ftsError) {
          console.error('Error calling search_public_boards_fts RPC:', ftsError);
          return NextResponse.json({ error: 'Failed to search public boards' }, { status: 500 });
        }

        const boardIdsFromFTS = ftsResults?.map((r: any) => r.board_id) || [];
        const relevanceMap = new Map(ftsResults?.map((r: any) => [r.board_id, r.relevance]) || []);

        if (boardIdsFromFTS.length > 0) {
          // Perform a more efficient JOIN query to get all data at once
          const { data: fetchedBoardsData, error: fetchBoardsError } = await supabase
            .from('board_sharing')
            .select(`
              id, likes, created_at, updated_at, public_board, board_id, total_views,
              boards!inner (
                id, board_name, preview_image_url, created_at, updated_at, user_id,
                popularity_score,
                comments(count)
              )
            `)
            .in('boards.id', boardIdsFromFTS)
            .eq('public_board', true);

          if (fetchBoardsError) {
            console.error('Error fetching board details for FTS results:', fetchBoardsError);
          }

          if (fetchedBoardsData) {
            // Create a map for quick lookup and reconstruct in the order from FTS
            const boardsDataMap = new Map(fetchedBoardsData.map(b => [b.boards.id, b]));
            const orderedBoardSharingItems = boardIdsFromFTS
              .map(id => boardsDataMap.get(id))
              .filter(b => b);
            
            // Process with batch image signing
            const processedWithImages = await processBoardsWithBatchImageSigning(orderedBoardSharingItems);
            
            // Add relevance scores
            processedBoards = processedWithImages.map(board => ({
              ...board,
              relevance: relevanceMap.get(board.id) || 0
            }));
          }
        }

        // Get total count for pagination with a single DB call
        const { data: countResult, error: countError } = await supabase.rpc(
          'count_search_public_boards_fts',
          {
            query_text: searchQuery,
            time_period_filter: timePeriod
          }
        );

        if (countError) {
          console.error('Error calling count_search_public_boards_fts RPC:', countError);
        }
        
        totalBoardsForPagination = countResult?.[0]?.total_boards || 0;
        popularBoardsList = []; // Don't show separate popular list for search

      } else {
        // --- Non-FTS Path (Optimized for performance) ---
        let query = supabase
          .from('board_sharing')
          .select(`
            id, likes, created_at, updated_at, public_board, board_id, total_views,
            boards!inner (
              id, board_name, preview_image_url, created_at, updated_at, user_id,
              popularity_score,
              comments(count)
            )
          `)
          .eq('public_board', true);

        // Apply minimum likes filter
        if (minLikes > 0) {
          query = query.gte('likes', minLikes);
        }

        // Apply time period filter more efficiently
        if (timeFilterDate) {
          query = query.gte('created_at', timeFilterDate);
        }
        
        // Apply sorting - use indexes efficiently
        if (sortBy === 'popularity_score') {
          query = query.order('popularity_score', { foreignTable: 'boards', ascending: sortOrder === 'asc' });
        } else if (sortBy === 'likes') {
          query = query.order('likes', { ascending: sortOrder === 'asc' });
        } else if (sortBy === 'recent') {
          query = query.order('created_at', { ascending: sortOrder === 'asc' });
        }

        // Apply pagination
        query = query.limit(limit).range(offset, offset + limit - 1);
        const { data: nonFtsPublicBoards, error: boardsError } = await query;

        if (boardsError) {
          console.error('Error fetching non-FTS public boards list:', boardsError);
          return NextResponse.json({ error: 'Failed to fetch public boards' }, { status: 500 });
        }

        // Process boards with batch image signing
        processedBoards = await processBoardsWithBatchImageSigning(nonFtsPublicBoards || []);

        // Count total for pagination
        let countQueryNonFts = supabase
          .from('board_sharing')
          .select('id', { count: 'exact', head: true })
          .eq('public_board', true);
        
        // Apply time filter to count query
        if (timeFilterDate) {
          countQueryNonFts = countQueryNonFts.gte('created_at', timeFilterDate);
        }
        
        // Apply minimum likes filter to count query too
        if (minLikes > 0) {
          countQueryNonFts = countQueryNonFts.gte('likes', minLikes);
        }

        const { count, error: countError } = await countQueryNonFts;
        if (countError) console.error('Error counting non-FTS public boards:', countError);
        totalBoardsForPagination = count || 0;

        // Fetch popular boards in a more efficient manner when needed
        if (showPopular) {
          const { data: popular, error: popularError } = await supabase
            .from('board_sharing')
            .select(`id, likes, board_id, boards!inner (id, board_name, preview_image_url)`)
            .eq('public_board', true)
            .order('likes', { ascending: false })
            .limit(5);
          
          if (popularError) {
            console.error('Error fetching popular boards:', popularError);
          }
          
          if (popular && popular.length > 0) {
            // Batch process popular board images
            popularBoardsList = await processBoardsWithBatchImageSigning(popular);
          }
        }
      }

      // Apply minimum comments filter post-query
      if (minComments > 0 && processedBoards.length > 0) {
        processedBoards = processedBoards.filter(board => (board?.commentCount || 0) >= minComments);
      }

      // Return response with appropriate cache headers
      return setCacheHeaders(
        NextResponse.json({
          boards: processedBoards,
          popularBoards: popularBoardsList,
          pagination: {
            total: totalBoardsForPagination,
            offset,
            limit,
            hasMore: totalBoardsForPagination > offset + limit
          }
        }, { status: 200 }),
        'list',
        searchParams
      );
    }

  } catch (error) {
    console.error('Unexpected error in /api/board/public:', error);
    if (error instanceof Error) {
      console.error(error.stack);
    }
    return NextResponse.json({ 
      error: 'An unexpected error occurred', 
      details: error instanceof Error ? error.message : String(error) 
    }, { status: 500 });
  }
} 
