'use client';

import { useState, useCallback } from 'react';
import { useApi } from './useApi';

export interface BoardElement {
  id: string;
  type: 'note' | 'image' | 'document' | 'link';
  content: string;
  position: { x: number; y: number };
  size?: { width: number; height: number };
  color?: string;
  createdAt: string;
  updatedAt: string;
}

export interface BoardConnection {
  id: string;
  sourceId: string;
  targetId: string;
  label?: string;
  color?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Board {
  id: string;
  boardName: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
  elementsCount: number;
  connectionsCount: number;
  isOwner: boolean;
  isPublic?: boolean;
  elements?: BoardElement[];
  connections?: BoardConnection[];
}

interface UseBoards {
  userBoards: Board[] | null;
  sharedBoards: Board[] | null;
  loading: boolean;
  error: Error | null;
  fetchUserBoards: () => Promise<void>;
  getBoardDetails: (boardId: string) => Promise<Board | null>;
  createBoard: (name: string, description?: string) => Promise<Board | null>;
  updateBoard: (boardId: string, data: Partial<Board>) => Promise<Board | null>;
  deleteBoard: (boardId: string) => Promise<boolean>;
  shareBoard: (boardId: string, email: string, permission: 'view' | 'edit') => Promise<boolean>;
}

/**
 * Hook for managing board operations
 */
export function useBoards(): UseBoards {
  const [userBoards, setUserBoards] = useState<Board[] | null>(null);
  const [sharedBoards, setSharedBoards] = useState<Board[] | null>(null);
  const api = useApi<any>();

  const fetchUserBoards = useCallback(async () => {
    try {
      const result = await api.fetch('/api/board/user-boards');
      if (result) {
        setUserBoards(result.userBoards);
        setSharedBoards(result.sharedBoards);
      }
    } catch (error) {
      console.error('Error fetching boards:', error);
    }
  }, [api]);

  const getBoardDetails = useCallback(
    async (boardId: string): Promise<Board | null> => {
      const result = await api.fetch(`/api/board/${boardId}`);
      return result as Board | null;
    },
    [api]
  );

  const createBoard = useCallback(
    async (name: string, description?: string): Promise<Board | null> => {
      const result = await api.fetch('/api/board', {
        method: 'POST',
        body: { boardName: name, description },
      });
      
      if (result) {
        // Update the board list
        setUserBoards((prev) => prev ? [...prev, result] : [result]);
        return result;
      }
      
      return null;
    },
    [api]
  );

  const updateBoard = useCallback(
    async (boardId: string, data: Partial<Board>): Promise<Board | null> => {
      const result = await api.fetch(`/api/board/${boardId}`, {
        method: 'PATCH',
        body: data,
      });
      
      if (result) {
        // Update the boards list
        setUserBoards((prev) => {
          if (!prev) return prev;
          return prev.map((board) => {
            if (board.id === boardId) {
              return { ...board, ...result };
            }
            return board;
          });
        });
        
        setSharedBoards((prev) => {
          if (!prev) return prev;
          return prev.map((board) => {
            if (board.id === boardId) {
              return { ...board, ...result };
            }
            return board;
          });
        });
        
        return result;
      }
      
      return null;
    },
    [api]
  );

  const deleteBoard = useCallback(
    async (boardId: string): Promise<boolean> => {
      const result = await api.fetch(`/api/board/${boardId}`, {
        method: 'DELETE',
      });
      
      if (result && result.success) {
        // Remove from the board lists
        setUserBoards((prev) => 
          prev ? prev.filter((board) => board.id !== boardId) : prev
        );
        
        setSharedBoards((prev) =>
          prev ? prev.filter((board) => board.id !== boardId) : prev
        );
        
        return true;
      }
      
      return false;
    },
    [api]
  );

  const shareBoard = useCallback(
    async (boardId: string, email: string, permission: 'view' | 'edit'): Promise<boolean> => {
      const result = await api.fetch(`/api/board/${boardId}/share`, {
        method: 'POST',
        body: { email, permission },
      });
      
      return result && result.success;
    },
    [api]
  );

  return {
    userBoards,
    sharedBoards,
    loading: api.loading,
    error: api.error,
    fetchUserBoards,
    getBoardDetails,
    createBoard,
    updateBoard,
    deleteBoard,
    shareBoard,
  };
} 