'use client';

import { useState, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { useQuery } from '@tanstack/react-query';
import type { Database } from '@/lib/database.types';

/**
 * Hook to get a signed URL for an image
 * This is used to avoid JWT expiration issues when server-side rendering
 */
export function useSignedImageUrl(
  boardId: string, 
  originalUrl: string | null,
  loadImmediately = true
): { 
  signedUrl: string | null, 
  isLoading: boolean, 
  error: Error | null,
  refetch: () => void
} {
  const [publicUrl, setPublicUrl] = useState<string | null>(null);
  const supabase = createClientComponentClient<Database>();
  
  // Extract the file path from the original URL
  useEffect(() => {
    if (!originalUrl) {
      setPublicUrl(null);
      return;
    }
    
    // If it's already relative or not a URL, use it directly for fallback
    if (!originalUrl.startsWith('http')) {
      try {
        // Create a public URL for fallback
        const { data } = supabase.storage.from('images').getPublicUrl(originalUrl);
        setPublicUrl(data.publicUrl);
      } catch (e) {
        console.error('Error getting public URL:', e);
        setPublicUrl(originalUrl);
      }
      return;
    }
    
    // Otherwise, set the original URL as the public fallback
    setPublicUrl(originalUrl);
  }, [originalUrl, supabase]);
  
  // Use React Query to fetch and cache the signed URL
  const {
    data: signedUrlData,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['signedImageUrl', boardId, originalUrl],
    queryFn: async () => {
      if (!originalUrl || !boardId) {
        return null;
      }
      
      try {
        // Use the edge function to get a signed URL with longer expiry
        const response = await fetch('/api/image/signed-url', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            boardId,
            imageUrl: originalUrl,
          }),
        });
        
        if (!response.ok) {
          throw new Error('Failed to get signed URL');
        }
        
        const data = await response.json();
        return data.signedUrl;
      } catch (e) {
        console.error('Error fetching signed URL:', e);
        return null;
      }
    },
    enabled: !!(originalUrl && boardId && loadImmediately),
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 1000 * 60 * 10,   // 10 minutes
  });
  
  // Return the signed URL or fall back to the public URL
  return {
    signedUrl: signedUrlData || publicUrl,
    isLoading,
    error: error as Error | null,
    refetch
  };
} 