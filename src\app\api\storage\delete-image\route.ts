import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import type { Database } from '@/lib/database.types';

import { getAuthenticatedUser } from '@/utils/authUtils';
// Replace 'images' with your actual bucket name if different
const BUCKET_NAME = 'images';

export async function DELETE(request: NextRequest) {
    try {
        const { user, error: authError, supabase } = await getAuthenticatedUser();
        if (authError || !user) {
            console.error("Delete Image - User Session Error:", authError);
            return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
        }

        // 2. Parse Request Body - Expecting the relative path directly now
        const { filePath } = await request.json() as { filePath?: string };

        if (!filePath || typeof filePath !== 'string' || filePath.trim() === '') {
            // Updated error message for clarity
            return NextResponse.json({ message: 'Relative file path is required in the request body' }, { status: 400 });
        }

        // No path extraction needed anymore

        // Log using the direct file path

        // 3. Attempt to delete the file using the provided relative path
        const { data, error: deleteError } = await supabase
            .storage
            .from(BUCKET_NAME)
            .remove([filePath]); // Use the filePath directly

        if (deleteError) {
            // Log the specific Supabase storage error
            console.error(`Storage Deletion Error for path ${filePath}:`, deleteError);
            // Check for common errors
            if (deleteError.message.includes('Not found')) {
                 return NextResponse.json({ message: `File not found at path: ${filePath}`, error: deleteError.message }, { status: 404 });
            }
             // Default to internal server error, possibly permission denied by RLS
            return NextResponse.json({ message: 'Failed to delete image from storage', error: deleteError.message }, { status: 500 });
        }

        // 4. Return Success Response
        return NextResponse.json({ message: 'Image deleted successfully from storage (if it existed and permissions allowed)' }, { status: 200 });

    } catch (error: any) {
        console.error('Error processing image deletion request:', error);
         // Handle JSON parsing errors or other unexpected errors
         if (error instanceof SyntaxError) {
             return NextResponse.json({ message: 'Invalid request body format', error: error.message }, { status: 400 });
         }
        return NextResponse.json({ message: 'Server error processing image deletion', error: error.message }, { status: 500 });
    }
}
