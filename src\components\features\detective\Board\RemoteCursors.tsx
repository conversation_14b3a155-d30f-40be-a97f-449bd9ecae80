import React, { useEffect, useState, useRef } from 'react';

interface CursorData { x: number; y: number; lastSeen?: number }
interface RemoteCursorsProps {
  cursors: Record<string, CursorData>;
}

interface InterpolatedCursor {
  x: number;
  y: number;
  targetX: number;
  targetY: number;
  lastUpdate: number;
}

// Utility to generate a consistent color from a string (e.g., userId)
const stringToHslColor = (str: string, s = 70, l = 50) => {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    hash = str.charCodeAt(i) + ((hash << 5) - hash);
  }
  const h = hash % 360;
  return `hsl(${h}, ${s}%, ${l}%)`;
};

const STALE_MS = 5000; // 5 seconds
const INTERPOLATION_SPEED = 0.15; // How fast to interpolate (0-1, higher = faster)

const RemoteCursors: React.FC<RemoteCursorsProps> = ({ cursors }) => {
  const [interpolatedCursors, setInterpolatedCursors] = useState<Record<string, InterpolatedCursor>>({});
  const animationFrameRef = useRef<number>();

  // Update interpolated cursors when new cursor data arrives
  useEffect(() => {
    setInterpolatedCursors(prev => {
      const updated = { ...prev };

      // Update existing cursors with new targets
      Object.entries(cursors).forEach(([id, cursor]) => {
        if (updated[id]) {
          // Update target position
          updated[id].targetX = cursor.x;
          updated[id].targetY = cursor.y;
          updated[id].lastUpdate = Date.now();
        } else {
          // New cursor - start at target position
          updated[id] = {
            x: cursor.x,
            y: cursor.y,
            targetX: cursor.x,
            targetY: cursor.y,
            lastUpdate: Date.now()
          };
        }
      });

      // Remove cursors that are no longer present
      Object.keys(updated).forEach(id => {
        if (!cursors[id]) {
          delete updated[id];
        }
      });

      return updated;
    });
  }, [cursors]);

  // Animation loop for smooth interpolation
  useEffect(() => {
    const animate = () => {
      setInterpolatedCursors(prev => {
        const updated = { ...prev };
        let hasChanges = false;

        Object.entries(updated).forEach(([id, cursor]) => {
          const deltaX = cursor.targetX - cursor.x;
          const deltaY = cursor.targetY - cursor.y;
          const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

          // Only interpolate if there's a meaningful distance
          if (distance > 0.5) {
            updated[id] = {
              ...cursor,
              x: cursor.x + deltaX * INTERPOLATION_SPEED,
              y: cursor.y + deltaY * INTERPOLATION_SPEED
            };
            hasChanges = true;
          }
        });

        return hasChanges ? updated : prev;
      });

      animationFrameRef.current = requestAnimationFrame(animate);
    };

    animationFrameRef.current = requestAnimationFrame(animate);

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, []);

  const now = Date.now();
  return (
    <>
      {Object.entries(interpolatedCursors)
        .filter(([id]) => {
          const originalCursor = cursors[id];
          return originalCursor && (!originalCursor.lastSeen || now - originalCursor.lastSeen < STALE_MS);
        })
        .map(([id, pos]) => (
          <div
            key={id}
            className="absolute pointer-events-none transition-opacity duration-200"
            style={{
              left: pos.x,
              top: pos.y,
              transform: 'translate(-50%, -50%)',
              zIndex: 9999
            }}
          >
            {/* Simple circle avatar for now */}
            <div
              style={{
                width: 12,
                height: 12,
                borderRadius: '50%',
                backgroundColor: stringToHslColor(id),
                border: '2px solid white',
                boxShadow: '0 2px 4px rgba(0,0,0,0.2)'
              }}
            />
          </div>
        ))}
    </>
  );
};

export default RemoteCursors; 