import React, { useMemo, useRef } from 'react';
import { motion } from '../../../../utils/MotionWrapper';
import { Grid } from './Grid';
import { Position } from '../../../../types';

// Custom view state for public board that doesn't include connection-related properties
interface PublicBoardViewState {
  scale: number;
  position: Position;
  selectedItemId: string | null;
  isGrabbing: boolean;
  presentationMode: boolean;
}

interface BoardCanvasProps {
  children: React.ReactNode;
  viewState: PublicBoardViewState;
  boardDimensions: { width: number; height: number };
  containerRef: React.RefObject<HTMLDivElement>;
  onWheel: (event: React.WheelEvent<Element>) => void;
  onCanvasPointerDown: (event: React.PointerEvent<Element>) => void;
}

/**
 * The main canvas component for the public board view
 */
const PublicBoardCanvas: React.FC<BoardCanvasProps> = ({
  children,
  viewState,
  boardDimensions,
  containerRef,
  onWheel,
  onCanvasPointerDown
}) => {
  const { scale, position, isGrabbing, presentationMode } = viewState;

  const boardRef = useRef<HTMLDivElement>(null);

  const borderStyles = useMemo(() => {
    const borderWidth = Math.max(2, Math.round(5 / scale));
    const opacity = Math.min(0.5, 0.2 + (0.4 / scale));
    return {
      border: { borderWidth: `${borderWidth}px`, opacity: opacity }
    };
  }, [scale]);

  const getCursorStyle = () => {
    // In presentation mode, always use grab cursor
    if (presentationMode) {
      return isGrabbing ? 'grabbing' : 'grab';
    }
    
    if (isGrabbing) {
      return 'grabbing';
    }
    return 'grab';
  };

  const handleBoardClick = (e: React.PointerEvent<HTMLDivElement>) => {
    onCanvasPointerDown(e);
  };

  return (
    <div
      id="board-canvas"
      ref={containerRef}
      className="relative w-full h-full overflow-hidden bg-noir-300 film-grain"
      onWheel={onWheel}
      onPointerDown={handleBoardClick}
      style={{ cursor: getCursorStyle() }}
    >
      {/* --- BEGIN TRY-CATCH --- */}
      {(() => {
        try {
          return (
            <motion.div
              data-testid="board-motion-div"
              className="absolute w-full h-full"
              style={{
                scale,
                x: position.x,
                y: position.y,
                transformOrigin: '0 0',
                pointerEvents: 'auto',
              }}
            >
              {/* Grid background */}
              <Grid />
              
              {/* Board boundary - simple dotted border with no corner markers */}
              <div 
                className="absolute border-dashed border-white rounded-lg pointer-events-none"
                style={{
                  width: boardDimensions.width,
                  height: boardDimensions.height,
                  left: 0,
                  top: 0,
                  borderWidth: borderStyles.border.borderWidth,
                  opacity: borderStyles.border.opacity
                }}
              />
              
              {/* Board content */}
              {children}
            </motion.div>
          );
        } catch (error) {
          console.error("[BoardCanvas] CRITICAL ERROR rendering main motion.div content:", error);
          // Return a placeholder or error message instead of crashing
          return <div style={{ color: 'red', padding: '20px', fontSize: '20px' }}>Error rendering board content. Check console.</div>;
        }
      })()}
      {/* --- END TRY-CATCH --- */}

      {/* Status bar */}
      <div className="fixed bottom-2 left-2 text-white/30 text-xs pointer-events-none">
        {scale.toFixed(2)}x
      </div>
    </div>
  );
};

export default PublicBoardCanvas; 