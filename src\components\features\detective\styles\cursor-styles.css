/**
 * Detective Board Cursor Style Guide
 * ---------------------------------
 * This file documents the cursor styles used across the detective board components.
 * It provides a central reference for maintaining consistent cursor behavior.
 *
 * Board Canvas:
 * - Regular mode: 'grab' when idle, 'grabbing' when panning
 * - Connection mode: 'grab' (changes to 'grabbing' when panning), regardless of connection state
 *
 * Board Items:
 * - Regular mode: 'move' 
 * - Edit mode: 'text'
 * - Connection mode:
 *   - Text elements: 'not-allowed' (cannot connect to text)
 *   - Connection source (hovering over the item that started the connection): 'not-allowed' (cannot connect to self)
 *   - Already-connected elements (hovering over items that already have a connection with the source): 'not-allowed' (cannot create duplicate connections)
 *   - Valid connection targets: 'crosshair'
 *   - General items in connect mode, no start selected: 'crosshair'
 *
 * Connection display elements are transparent to mouse events (pointerEvents: 'none')
 * so they don't affect cursor style.
 */

/* This is just a documentation file - actual styles are applied inline in the components */ 