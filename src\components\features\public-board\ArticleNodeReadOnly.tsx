import React, { useState, useEffect } from 'react';
import { ExternalLink, ImageOff } from 'lucide-react';
import { Position } from '@/types';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import type { Database } from '@/lib/database.types';

interface ArticleNodeReadOnlyProps {
  id: string;
  content: string;
  position: Position;
  scale: number;
  title: string;
  url: string;
  website_url?: string;
  imageUrl?: string | null;
  file_url?: string | null;
  width: number;
  height: number;
}

const ArticleNodeReadOnly: React.FC<ArticleNodeReadOnlyProps> = ({
  id,
  position,
  title,
  content,
  url,
  website_url,
  imageUrl,
  file_url,
  width,
  height,
}) => {
  const [imageError, setImageError] = useState(false);
  const [effectiveImageUrl, setEffectiveImageUrl] = useState<string | null>(null);

  const baseWidth = 300;
  const scaleFactor = width / baseWidth;
  const clampedScale = Math.max(0.7, Math.min(2.0, scaleFactor));

  const titleFontSizeRem = 1.5 * clampedScale;
  const contentFontSizeRem = 0.875 * clampedScale;
  const metaFontSizeRem = 0.75 * clampedScale;
  const contentLineHeight = 1.5;

  const tapeBaseWidthPx = 100;
  const tapeBaseHeightPx = 30;
  const dynamicTapeWidthPx = tapeBaseWidthPx * clampedScale;
  const dynamicTapeHeightPx = tapeBaseHeightPx * clampedScale;
  const dynamicTapeTranslateYPx = -(dynamicTapeHeightPx * 0.4);

  useEffect(() => {
    console.log(`[ArticleNodeReadOnly ${id}] useEffect triggered. Received imageUrl: ${imageUrl?.substring(0,50)}..., file_url: ${file_url}`);
    setImageError(false);

    // Priority 1: Use imageUrl if it's a displayable URL (blob or http)
    if (imageUrl && (imageUrl.startsWith('http') || imageUrl.startsWith('blob:') || imageUrl.startsWith('data:'))) {
      console.log(`[ArticleNodeReadOnly ${id}] Using displayable imageUrl: ${imageUrl.substring(0,50)}...`);
      setEffectiveImageUrl(imageUrl);
      return;
    }

    // Priority 2: If imageUrl isn't displayable, but file_url (storage path) exists, fetch signed URL
    if (file_url) {
      console.log(`[ArticleNodeReadOnly ${id}] imageUrl not displayable, fetching signed URL for file_url: ${file_url}`);
      
      const fetchSignedUrl = async (path: string) => {
        try {
          const supabase = createClientComponentClient<Database>();
          console.log(`[ArticleNodeReadOnly ${id}] Attempting createSignedUrl for path: ${path}`);
          const { data: signedUrlData, error: signedUrlError } = await supabase
            .storage
            .from('images')
            .createSignedUrl(path, 3600); // 1 hour expiry
          
          if (signedUrlError) {
            console.error(`[ArticleNodeReadOnly ${id}] Failed createSignedUrl for path ${path}:`, signedUrlError);
            console.log(`[ArticleNodeReadOnly ${id}] Attempting getPublicUrl for path: ${path}`);
            const { data: publicUrlData } = supabase.storage.from('images').getPublicUrl(path);
            
            if (publicUrlData?.publicUrl) {
              console.log(`[ArticleNodeReadOnly ${id}] Using public URL for path ${path}:`, publicUrlData.publicUrl);
              setEffectiveImageUrl(publicUrlData.publicUrl);
            } else {
              console.warn(`[ArticleNodeReadOnly ${id}] Failed getPublicUrl for path ${path}. Setting image to null.`);
              setEffectiveImageUrl(null);
              setImageError(true); 
            }
          } else if (signedUrlData?.signedUrl) {
            console.log(`[ArticleNodeReadOnly ${id}] Successfully generated signed URL for path ${path}`);
            setEffectiveImageUrl(signedUrlData.signedUrl);
          } else {
             console.warn(`[ArticleNodeReadOnly ${id}] createSignedUrl returned no data/error for path ${path}. Setting image to null.`);
             setEffectiveImageUrl(null);
             setImageError(true);
          }
        } catch (error) {
          console.error(`[ArticleNodeReadOnly ${id}] Error in fetchSignedUrl for path ${path}:`, error);
          setEffectiveImageUrl(null);
          setImageError(true);
        }
      };
      
      fetchSignedUrl(file_url);
      return; // Don't fall through to the final fallback yet
    }

    // Fallback: Neither imageUrl nor file_url yielded a result
    console.log(`[ArticleNodeReadOnly ${id}] No displayable imageUrl or file_url found.`);
    setEffectiveImageUrl(null);
    
  }, [id, imageUrl, file_url]); // Depend on id, imageUrl, and file_url


  const handleImageError = () => {
    setImageError(true);
  };

  const handleArticleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    const linkToOpen = website_url ||
      (url && !website_url && !url.includes('/article-') && !url.includes('.jpg') && !url.includes('.png') ?
        url : null);
    if (linkToOpen) {
      window.open(linkToOpen, '_blank', 'noopener,noreferrer');
    }
  };

  const getDomain = () => {
    const sourceUrl = website_url ||
      (url && !url.includes('/article-') && !url.includes('.jpg') && !url.includes('.png') ? url : null);
    if (sourceUrl) {
      try {
        const urlObj = new URL(sourceUrl);
        return urlObj.hostname.replace('www.', '');
      } catch (e) {
        return 'website';
      }
    }
    return 'article';
  };

  const canOpenLink = !!(website_url || (url && !website_url && !url.includes('/article-') && !url.includes('.jpg') && !url.includes('.png')));

  return (
    <div
      className="absolute select-none flex flex-col bg-yellow-50 border-b-2 border-r-2 border-yellow-200 rounded-md shadow-lg p-4 transform -rotate-1 font-serif"
      style={{
        left: `${position.x}px`,
        top: `${position.y}px`,
        width: `${width}px`,
        height: `${height}px`,
        cursor: canOpenLink ? 'pointer' : 'default',
      }}
      onClick={canOpenLink ? handleArticleClick : undefined}
      data-nodrag={canOpenLink ? "true" : undefined}
    >
      {/* Tape Effect */}
      <div
        className="absolute top-0 left-1/2 z-10 pointer-events-none"
        style={{
          width: `${dynamicTapeWidthPx}px`,
          height: `${dynamicTapeHeightPx}px`,
          backgroundColor: 'rgba(245, 222, 179, 0.75)',
          borderLeft: '2px dashed rgba(0,0,0,0.15)',
          borderRight: '2px dashed rgba(0,0,0,0.15)',
          borderTop: '1px solid rgba(0,0,0,0.05)',
          borderBottom: '1px solid rgba(0,0,0,0.05)',
          boxShadow: '0 2px 3px rgba(0,0,0,0.15)',
          transform: `translateX(-50%) translateY(${dynamicTapeTranslateYPx}px)`,
        }}
      />

      {/* Header Area: Title */}
      <div className="relative flex justify-between items-start mb-3">
        <h3
          className="font-black text-stone-900 leading-tight article-title flex-grow mr-2"
          style={{ fontSize: `${titleFontSizeRem}rem` }}
        >
          {title}
        </h3>
      </div>
      
      {/* Domain and Date */}
      <div
        className="text-stone-500 mb-2 flex justify-between items-center border-b border-stone-200 pb-1"
        style={{ fontSize: `${metaFontSizeRem}rem` }}
      >
        <div className="uppercase tracking-wider">{getDomain()}</div>
        <div>
          {new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' })}
        </div>
      </div>

      {/* Image Section */}
      {effectiveImageUrl && !imageError && (
        <div className="w-full bg-stone-200 overflow-hidden border-b border-stone-300 flex-none">
          <img
            src={effectiveImageUrl}
            alt={title}
            className="w-full h-full object-cover"
            onError={handleImageError}
            referrerPolicy="no-referrer"
            loading="lazy"
          />
          <div
            className="italic text-stone-600 px-2 py-1 border-t border-stone-300 bg-stone-100"
            style={{ fontSize: `${metaFontSizeRem}rem` }}
          >
            {title} - Photo
          </div>
        </div>
      )}

      {/* Article Content - Ruled paper style */}
      <div
        className="p-3 text-stone-800 font-serif flex-1 overflow-auto min-h-0"
        style={{
          backgroundImage: `
            linear-gradient(to bottom, transparent 0.95rem, #e0e0e040 0.95rem, #e0e0e040 1rem),
            linear-gradient(to right, transparent 0.95rem, #e0e0e040 0.95rem, #e0e0e040 1rem)
          `,
          backgroundSize: '1rem 1rem',
          lineHeight: '1.5rem', // Adjust if needed based on font size and desired line spacing
        }}
      >
        {content ? (
          <div
            style={{
              height: '100%',
              columnCount: 2,
              columnGap: '0.75rem',
              wordBreak: 'break-word',
            }}
          >
            {content}
          </div>
        ) : (
           <div className="text-stone-500 italic" style={{ fontSize: `${contentFontSizeRem}rem` }}>No article content.</div>
        )}
      </div>
      
      {/* Footer */}
      <div
        className="mt-3 pt-2 border-t border-stone-200 text-stone-500 flex justify-between"
        style={{ fontSize: `${metaFontSizeRem}rem` }}
      >
        <span>Source: {getDomain()}</span>
        {canOpenLink && (
          <button
            className="hover:underline"
            onClick={handleArticleClick}
            data-nodrag="true"
          >
            Read full article
          </button>
        )}
      </div>
    </div>
  );
};

export default ArticleNodeReadOnly; 