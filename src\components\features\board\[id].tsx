import React, { useEffect } from 'react';
import { useRouter } from 'next/router';
import MainLayout from '@/components/layout/MainLayout';
import DetectiveBoard from '../detective/DetectiveBoard';

const Board: React.FC = () => {
  const router = useRouter();
  const { id } = router.query;
  
  useEffect(() => {
    // This could be used to load a specific board from storage in the future
    if (id) {
      console.log(`Loading board: ${id}`);
    }
  }, [id]);

  return (
    <MainLayout className="p-0">
      <DetectiveBoard />
    </MainLayout>
  );
};

export default Board; 