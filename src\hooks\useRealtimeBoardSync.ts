import { useEffect, useRef, useState, useCallback, useMemo } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { useAuth } from '@/context/AuthContext';
import type { RealtimeChannel, SupabaseClient } from '@supabase/supabase-js';
import type { Database } from '@/lib/database.types';

/**
 * Hook: useRealtimeBoardSync
 * -------------------------------------------
 * Manages a Presence channel per board (topic: `board-${boardId}`) and
 * conditionally spins-up a Broadcast channel when two or more users are
 * viewing the same board.  Returns helpers to emit and listen for
 * broadcast events plus a userCount for UI / logic needs.
 */
export interface RealtimeSyncReturn {
  /** Total number of connected users (including self) on this board */
  userCount: number;
  /** Derived flag – true when userCount >= 2 */
  isMultiUser: boolean;
  /**
   * Emits an event to other subscribers on the same board.
   * No-op if broadcast channel is not active (i.e. single-user).
   */
  broadcast: (event: string, payload: any) => void;
  /**
   * Subscribe to a broadcast event.  The handler receives the `payload`
   * that was sent by a peer.
   * @param event  Specific event name or '*' to listen to all.
   * @param handler Callback executed on each incoming message.
   */
  onBroadcast: (event: string | '*', handler: (payload: any) => void) => void;
}

export function useRealtimeBoardSync(boardId?: string): RealtimeSyncReturn {
  const { user } = useAuth();
  const supabase = useMemo(() => {
    console.log('🔧 Creating Supabase client for Realtime');
    return createClientComponentClient<Database>();
  }, []);

  const presenceChannelRef = useRef<RealtimeChannel | null>(null);
  const broadcastChannelRef = useRef<RealtimeChannel | null>(null);
  const broadcastHandlersRef = useRef<Map<string, (payload: any) => void>>(new Map());
  // Use ref instead of state to prevent re-renders
  const isInitializedRef = useRef<boolean>(false);

  const [userCount, setUserCount] = useState<number>(1);

  // Topic is namespaced to each board
  const topic = boardId ? `board-${boardId}` : null;
  
  // Log initial setup
  useEffect(() => {
    if (topic) {
      console.log(`🔗 Realtime sync for topic: ${topic}`);
    } else {
      console.warn('⚠️ No boardId provided to useRealtimeBoardSync');
    }
  }, [topic]);

  // Verify board access before connecting
  useEffect(() => {
    if (!topic || !boardId) return;
  
    // This security check ensures users can only subscribe to boards they have access to
    // It's a client-side check that supplements server-side security
    const checkBoardAccess = async () => {
      try {
        // Get current user - use AuthContext user if available, otherwise fetch fresh
        let userId = user?.id;
        
        if (!userId) {
          // Fallback to fresh user data if not available in context
          const { data: userData } = await supabase.auth.getUser();
          userId = userData.user?.id;
        }
        
        if (!userId) {
          console.warn('🔒 No authenticated user, cannot verify board access');
          return false;
        }
        
        // Check if user is board owner
        const { data: isOwner } = await supabase
          .from('boards')
          .select('id')
          .eq('id', boardId)
          .eq('user_id', userId)
          .maybeSingle();
          
        if (isOwner) return true;
        
        // Check if board is shared with user
        const { data: isShared } = await supabase
          .from('board_shares')
          .select('id')
          .eq('board_id', boardId)
          .eq('user_id', userId)
          .maybeSingle();
          
        if (isShared) return true;
        
        // Check if board is public
        const { data: isPublic } = await supabase
          .from('board_sharing')
          .select('id')
          .eq('board_id', boardId)
          .eq('public_board', true)
          .maybeSingle();
          
        if (isPublic) return true;
        
        // No access found
        console.warn('🚫 User does not have access to this board. Realtime connections will be rejected.');
        return false;
      } catch (err) {
        console.error('🔒 Exception checking board access:', err);
        return false;
      }
    };

    // We don't block connections here, since the server will apply RLS anyway
    // This just provides better user feedback and avoids unnecessary connection attempts
    checkBoardAccess().then(hasAccess => {
      if (!hasAccess) {
        // Maybe notify the user or take other actions
        console.warn('🚫 Not establishing realtime connection due to access restrictions');
      }
    });
  }, [topic, boardId, supabase]);

  /* --------------------------------------------------
   * Presence channel setup / teardown
   * -------------------------------------------------- */
  useEffect(() => {
    if (!topic) return;
    if (isInitializedRef.current) {
      console.log('🔄 Presence channel already initialized, skipping setup');
      return;
    }
    
    console.log('🔄 Setting up Presence channel for', topic);
    isInitializedRef.current = true;

    // Generate a random presence key for this client (must be unique)
    const presenceKey = `${Date.now()}-${Math.random().toString(36).slice(2)}`;

    try {
      // IMPORTANT: Make the channel name unique for presence vs broadcast
      // This is a key fix for ensuring proper separation
      const channel = supabase.channel(`presence-${topic}`, {
        config: {
          presence: { key: presenceKey }
        }
      });

      presenceChannelRef.current = channel;

      console.log('👂 Adding presence event listeners');

      // Add debugging listeners for all presence events
      channel
        .on('presence', { event: 'sync' }, () => {
          console.log('🔄 Presence sync event received');
          const state = channel.presenceState();
          console.log('🧑‍🤝‍🧑 Presence state:', state);
          
          const count = Object.values(state).reduce((acc, value) => {
            // `value` is an array of presences for a given key
            return acc + (Array.isArray(value) ? value.length : 0);
          }, 0);
          console.log(`👥 Setting user count to ${count || 1}`);
          setUserCount(count || 1); // default to 1 for safety
        })
        .on('presence', { event: 'join' }, ({ key, newPresences }) => {
          console.log('➕ Presence join:', { key, newPresences });
        })
        .on('presence', { event: 'leave' }, ({ key, leftPresences }) => {
          console.log('➖ Presence leave:', { key, leftPresences });
        })
        .subscribe(async status => {
          console.log(`🔌 Presence channel subscription status: ${status}`);
          
          if (status === 'SUBSCRIBED') {
            console.log('🏃‍♀️ Tracking presence with key:', presenceKey);
            try {
              await channel.track({ 
                online_at: new Date().toISOString(),
                client_id: presenceKey
              });
              console.log('✅ Successfully tracked presence');
            } catch (err) {
              console.error('❌ Error tracking presence:', err);
            }
          }
        });

      return () => {
        console.log('🧹 Cleaning up presence channel');
        channel.unsubscribe();
        presenceChannelRef.current = null;
        isInitializedRef.current = false; // Reset on unmount
        setUserCount(1);
      };
    } catch (err) {
      console.error('❌ Error setting up presence channel:', err);
      isInitializedRef.current = false; // Reset on error
      return () => {};
    }
  }, [topic, supabase]); // Removed isInitialized from dependency array

  /* --------------------------------------------------
   * Broadcast channel lifecycle: active only if >=2 users
   * -------------------------------------------------- */
  useEffect(() => {
    if (!topic) return;

    console.log(`🔄 Broadcast channel effect - userCount: ${userCount}`);

    // Need a broadcast channel while multiple users are connected
    if (userCount >= 2 && !broadcastChannelRef.current) {
      console.log('📡 Creating broadcast channel - multiple users detected');
      try {
        // IMPORTANT: Make this a separate channel from presence for clarity
        const bc = supabase.channel(`broadcast-${topic}`, {
          config: {
            broadcast: {
              self: false // Don't receive own messages
            }
          }
        });
        
        broadcastChannelRef.current = bc;
        
        bc.subscribe(status => {
          console.log(`🔌 Broadcast channel subscription status: ${status}`);
          
          if (status === 'SUBSCRIBED') {
            broadcastHandlersRef.current.forEach((handler, event) => {
              console.log(`🔄 Re-registering handler for event "${event}"`);
              bc.on('broadcast', { event }, (message) => {
                console.log(`📨 Received broadcast event "${event}":`, message);
                handler(message.payload);
              });
            });
          }
        });
        
        console.log('✅ Broadcast channel created and subscribed');
      } catch (err) {
        console.error('❌ Error creating broadcast channel:', err);
      }
    }

    // Tear down when returning to single user
    if (userCount < 2 && broadcastChannelRef.current) {
      console.log('👋 Removing broadcast channel - returned to single user');
      broadcastChannelRef.current.unsubscribe();
      broadcastChannelRef.current = null;
    }
  }, [userCount, topic, supabase]);

  /* --------------------------------------------------
   * Helper wrappers
   * -------------------------------------------------- */
  const broadcast = useCallback((event: string, payload: any) => {
    const bc = broadcastChannelRef.current;
    if (!bc) {
      console.log('⚠️ Broadcast called but no channel exists (normal in single-user mode)');
      return;
    }
    
    bc.send({
      type: 'broadcast',
      event,
      payload
    })
      .catch(err => console.error('❌ Error sending broadcast:', err));
  }, []);

  const onBroadcast = useCallback(
    (event: string | '*', handler: (payload: any) => void) => {
      const bc = broadcastChannelRef.current;
      // Only register if not already present
      if (!broadcastHandlersRef.current.has(event)) {
        broadcastHandlersRef.current.set(event, handler);
        if (bc) {
          console.log(`🔔 Subscribing to broadcast event: "${event}"`);
          bc.on('broadcast', { event }, (message) => {
            console.log(`📨 Received broadcast event "${event}":`, message);
            handler(message.payload);
          });
        }
      }
    },
    []
  );

  return {
    userCount,
    isMultiUser: userCount >= 2,
    broadcast,
    onBroadcast
  };
} 