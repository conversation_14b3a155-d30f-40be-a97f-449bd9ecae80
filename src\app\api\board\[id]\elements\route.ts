import { NextRequest, NextResponse } from 'next/server';
import type { Database, Tables } from '@/lib/database.types';
import type { User } from '@supabase/supabase-js';
import { getAuthenticatedUser } from '@/utils/authUtils';

// Import shared utilities
import { 
    TransformedElement, 
    processElementsInBatches 
} from '@/utils/apiBoardUtils';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const boardId = params.id;

  const { searchParams } = new URL(request.url);
  const offset = parseInt(searchParams.get('offset') || '0', 10);
  const limit = Math.min(parseInt(searchParams.get('limit') || '20', 10), 50);
  const skipImages = searchParams.get('skipImages') === 'true';

  if (!boardId) {
    return NextResponse.json({ message: 'Board ID is required' }, { status: 400 });
  }
  if (isNaN(offset) || isNaN(limit) || offset < 0 || limit <= 0) {
    return NextResponse.json({ message: 'Invalid offset or limit' }, { status: 400 });
  }

  try {
    const { user: currentUser, supabase } = await getAuthenticatedUser();
    const { data: boardMeta, error: boardMetaError } = await supabase
      .from('boards')
      .select('user_id, board_sharing(public_board)')
      .eq('id', boardId)
      .single();

    if (boardMetaError || !boardMeta) {
      console.error(`Error fetching board metadata for elements ${boardId}:`, boardMetaError);
      return NextResponse.json({ message: 'Failed to fetch board metadata' }, { status: (boardMetaError && boardMetaError.code === 'PGRST116') ? 404 : 500 });
    }
    
    const ownerID = boardMeta.user_id;
    const isPublic = (boardMeta.board_sharing as Tables<'board_sharing'>[] ?? []).some((s: Tables<'board_sharing'>) => s.public_board === true);

    // currentUser is already available from above

    const { data: elements, error: elementsError } = await supabase
      .from('elements')
      .select('*')
      .eq('board_id', boardId)
      .order('created_at', { ascending: true })
      .range(offset, offset + limit - 1);

    if (elementsError) {
      console.error(`Error fetching elements for board ${boardId}:`, elementsError);
      return NextResponse.json({ message: 'Failed to fetch elements' }, { status: 500 });
    }

    if (!elements) {
      return NextResponse.json({ elements: [], offset, limit }, { status: 200 }); 
    }

    const transformedElements = await processElementsInBatches(
      elements,
      supabase,
      boardId,
      isPublic,
      currentUser,
      ownerID,
      Math.min(limit, 15),
      skipImages
    );
    
    return NextResponse.json({
      elements: transformedElements,
      offset,
      limit,
    }, { status: 200 });

  } catch (error: any) {
    console.error(`Unexpected error fetching elements for board ${boardId}:`, error);
    return NextResponse.json({ message: 'Failed to fetch elements', error: error.message }, { status: 500 });
  }
} 