'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Loader2 } from 'lucide-react';
import { useAuth } from '@/context/AuthContext';
import { Button } from '@/components/ui/button';
import { Modal } from '@/components/ui/modal';

interface VerificationModalProps {
  isOpen: boolean;
  onClose: () => void;
  email: string;
  userId?: string;
  verificationSentAt?: string;
  onSuccess?: () => void;
}

interface FormErrors {
  verificationCode?: string;
  general?: string;
}

export function VerificationModal({ 
  isOpen, 
  onClose, 
  email,
  userId,
  verificationSentAt,
  onSuccess 
}: VerificationModalProps) {
  const { verifyEmail, resendVerificationCode, supabase } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const [verificationCode, setVerificationCode] = useState(['', '', '', '', '', '']);
  const [errors, setErrors] = useState<FormErrors>({});
  const [countdown, setCountdown] = useState(0);
  
  const codeRefs = [
    useRef<HTMLInputElement>(null),
    useRef<HTMLInputElement>(null),
    useRef<HTMLInputElement>(null),
    useRef<HTMLInputElement>(null),
    useRef<HTMLInputElement>(null),
    useRef<HTMLInputElement>(null),
  ];

  // Set up countdown timer for resend code
  useEffect(() => {
    if (verificationSentAt) {
      const sentTime = new Date(verificationSentAt).getTime();
      const currentTime = Date.now();
      const timeDiff = Math.max(0, sentTime + 60000 - currentTime); // 60 seconds cooldown
      
      if (timeDiff > 0) {
        setCountdown(Math.ceil(timeDiff / 1000));
        
        const timer = setInterval(() => {
          setCountdown(prev => {
            if (prev <= 1) {
              clearInterval(timer);
              return 0;
            }
            return prev - 1;
          });
        }, 1000);
        
        return () => clearInterval(timer);
      }
    }
  }, [verificationSentAt]);

  const validateForm = () => {
    const newErrors: FormErrors = {};

    if (verificationCode.some(digit => !digit)) {
      newErrors.verificationCode = 'Please enter the complete verification code';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setIsLoading(true);
    try {
      await verifyEmail(email, verificationCode.join(''), userId);

      console.log("Verification API call successful.");

      console.log("Reloading page after successful verification...");
      window.location.reload();

      if (onSuccess) {
        onSuccess();
      }
      
      onClose();

    } catch (error: any) {
      console.error('Verification error:', error);
      if (error.message.includes('invalid') || error.message.includes('expired')) {
        setErrors({ verificationCode: 'Invalid or expired verification code' });
      } else {
        setErrors({ general: error.message || 'Failed to verify email' });
      }
      setIsLoading(false);
    }
  };

  const handleResendCode = async () => {
    if (countdown > 0) return;
    
    setIsResending(true);
    setErrors({});
    
    try {
      await resendVerificationCode(email, userId);
      
      // Reset the verification code inputs
      setVerificationCode(['', '', '', '', '', '']);
      
      // Reset the countdown
      setCountdown(60);
      
      const timer = setInterval(() => {
        setCountdown(prev => {
          if (prev <= 1) {
            clearInterval(timer);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
      
      // Focus the first input
      codeRefs[0].current?.focus();
      
      setErrors({ general: 'Verification code has been resent to your email' });
    } catch (error: any) {
      console.error('Resend code error:', error);
      setErrors({ general: error.message || 'Failed to resend verification code' });
    } finally {
      setIsResending(false);
    }
  };

  const handleVerificationCodeChange = (index: number, value: string) => {
    if (value.length > 1) {
      // If pasting a full code
      const chars = value.slice(0, 6).split('');
      const newCode = [...verificationCode];
      chars.forEach((char, i) => {
        if (i + index < 6) {
          newCode[i + index] = char.toUpperCase();
        }
      });
      setVerificationCode(newCode);
      
      // Focus last input if there are 6 characters
      if (chars.length + index >= 6) {
        codeRefs[5].current?.focus();
      } else if (chars.length > 0) {
        codeRefs[index + chars.length]?.current?.focus();
      }
    } else {
      // Single character input
      const newCode = [...verificationCode];
      newCode[index] = value.toUpperCase();
      setVerificationCode(newCode);

      // Auto-focus next input
      if (value && index < 5) {
        codeRefs[index + 1].current?.focus();
      }
    }

    setErrors(prev => ({ ...prev, verificationCode: undefined, general: undefined }));
  };

  const handleVerificationKeyDown = (index: number, e: React.KeyboardEvent) => {
    if (e.key === 'Backspace' && !verificationCode[index] && index > 0) {
      // Move to previous input on backspace if current input is empty
      codeRefs[index - 1].current?.focus();
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Verify Your Email">
      <p className="text-sm text-gray-400 text-center mb-8">
        We've sent a verification code to <strong className="text-white">{email}</strong>
      </p>

      <form className="space-y-6" onSubmit={handleSubmit}>
        {errors.general && (
          <div className={errors.general.includes('resent') 
            ? "text-sm text-center p-2 rounded-md border bg-green-900/10 border-green-500/20 text-green-400" 
            : "text-sm text-center p-2 rounded-md border bg-red-900/10 border-red-500/20 text-red-400"}>
            {errors.general}
          </div>
        )}

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Enter verification code
          </label>
          <div className="grid grid-cols-6 gap-2">
            {verificationCode.map((digit, index) => (
              <input
                key={index}
                ref={codeRefs[index]}
                type="text"
                maxLength={6}
                className={errors.verificationCode
                  ? "w-full aspect-square border rounded-md text-center text-xl font-bold text-white focus:outline-none focus:ring-1 border-red-500 bg-red-900/10 focus:border-red-500 focus:ring-red-500"
                  : "w-full aspect-square border rounded-md text-center text-xl font-bold text-white focus:outline-none focus:ring-1 border-white/10 bg-black/20 focus:border-noir-accent focus:ring-noir-accent"
                }
                value={digit}
                onChange={(e) => handleVerificationCodeChange(index, e.target.value)}
                onKeyDown={(e) => handleVerificationKeyDown(index, e)}
                autoFocus={index === 0}
              />
            ))}
          </div>
          {errors.verificationCode && (
            <p className="mt-2 text-sm text-red-400">{errors.verificationCode}</p>
          )}
        </div>

        <div>
          <Button
            type="submit"
            disabled={isLoading || verificationCode.some(digit => !digit)}
            className="w-full bg-noir-accent hover:bg-noir-accent/90 relative"
          >
            {isLoading ? (
              <Loader2 className="h-5 w-5 animate-spin" />
            ) : (
              'Verify Email'
            )}
          </Button>
        </div>
      </form>

      <div className="mt-6 text-center">
        <p className="text-sm text-gray-400 mb-2">
          Didn't receive a code? Check your spam folder
        </p>
        <button
          type="button"
          className={countdown > 0
            ? "text-sm text-gray-500 cursor-not-allowed"
            : "text-sm text-noir-accent hover:text-noir-accent/80"
          }
          onClick={handleResendCode}
          disabled={countdown > 0 || isResending}
        >
          {isResending ? (
            <span className="flex items-center justify-center">
              <Loader2 className="h-3 w-3 animate-spin mr-2" />
              Resending...
            </span>
          ) : countdown > 0 ? (
            `Resend code in ${countdown}s`
          ) : (
            'Resend verification code'
          )}
        </button>
      </div>
    </Modal>
  );
} 