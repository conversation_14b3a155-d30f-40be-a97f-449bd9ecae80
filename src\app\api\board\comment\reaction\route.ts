import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import type { Database } from '@/lib/database.types';

import { getAuthenticatedUser } from '@/utils/authUtils';
/**
 * Add a reaction (like/dislike) to a comment
 * POST /api/board/comment/reaction
 * Body: { commentId: string, type: 'like' | 'dislike' }
 */
export async function POST(request: NextRequest) {
  try {
    const { user, error: authError, supabase } = await getAuthenticatedUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Authentication required to react to comments' }, { status: 401 });
    }

    const { commentId, type } = await request.json();

    if (!commentId) {
      return NextResponse.json({ error: 'Comment ID is required' }, { status: 400 });
    }

    if (!type || !['like', 'dislike'].includes(type)) {
      return NextResponse.json({ error: 'Valid reaction type (like/dislike) is required' }, { status: 400 });
    }

    // Check if the comment exists
    const { data: comment, error: commentError } = await supabase
      .from('comments')
      .select('id, likes, dislikes')
      .eq('id', commentId)
      .single();

    if (commentError) {
      console.error('Error fetching comment:', commentError);
      return NextResponse.json({ error: 'Comment not found' }, { status: 404 });
    }

    // Check if user already has a reaction to this comment
    const { data: existingReaction, error: reactionCheckError } = await supabase
      .from('comment_reactions')
      .select('id, reaction_type')
      .eq('user_id', user.id)
      .eq('comment_id', commentId)
      .single();

    // If user already has the same reaction, return error
    if (existingReaction && existingReaction.reaction_type === type) {
      return NextResponse.json({ 
        error: `You have already ${type}d this comment`,
        reaction: existingReaction
      }, { status: 400 });
    }

    // If user has a different reaction, update it
    if (existingReaction) {
      // Then update the reaction type
      const { data: updatedReaction, error: updateError } = await supabase
        .from('comment_reactions')
        .update({ 
          reaction_type: type 
        })
        .eq('id', existingReaction.id)
        .select()
        .single();

      if (updateError) {
        console.error('Error updating reaction:', updateError);
        return NextResponse.json({ error: 'Failed to update reaction' }, { status: 500 });
      }

      return NextResponse.json({
        success: true,
        message: `Reaction changed to ${type}`,
        reaction: updatedReaction,
        likes: comment.likes || 0,
        dislikes: comment.dislikes || 0
      });
    }

    // If no existing reaction, create a new one
    const { data: newReaction, error: insertError } = await supabase
      .from('comment_reactions')
      .insert({
        user_id: user.id,
        comment_id: commentId,
        reaction_type: type
      })
      .select()
      .single();

    if (insertError) {
      console.error('Error adding reaction:', insertError);
      return NextResponse.json({ error: 'Failed to add reaction' }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      message: `Comment ${type}d successfully`,
      reaction: newReaction,
      likes: comment.likes || 0,
      dislikes: comment.dislikes || 0
    });
  } catch (error) {
    console.error('Error processing reaction:', error);
    return NextResponse.json({ error: 'Failed to process reaction' }, { status: 500 });
  }
}

/**
 * Remove a reaction from a comment
 * DELETE /api/board/comment/reaction?commentId=123
 */
export async function DELETE(request: NextRequest) {
  try {
    const { user, error: authError, supabase } = await getAuthenticatedUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const commentId = searchParams.get('commentId');

    if (!commentId) {
      return NextResponse.json({ error: 'Comment ID is required' }, { status: 400 });
    }

    // Check if the comment exists
    const { data: comment, error: commentError } = await supabase
      .from('comments')
      .select('id, likes, dislikes')
      .eq('id', commentId)
      .single();

    if (commentError) {
      console.error('Error fetching comment:', commentError);
      return NextResponse.json({ error: 'Comment not found' }, { status: 404 });
    }

    // Check if the user has a reaction to this comment
    const { data: existingReaction, error: reactionCheckError } = await supabase
      .from('comment_reactions')
      .select('id, reaction_type')
      .eq('user_id', user.id)
      .eq('comment_id', commentId)
      .single();

    if (!existingReaction) {
      return NextResponse.json({ error: 'You have not reacted to this comment' }, { status: 400 });
    }

    // Remove the reaction
    const { error: deleteError } = await supabase
      .from('comment_reactions')
      .delete()
      .eq('id', existingReaction.id);

    if (deleteError) {
      console.error('Error removing reaction:', deleteError);
      return NextResponse.json({ error: 'Failed to remove reaction' }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      message: 'Reaction removed successfully',
      likes: comment.likes || 0,
      dislikes: comment.dislikes || 0
    });
  } catch (error) {
    console.error('Error removing reaction:', error);
    return NextResponse.json({ error: 'Failed to remove reaction' }, { status: 500 });
  }
}

/**
 * Get a user's reaction to a comment and the total counts
 * GET /api/board/comment/reaction?commentId=123
 */
export async function GET(request: NextRequest) {
  try {
    const { user, error: authError, supabase } = await getAuthenticatedUser();

    const { searchParams } = new URL(request.url);
    const commentId = searchParams.get('commentId');

    if (!commentId) {
      return NextResponse.json({ error: 'Comment ID is required' }, { status: 400 });
    }

    // Get comment reaction counts
    const { data: comment, error: commentError } = await supabase
    .from('comments')
    .select('likes, dislikes')
    .eq('id', commentId)
    .single();
  
      if (commentError) {
        console.error('Error fetching comment:', commentError);
        return NextResponse.json({ error: 'Comment not found' }, { status: 404 });
      }

    // Initialize the response object with typed userReaction
    const response: {
      likes: number;
      dislikes: number;
      userReaction: string | null;
    } = {
      likes: comment.likes || 0,
      dislikes: comment.dislikes || 0,
      userReaction: null
    };

    // If user is authenticated, check if they have reacted to this comment
    if (user) {
      const { data: userReaction, error: reactionError } = await supabase
        .from('comment_reactions')
        .select('reaction_type')
        .eq('user_id', user.id)
        .eq('comment_id', commentId)
        .single();

      if (!reactionError && userReaction) {
        response.userReaction = userReaction.reaction_type;
      }
    }

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error checking reaction status:', error);
    return NextResponse.json({ error: 'Failed to check reaction status' }, { status: 500 });
  }
} 