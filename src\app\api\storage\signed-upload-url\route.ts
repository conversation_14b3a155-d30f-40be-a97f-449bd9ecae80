import { NextRequest, NextResponse } from 'next/server';
import { getAuthenticatedUser } from '@/utils/authUtils';
import { v4 as uuidv4 } from 'uuid';

const BUCKET_NAME = 'images';
const MAX_FILE_SIZE_MB = 10;
const MAX_FILE_SIZE_BYTES = MAX_FILE_SIZE_MB * 1024 * 1024;

export async function POST(request: NextRequest) {
  try {
    const { user, error: authError, supabase } = await getAuthenticatedUser();
    
    if (authError || !user) {
      console.error("Signed Upload URL Error:", authError);
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }
    
    const { fileName, fileType, fileSize } = await request.json();

    if (!fileName || !fileType || !fileSize) {
      return NextResponse.json({ message: 'Missing fileName, fileType, or fileSize' }, { status: 400 });
    }

    if (fileSize > MAX_FILE_SIZE_BYTES) {
        return NextResponse.json(
            { message: `File is too large. Maximum size is ${MAX_FILE_SIZE_MB}MB.` },
            { status: 413 } // 413 Payload Too Large
        );
    }
    
    if (!fileType.startsWith('image/')) {
        return NextResponse.json({ message: 'Only image files are allowed' }, { status: 400 });
    }

    const fileExt = fileName.split('.').pop();
    const filePath = `${user.id}/${uuidv4()}.${fileExt}`;
    
    const { data, error } = await supabase.storage
      .from(BUCKET_NAME)
      .createSignedUploadUrl(filePath);

    if (error) {
      console.error("Supabase Signed URL Error:", error.message);
      return NextResponse.json({ message: 'Failed to create signed URL', error: error.message }, { status: 500 });
    }
    
    return NextResponse.json({ success: true, ...data });

  } catch (error) {
    console.error("Server Error:", error);
    return NextResponse.json({ message: 'Server error', error: String(error) }, { status: 500 });
  }
} 