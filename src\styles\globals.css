@import url('https://fonts.googleapis.com/css2?family=Gaegu:wght@300&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
    --radius: 0.5rem;
    --comment-font: 'Comic Sans MS', 'Comic Neue', cursive;
    --paper-bg: #f5f0e1;
    --sticky-yellow: #fff7e6;
    --sticky-pink: #ffe6e6;
    --kraft-paper: #f0e6d1;
    --connector-color: rgba(0, 0, 0, 0.2);
    --text-color: #333;
    --subtle-text: #666;
    --button-bg: #e0d8c7;
    --button-border: #c7bfac;
    --accent-color: #8b4513;
  }
}

@layer base {
  * {
    @apply border-border selection:bg-white/10 selection:text-white;
  }

  body {
    @apply bg-noir text-foreground overflow-x-hidden;
    font-feature-settings: "ss01", "ss02", "cv01", "cv02", "cv03";
    font-family: 'Inter', sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    position: relative;
  }

  html {
    @apply scroll-smooth;
    overscroll-behavior: none;
  }
}

/* Add a new class for when the board is active */ 
.body-no-scroll-for-board,
.body-no-scroll-for-board #__next {
  height: 100vh;
  overflow: hidden;
  overscroll-behavior: none;
}

/* Ensure html also respects this when body has the class */
html:has(body.body-no-scroll-for-board) {
  height: 100vh;
  overflow: hidden;
  overscroll-behavior: none;
}

@layer components {
  .readable-font-wrapper {
    font-family: 'Inter', sans-serif;
  }
  
  .readable-font-wrapper h1, 
  .readable-font-wrapper h2, 
  .readable-font-wrapper h3, 
  .readable-font-wrapper h4,
  .readable-font-wrapper p,
  .readable-font-wrapper div,
  .readable-font-wrapper span {
    font-family: 'Inter', sans-serif;
  }
  
  .handwritten-font {
    font-family: 'Gaegu', cursive;
  }
  
  .ai-chat-font,
  .ai-chat-font * {
    font-family: 'Calibri', 'Arial', sans-serif !important;
  }
}

@layer utilities {
  .scrollbar-none {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-none::-webkit-scrollbar {
    display: none;
  }

  .glass-morphism {
    @apply backdrop-blur-xl bg-white/5 border border-white/10 shadow-[0_4px_12px_-2px_rgba(0,0,0,0.3)];
  }
  
  .neo-blur {
    @apply backdrop-blur-xl bg-black/40 border border-white/10;
  }
  
  .noir-gradient {
    @apply bg-gradient-to-b from-noir-50 to-noir-300;
  }
  
  .detective-text {
    @apply font-serif tracking-wide;
  }
  
  .film-grain {
    position: relative;
  }
  
  .film-grain::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.05;
    pointer-events: none;
    background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E");
  }
  
  .sticky-shadow {
    box-shadow: 0 1px 2px rgba(0,0,0,0.15), 
                0 2px 4px rgba(0,0,0,0.12),
                0 4px 8px rgba(0,0,0,0.1);
  }
  
  .connection-line {
    stroke-dasharray: 5, 5;
    animation: dash 30s linear infinite;
  }
  
  @keyframes dash {
    to {
      stroke-dashoffset: 1000;
    }
  }
}

#__next {
  /* Remove width, height, overflow from here - will be handled by the conditional class */
  /* You might want to keep width: 100% if it doesn't cause issues, */
  /* or ensure its parent (body) handles width correctly. */
  /* For now, let's remove to rely on the conditional class for board view. */
}

/* Custom styles for the vertical zoom slider */
.zoom-slider {
  -webkit-appearance: none;
  appearance: none;
  width: 128px; /* Corresponds to h-32 in Tailwind */
  height: 4px; /* Corresponds to h-1 */
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 9999px;
  cursor: pointer;
  transform: rotate(-90deg);
  outline: none;
  transition: opacity 0.2s;
}

.zoom-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  background-color: white;
  border-radius: 50%;
  cursor: pointer;
  border: 2px solid #333;
}

.zoom-slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  background-color: white;
  border-radius: 50%;
  cursor: pointer;
  border: 2px solid #333;
}