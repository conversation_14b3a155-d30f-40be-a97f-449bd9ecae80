import { ItemPlugin, ItemRegistry, BoardItem, Position } from '../types';
import { v4 as uuidv4 } from 'uuid';

/**
 * Implementation of the ItemRegistry interface
 */
export class ItemRegistryImpl implements ItemRegistry {
  private plugins: Map<string, ItemPlugin> = new Map();

  /**
   * Register a new item type plugin
   */
  registerItemType(plugin: ItemPlugin): void {
    if (this.plugins.has(plugin.type)) {
      console.warn(`Item type '${plugin.type}' is already registered. Overwriting.`);
    }
    this.plugins.set(plugin.type, plugin);
  }

  /**
   * Get all registered item type plugins
   */
  getItemTypes(): ItemPlugin[] {
    return Array.from(this.plugins.values());
  }

  /**
   * Get an item plugin by type
   */
  getItemPlugin(type: string): ItemPlugin | undefined {
    return this.plugins.get(type);
  }

  /**
   * Create a new item of the specified type
   */
  createItem(type: string, position: Position): BoardItem | null {
    const plugin = this.plugins.get(type);
    if (!plugin) {
      console.error(`No plugin registered for item type: ${type}`);
      return null;
    }

    return {
      id: uuidv4(),
      type: plugin.type,
      ...plugin.defaultProps,
      position
    } as BoardItem;
  }
}

/**
 * Singleton instance of the item registry
 */
export const itemRegistry = new ItemRegistryImpl();

// Track initialization state
let isInitialized = false;

/**
 * Register default item types
 */
export const initializeDefaultItemTypes = (): void => {
  // Prevent multiple initializations
  if (isInitialized) {
    return;
  }
  
  console.log("Initializing default item types");
  isInitialized = true;
  
  // Register sticky note type (yellow)
  itemRegistry.registerItemType({
    type: 'sticky-yellow',
    displayName: 'Sticky Note (Yellow)',
    component: null as any, // Will be set in component registry
    toolbarIcon: null as any, // Will be set in component registry
    height: 256,
    width: 256,
    defaultProps: {
      content: 'Double-click to add text',
      color: 'yellow',
    },
    factory: (position) => ({
      id: uuidv4(),
      type: 'sticky-yellow',
      content: 'Double-click to add text',
      color: 'yellow',
      position
    })
  });
  
  // Register sticky note type (red)
  itemRegistry.registerItemType({
    type: 'sticky-red',
    displayName: 'Sticky Note (Red)',
    component: null as any, // Will be set in component registry
    toolbarIcon: null as any, // Will be set in component registry
    height: 256,
    width: 256,
    defaultProps: {
      content: 'Double-click to add text',
      color: 'red',
    },
    factory: (position) => ({
      id: uuidv4(),
      type: 'sticky-red',
      content: 'Double-click to add text',
      color: 'red',
      position
    })
  });
  
  // Register sticky note type (blue)
  itemRegistry.registerItemType({
    type: 'sticky-blue',
    displayName: 'Sticky Note (Blue)',
    component: null as any, // Will be set in component registry
    toolbarIcon: null as any, // Will be set in component registry
    height: 256,
    width: 256,
    defaultProps: {
      content: 'Double-click to add text',
      color: 'blue',
    },
    factory: (position) => ({
      id: uuidv4(),
      type: 'sticky-blue',
      content: 'Double-click to add text',
      color: 'blue',
      position
    })
  });

  // Register text note type
  itemRegistry.registerItemType({
    type: 'text',
    displayName: 'Text Note',
    component: null as any,
    toolbarIcon: null as any,
    height: 50,
    width: 200,
    defaultProps: {
      content: 'Text note'
    },
    factory: (position) => ({
      id: uuidv4(),
      type: 'text',
      content: 'Text note',
      position
    })
  });

  // Register article type
  itemRegistry.registerItemType({
    type: 'article',
    displayName: 'Article',
    component: null as any,
    toolbarIcon: null as any,
    height: 800,
    width: 400,
    defaultProps: {
      title: '',
      url: '',
      content: 'No notes yet'
    },
    factory: (position) => ({
      id: uuidv4(),
      type: 'article',
      title: '',
      url: '',
      content: 'No notes yet',
      position
    })
  });

  // Register image type
  itemRegistry.registerItemType({
    type: 'image',
    displayName: 'Image',
    component: null as any,
    toolbarIcon: null as any,
    height: 400,
    width: 400,
    defaultProps: {
      imageUrl: '',
      alt: '',
      content: 'Image description'
    },
    factory: (position) => ({
      id: uuidv4(),
      type: 'image',
      content: 'Image description',
      position,
      imageUrl: '',
      alt: ''
    })
  });
}; 