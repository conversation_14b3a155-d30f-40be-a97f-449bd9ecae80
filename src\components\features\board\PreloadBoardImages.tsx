'use client';

import React, { useEffect } from 'react';
import { BoardItem } from '@/hooks/useBoardsData';

interface PreloadBoardImagesProps {
  boards: BoardItem[];
  preloadCount?: number;
}

/**
 * Component that preloads board preview images in the background
 * This improves perceived performance when users scroll down to see more boards
 */
const PreloadBoardImages: React.FC<PreloadBoardImagesProps> = ({ 
  boards,
  preloadCount = 3
}) => {
  useEffect(() => {
    // Only preload valid images that are not already visible in the initial view
    const imagesToPreload = boards
      .slice(0, preloadCount)
      .filter(board => board.previewImageUrl && typeof board.previewImageUrl === 'string');
    
    if (imagesToPreload.length === 0) return;
    
    // Create an array to track preloaded images
    const preloadedImages: HTMLImageElement[] = [];
    
    // Preload each image
    imagesToPreload.forEach(board => {
      if (!board.previewImageUrl) return;
      
      const img = new Image();
      img.src = board.previewImageUrl;
      preloadedImages.push(img);
    });
    
    // Clean up preloaded images when component unmounts
    return () => {
      preloadedImages.forEach(img => {
        img.src = '';
      });
    };
  }, [boards, preloadCount]);

  // This component doesn't render anything visible
  return null;
};

export default PreloadBoardImages; 