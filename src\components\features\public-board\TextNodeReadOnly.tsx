import React from 'react';
import { Position } from '@/types';
import AutoFitText from '@/components/ui/AutoFitText';

interface TextNodeReadOnlyProps {
  id: string;
  content: string;
  position: Position;
  scale: number;
  width?: number;
  height?: number;
}

const TextNodeReadOnly: React.FC<TextNodeReadOnlyProps> = ({
  content,
  position,
  width,
  height,
}) => {
  return (
    <div
      className="absolute detective-node"
      style={{
        left: `${position.x}px`,
        top: `${position.y}px`,
        width: `${width ?? 200}px`,
        height: `${height ?? 50}px`,
      }}
    >
      <div className="relative w-full h-full">
        <div 
          className="text-white p-2 handwritten-font w-full h-full overflow-hidden"
        >
          <AutoFitText
            mode="multi"
            className="handwritten-font"
            style={{ width: '100%', height: '100%' }}
            parentWidth={width}
            parentHeight={height}
          >
            {content}
          </AutoFitText>
        </div>
      </div>
    </div>
  );
};

export default TextNodeReadOnly; 