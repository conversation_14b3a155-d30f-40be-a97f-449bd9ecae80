# Detective Board

This is a [Next.js](https://nextjs.org) project using the App Router for modern Next.js features and routing.

## Project Structure

The project uses the App Router structure:

```
/src
  /app - Next.js App Router pages and routes
  /components - React components
  /context - React context providers
  /lib - Utility functions and services
  /styles - Global styles
  /types - TypeScript types and interfaces
/public - Static assets
/prisma - Database schema and migrations
```

## Getting Started

First, install dependencies:

```bash
npm install
```

Then, run the development server:

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Environment Variables

The project requires the following environment variables:

```
# Authentication
NEXTAUTH_SECRET=your-secret
NEXTAUTH_URL=http://localhost:3000

# Email (for verification)
MAILERSEND_API_KEY=your-mailersend-api-key
EMAIL_FROM=<EMAIL>

# Supabase (for database)
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
```

## Learn More

To learn more about Next.js and the App Router:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [App Router Documentation](https://nextjs.org/docs/app) - learn about the new App Router features.
