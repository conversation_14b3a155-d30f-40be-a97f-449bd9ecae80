---
description: overview of components being used
globs: 
alwaysApply: false
---
# Components Guide

This directory contains all the React components used in the Detective Board application, organized by their scope and purpose.

## High-Level Structure

-   **`src/components/features`**: Contains components implementing specific application features, often composed of smaller UI and layout components. These are typically higher-level components related to core domain concepts like the board, authentication, dashboard, etc.
-   **`src/components/layout`**: Contains components responsible for the overall structure and layout of pages or sections of the application.
-   **`src/components/ui`**: Contains generic, reusable UI elements (like buttons, inputs, modals) that are presentation-focused and application-agnostic.

## Detailed Breakdown

### `src/components/features`

Components related to specific application functionalities.

-   **`auth/`**: Components related to user authentication flows.
    -   [SignInModal.tsx](mdc:src/components/features/auth/SignInModal.tsx): Modal for user sign-in.
    -   [SignUpModal.tsx](mdc:src/components/features/auth/SignUpModal.tsx): Modal for user registration.
    -   [VerificationModal.tsx](mdc:src/components/features/auth/VerificationModal.tsx): Modal for email verification.
    -   [PasswordProtectionWrapper.tsx](mdc:src/components/features/auth/PasswordProtectionWrapper.tsx): Wrapper component, likely for routes requiring authentication.
    -   [PasswordProtection.tsx](mdc:src/components/features/auth/PasswordProtection.tsx): Component for password protection logic/UI.
-   **`board/`**: Components related to board management outside the main detective board interface.
    -   [RecentBoards.tsx](mdc:src/components/features/board/RecentBoards.tsx): Displays a list of recently accessed boards.
    -   [ShareModal.tsx](mdc:src/components/features/board/ShareModal.tsx): Modal for managing board sharing settings.
    -   [BoardPreview.tsx](mdc:src/components/features/board/BoardPreview.tsx): Component to display a preview image or summary of a board.
    -   [DeleteConfirmationModal.tsx](mdc:src/components/features/board/DeleteConfirmationModal.tsx): Modal to confirm board deletion.
    -   [[id].tsx](mdc:src/components/features/board/%5Bid%5D.tsx): Likely a dynamic route component for displaying a specific board page.
-   **`comments/`**: (Currently empty) Intended for components related to commenting features.
-   **`dashboard/`**: Components for the user dashboard.
    -   [PopularBoards.tsx](mdc:src/components/features/dashboard/PopularBoards.tsx): Displays popular or featured public boards.
    -   [Dashboard.tsx](mdc:src/components/features/dashboard/Dashboard.tsx): The main dashboard component, likely aggregating other dashboard features.
    -   [PublicBoards.tsx](mdc:src/components/features/dashboard/PublicBoards.tsx): Displays a list of public boards.
-   **`detective/`**: Core components for the main interactive detective board feature.
    -   [DetectiveBoard.tsx](mdc:src/components/features/detective/DetectiveBoard.tsx): The primary component orchestrating the entire interactive board experience.
    -   [BoardItem.tsx](mdc:src/components/features/detective/BoardItem.tsx): A wrapper or base component for individual items placed on the board.
    -   [index.tsx](mdc:src/components/features/detective/index.tsx): Barrel file exporting components from the `detective/` feature directory.
    -   **`Board/`**: Components related to the board canvas itself.
        -   [BoardCanvas.tsx](mdc:src/components/features/detective/Board/BoardCanvas.tsx): The main interactive canvas area where items are placed and manipulated.
        -   [PublicBoardCanvas.tsx](mdc:src/components/features/detective/Board/PublicBoardCanvas.tsx): A read-only version of the canvas for public viewing.
        -   [Grid.tsx](mdc:src/components/features/detective/Board/Grid.tsx): Component for rendering the background grid on the canvas.
        -   [index.ts](mdc:src/components/features/detective/Board/index.ts): Barrel file for `Board/` components.
    -   **`Connections/`**: Components for rendering and interacting with connections between items.
        -   [Connection.tsx](mdc:src/components/features/detective/Connections/Connection.tsx): Renders a single connection line between two items.
        -   [ConnectionInProgress.tsx](mdc:src/components/features/detective/Connections/ConnectionInProgress.tsx): Renders the visual feedback while a user is drawing a connection.
    -   **`Items/`**: Components representing the different types of items that can be placed on the board.
        -   [ArticleNode.tsx](mdc:src/components/features/detective/Items/ArticleNode.tsx): Component for displaying article items.
        -   [ImageNode.tsx](mdc:src/components/features/detective/Items/ImageNode.tsx): Component for displaying image items.
        -   [StickyNote.tsx](mdc:src/components/features/detective/Items/StickyNote.tsx): Component for displaying sticky note items.
        -   [TextNode.tsx](mdc:src/components/features/detective/Items/TextNode.tsx): Component for displaying text items.
    -   **`Modals/`**: Modal dialog components specific to the detective board feature.
        -   [AIChatModal.tsx](mdc:src/components/features/detective/Modals/AIChatModal.tsx): Modal for interacting with an AI assistant related to the board.
        -   [ArticleForm.tsx](mdc:src/components/features/detective/Modals/ArticleForm.tsx): Form for adding or editing article items.
        -   [ArticleImportForm.tsx](mdc:src/components/features/detective/Modals/ArticleImportForm.tsx): Form specifically for importing articles, possibly from URLs.
        -   [ExitConfirmation.tsx](mdc:src/components/features/detective/Modals/ExitConfirmation.tsx): Modal to confirm exiting the board, potentially with unsaved changes.
        -   [ImageUploadModal.tsx](mdc:src/components/features/detective/Modals/ImageUploadModal.tsx): Modal for uploading images to be added to the board.
        -   [SaveModal.tsx](mdc:src/components/features/detective/Modals/SaveModal.tsx): Modal for saving the current board state.
        -   [index.ts](mdc:src/components/features/detective/Modals/index.ts): Barrel file for `Modals/` components.
    -   **`Strokes/`**: Components related to freehand drawing/pen tool.
        -   [StrokeRenderer.tsx](mdc:src/components/features/detective/Strokes/StrokeRenderer.tsx): Component responsible for rendering the freehand drawing strokes on the canvas.
    -   **`styles/`**: CSS styles specific to the detective board components.
        -   [cursor-styles.css](mdc:src/components/features/detective/styles/cursor-styles.css): CSS file defining custom cursor styles used on the board.
    -   **`Toolbar/`**: Components for the main toolbar providing board interaction tools.
        -   [Toolbar.tsx](mdc:src/components/features/detective/Toolbar/Toolbar.tsx): The main toolbar container component.
        -   [ToolButton.tsx](mdc:src/components/features/detective/Toolbar/ToolButton.tsx): A reusable button component for individual tools in the toolbar.
        -   [ColorPicker.tsx](mdc:src/components/features/detective/Toolbar/ColorPicker.tsx): Component for selecting colors (likely for strokes or items).
        -   [index.ts](mdc:src/components/features/detective/Toolbar/index.ts): Barrel file for `Toolbar/` components.
-   **`public-board/`**: Components specifically for the read-only public view of a board.
    -   [PublicBoardView.tsx](mdc:src/components/features/public-board/PublicBoardView.tsx): The main component for displaying a public, non-interactive board.
    -   [ArticleNodeReadOnly.tsx](mdc:src/components/features/public-board/ArticleNodeReadOnly.tsx): Read-only version of the ArticleNode.
    -   [ConnectionReadOnly.tsx](mdc:src/components/features/public-board/ConnectionReadOnly.tsx): Read-only version of the Connection component.
    -   [ImageNodeReadOnly.tsx](mdc:src/components/features/public-board/ImageNodeReadOnly.tsx): Read-only version of the ImageNode.
    -   [StickyNoteReadOnly.tsx](mdc:src/components/features/public-board/StickyNoteReadOnly.tsx): Read-only version of the StickyNote.
    -   [TextNodeReadOnly.tsx](mdc:src/components/features/public-board/TextNodeReadOnly.tsx): Read-only version of the TextNode.

### `src/components/layout`

Components defining the overall page structure.

-   [MainLayout.tsx](mdc:src/components/layout/MainLayout.tsx): The primary layout structure for most pages (e.g., including header, footer, main content area).
-   [ReadableFontWrapper.tsx](mdc:src/components/layout/ReadableFontWrapper.tsx): A wrapper component to apply consistent, readable font styles.

### `src/components/ui`

Generic, reusable UI building blocks.

-   [AutoFitText.tsx](mdc:src/components/ui/AutoFitText.tsx): Component to automatically adjust text size to fit its container.
-   [button.tsx](mdc:src/components/ui/button.tsx): Standard button component.
-   [card.tsx](mdc:src/components/ui/card.tsx): Card component for displaying content in a visually distinct block.
-   [input.tsx](mdc:src/components/ui/input.tsx): Standard text input field component.
-   [label.tsx](mdc:src/components/ui/label.tsx): Label component, typically associated with form inputs.
-   [modal.tsx](mdc:src/components/ui/modal.tsx): Generic modal dialog component base.
-   [popover.tsx](mdc:src/components/ui/popover.tsx): Popover component for displaying content anchored to an element.
-   [select.tsx](mdc:src/components/ui/select.tsx): Dropdown select input component.
-   [slider.tsx](mdc:src/components/ui/slider.tsx): Slider component for selecting a value within a range.


