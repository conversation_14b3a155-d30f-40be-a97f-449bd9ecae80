---
description: overview of hooks folder
globs: 
alwaysApply: false
---
# Hooks Overview

This directory contains custom React hooks used throughout the application.

## Core Board Logic
- **[useBoardState.ts](mdc:src/hooks/useBoardState.ts)**: Manages the overall state of the detective board, likely including element positions, connections, and potentially history/undo/redo functionality.
- **[useBoardElements.ts](mdc:src/hooks/useBoardElements.ts)**: Handles the logic specifically related to managing board elements (e.g., adding, deleting, updating items like notes, images, strings).
- **[useConnections.ts](mdc:src/hooks/useConnections.ts)**: Manages the connections (e.g., strings, lines) between elements on the board.
- **[useZoomAndPan.ts](mdc:src/hooks/useZoomAndPan.ts)**: Implements zoom and panning functionality for the board canvas.
- **[usePenTool.ts](mdc:src/hooks/usePenTool.ts)**: Manages the state and logic for a pen or drawing tool on the board.

## State Management & Persistence
- **[useAutoSave.ts](mdc:src/hooks/useAutoSave.ts)**: Implements automatic saving of the board state, likely to local storage or a backend.
- **[useLocalStorage.ts](mdc:src/hooks/useLocalStorage.ts)**: A generic hook for interacting with the browser's local storage.
- **[useModalState.ts](mdc:src/hooks/useModalState.ts)**: Manages the state (open/closed, content) of modal dialogs.

## Data Fetching & API Interaction
- **[useApi.ts](mdc:src/hooks/useApi.ts)**: Provides functions for interacting with a backend API.
- **[useBoards.ts](mdc:src/hooks/useBoards.ts)**: Handles fetching, creating, or managing multiple boards.
- **[useAsync.ts](mdc:src/hooks/useAsync.ts)**: A general-purpose hook for handling asynchronous operations, managing loading, error, and data states.

## UI & Utility Hooks
- **[useOutsideClick.ts](mdc:src/hooks/useOutsideClick.ts)**: Detects clicks outside of a specified element, useful for closing dropdowns or modals.
- **[useMediaQuery.ts](mdc:src/hooks/useMediaQuery.ts)**: Tracks browser viewport size changes based on media queries.
- **[useFormValidation.ts](mdc:src/hooks/useFormValidation.ts)**: Provides logic for validating form inputs.

## Index
- **[index.ts](mdc:src/hooks/index.ts)**: Likely exports all the hooks from this directory for easier importing elsewhere.

