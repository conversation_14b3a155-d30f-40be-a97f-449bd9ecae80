'use client';

import React, { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Loader2 } from 'lucide-react';
import MainLayout from '@/components/layout/MainLayout';
import DetectiveBoardContainer from '@/components/features/detective';

export default function BoardPage() {
  const params = useParams();
  const router = useRouter();
  const id = params.id as string;
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  useEffect(() => {
    const checkBoardAccess = async () => {
      if (!id) return;
      
      setIsLoading(true);
      setError(null);
      
      try {
        // Fetch board data to check permissions
        const response = await fetch(`/api/board/${id}`);
        
        if (!response.ok) {
          if (response.status === 404) {
            setError('Board not found');
          } else {
            setError('Error loading board');
          }
          setIsLoading(false);
          return;
        }
        
        const data = await response.json();
        
        // If the board is explicitly shared with the current user, handle share permissions
        if (data.sharedWith) {
          if (data.permissionLevel === 'view') {
            console.log(`Board ${id} is shared with view permission. Redirecting to read-only view.`);
            router.replace(`/public-board/${id}`);
            return;
          }
          // If permissionLevel is 'edit', allow loading editable board
        }

        // If the board is public but the user doesn't have edit rights
        if (data.isPublic && (data.permissionLevel !== 'edit') && !data.canEdit) {
          console.log(`Board ${id} is public and user doesn't have edit rights. Redirecting to read-only view.`);
          router.replace(`/public-board/${id}`);
          return;
        }

        // User has edit access (owner or shared with edit), load the board
        setIsLoading(false);
      } catch (err) {
        console.error('Error checking board access:', err);
        setError('Error checking board access');
        setIsLoading(false);
      }
    };
    
    checkBoardAccess();
  }, [id, router]);

  // Show loading state while checking permissions
  if (isLoading) {
    return (
      <MainLayout className="p-0">
        <div className="bg-noir-900 flex flex-col items-center justify-center h-screen">
          <Loader2 className="h-16 w-16 animate-spin text-noir-accent mb-6" />
          <p className="text-white text-xl font-medium mb-2">Loading detective board...</p>
          <p className="text-gray-400 text-sm">Checking board access...</p>
          
          {/* Simple pulse animation instead of progress bar */}
          <div className="mt-8 w-64 bg-gray-700 rounded-full h-2 overflow-hidden">
            <div className="bg-noir-accent h-2 rounded-full animate-pulse w-full"></div>
          </div>
        </div>
      </MainLayout>
    );
  }

  // Show error state if there was a problem
  if (error) {
    return (
      <MainLayout className="p-0">
        <div className="bg-noir-900 flex flex-col items-center justify-center h-screen">
          <div className="text-red-500 text-xl font-medium mb-2">Error</div>
          <p className="text-white text-lg">{error}</p>
          <button 
            onClick={() => router.push('/recent')}
            className="mt-8 px-4 py-2 bg-noir-accent hover:bg-noir-accent/80 rounded text-white transition-colors"
          >
            Return to Recent Boards
          </button>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout className="p-0">
      <DetectiveBoardContainer />
    </MainLayout>
  );
} 