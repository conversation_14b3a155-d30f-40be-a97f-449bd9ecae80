import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
// import type { Database } from '@/lib/database.types'; // Assuming you have this types file

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url);
  const code = requestUrl.searchParams.get('code');

  if (code) {
    const cookieStore = cookies();
    // If you have a Database type, you can use createRouteHandlerClient<Database>
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore }); 
    
    try {
      await supabase.auth.exchangeCodeForSession(code);
      // Successfully exchanged code for session.
      // The onAuthStateChange listener in AuthContext should now pick up the session.
      console.log('[API Callback] Successfully exchanged code for session.');
    } catch (error) {
      console.error('[API Callback] Error exchanging code for session:', error);
      // Redirect to an error page or home page with an error message
      return NextResponse.redirect(`${requestUrl.origin}/auth-error?message=Could not exchange code for session`);
    }
  } else {
    console.warn('[API Callback] No code found in callback URL.');
    // Redirect to an error page or home page if no code is present
    return NextResponse.redirect(`${requestUrl.origin}/auth-error?message=No code provided in callback`);
  }

  // Redirect user to home page after successful authentication or if already handled
  // The client-side AuthContext will handle the user state.
  console.log('[API Callback] Redirecting to home page.');
  return NextResponse.redirect(requestUrl.origin);
} 