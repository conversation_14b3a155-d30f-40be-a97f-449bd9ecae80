import React, { useState, useRef, useCallback } from 'react';
import { X, Upload, Image, Loader2 } from 'lucide-react';
import { ImageUploadModalProps } from '../../../../types';
import { toast } from 'sonner';

/**
 * Modal for uploading images
 */
const ImageUploadModal: React.FC<ImageUploadModalProps> = ({
  isOpen,
  onClose,
  onSubmit
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [file, setFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const file = e.dataTransfer.files[0];
      processFile(file);
    }
  }, []);

  const handleFileChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      processFile(file);
    }
  }, []);

  const processFile = (file: File) => {
    // Check if the file is an image
    if (!file.type.match('image.*')) {
      toast.error('Only image files are allowed!');
      return;
    }

    // Check file size
    const MAX_FILE_SIZE_MB = 10;
    const MAX_FILE_SIZE_BYTES = MAX_FILE_SIZE_MB * 1024 * 1024;
    if (file.size > MAX_FILE_SIZE_BYTES) {
        toast.error(`File is too large. Max size is ${MAX_FILE_SIZE_MB}MB.`);
        return;
    }

    // Store the file for upload
    setFile(file);

    // Create a temporary URL for the image preview
    const reader = new FileReader();
    reader.onload = (e) => {
      if (e.target?.result) {
        setPreviewUrl(e.target.result as string);
      }
    };
    reader.readAsDataURL(file);
  };

  const uploadToSupabase = async (file: File) => {
    if (!file) return null;
    
    setIsUploading(true);
    
    try {
      // 1. Get the signed URL from our new API route
      const signedUrlResponse = await fetch('/api/storage/signed-upload-url', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          fileName: file.name,
          fileType: file.type,
          fileSize: file.size,
        }),
      });

      const signedUrlData = await signedUrlResponse.json();

      if (!signedUrlResponse.ok) {
        throw new Error(signedUrlData.message || 'Failed to get signed URL.');
      }
      
      const { signedUrl, path } = signedUrlData;

      // 2. Upload the file directly to Supabase Storage using the signed URL
      const uploadResponse = await fetch(signedUrl, {
        method: 'PUT',
        body: file,
        headers: {
            'Content-Type': file.type,
        },
      });

      if (!uploadResponse.ok) {
        const errorBody = await uploadResponse.text();
        console.error("Direct upload failed:", errorBody);
        throw new Error('Failed to upload image to storage.');
      }

      return path; // Return the path of the uploaded file

    } catch (error) {
      console.error('Upload error:', error);
      if (error instanceof Error) {
        toast.error(error.message);
      } else {
        toast.error('Failed to upload image. Please try again.');
      }
      return null;
    } finally {
      setIsUploading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!file || !previewUrl) return;
    
    try {
      // Upload the image to Supabase
      const imagePath = await uploadToSupabase(file);
      
      if (imagePath) {
        // Submit the image path to the board logic along with the local preview
        onSubmit({ 
          file_url: imagePath, 
          alt: '',
          localImageUrl: previewUrl
        });
        onClose();
        resetForm();
      }
    } catch (error) {
      console.error('Error adding image to board:', error);
      toast.error('Failed to add image to board');
    }
  };

  const resetForm = () => {
    setPreviewUrl(null);
    setFile(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleButtonClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50">
      <div className="bg-noir-100 rounded-lg w-full max-w-md p-4 shadow-xl">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-white text-lg font-medium">Add Image</h2>
          <button 
            onClick={onClose}
            className="text-white/70 hover:text-white"
            aria-label="Close"
          >
            <X size={20} />
          </button>
        </div>

        <form onSubmit={handleSubmit}>
          <div 
            className={`border-2 border-dashed rounded-lg p-8 mb-4 flex flex-col items-center justify-center cursor-pointer ${
              isDragging ? 'border-noir-accent bg-noir-50/20' : 'border-white/20'
            }`}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
            onClick={handleButtonClick}
          >
            {previewUrl ? (
              <div className="w-full max-h-64 overflow-hidden">
                <img 
                  src={previewUrl} 
                  alt="Preview" 
                  className="w-full h-auto object-contain"
                />
              </div>
            ) : (
              <>
                <Image size={48} className="text-white/50 mb-2" />
                <p className="text-white/80 text-center">
                  Drag and drop an image here, or click to select
                </p>
              </>
            )}
            
            <input
              type="file"
              accept="image/*"
              onChange={handleFileChange}
              ref={fileInputRef}
              className="hidden"
            />
          </div>

          <div className="flex justify-end space-x-2">
            <button
              type="button"
              onClick={() => {
                onClose();
                resetForm();
              }}
              className="px-4 py-2 text-white/70 hover:text-white"
              disabled={isUploading}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={!previewUrl || isUploading}
              className={`px-4 py-2 rounded flex items-center justify-center ${
                previewUrl && !isUploading
                  ? 'bg-noir-accent text-white hover:bg-opacity-90' 
                  : 'bg-noir-50/50 text-white/50 cursor-not-allowed'
              }`}
            >
              {isUploading ? (
                <>
                  <Loader2 size={16} className="animate-spin mr-2" />
                  Uploading...
                </>
              ) : (
                'Add to Board'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ImageUploadModal; 