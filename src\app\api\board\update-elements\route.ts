import { NextRequest, NextResponse } from 'next/server';
import type { Database } from '@/lib/database.types';
// Removed TablesInsert, TablesUpdate
// import type { Tables, TablesInsert, TablesUpdate } from '@/lib/database.types';
import { getAuthenticatedUser } from '@/utils/authUtils';

// --- Type for incoming element data from frontend ---
// Adjust this based on the actual structure sent by your frontend
interface ElementPayload {
    id?: string; // Optional for 'add', required for 'update'/'delete'
    type: string;
    title?: string;
    content?: string;
    url?: string;
    website_url?: string; // Added for storing article website URLs
    file_url?: string; // Added for storing Supabase Storage image URLs
    position?: { x: number; y: number };
    isAiGenerated?: boolean;
}

// --- Type for the specific board sharing data we query ---
interface BoardSharingPermission {
    user_id: string; // Note: This corresponds to shared_with_user_id due to the select alias
    permission_level: string;
}

// --- Type for the expected RPC response on delete ---
interface DeleteRpcResponse {
    message: string;
    deleted_file_path?: string; // Optional path returned by the modified RPC
}

export async function POST(request: NextRequest) {
    try {
        // 1. Get User Session
        const { user, error: authError, supabase: supabaseUserClient } = await getAuthenticatedUser();
        if (authError || !user) {
            console.error("Update Element - User Session Error:", authError);
            return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
        }
        const userId = user.id;

        // 2. Parse Request Body - check for both single and batch operations
        const requestBody = await request.json();
        const { boardId, action, isBatch } = requestBody;
        
        // 3. Basic validation
        if (!boardId || !action) {
            return NextResponse.json({ message: 'Board ID and action are required' }, { status: 400 });
        }
        
        // Handle batch operation
        if (isBatch && requestBody.elements && Array.isArray(requestBody.elements)) {
            // Batch operation with elements array
            const elements = requestBody.elements;
            if (elements.length === 0) {
                return NextResponse.json({ message: 'Elements array is empty' }, { status: 400 });
            }
            
            // Process elements in parallel - only 'add' action supported for batch operations
            if (action !== 'add') {
                return NextResponse.json({ message: 'Batch operations only support "add" action currently' }, { status: 400 });
            }
            
            // Process all elements
            const processPromises = elements.map(async (element: ElementPayload) => {
                // Clean up element as needed
                if (element.type === 'image' && element.file_url) {
                    // Clean up file_url if it's a URL instead of just a path
                    if (element.file_url.includes('/images/')) {
                        // Extract the part after /images/
                        const cleanPath = element.file_url.split('/images/').pop();
                        if (cleanPath) {
                            element.file_url = cleanPath;
                        }
                    }
                }
                
                // For article elements, ensure website_url is set properly
                if (element.type === 'article') {
                    // If website_url isn't set but url is, use url as the website_url
                    if (!element.website_url && element.url) {
                        element.website_url = element.url;
                    }
                }
                
                return supabaseUserClient.rpc(
                    'manage_element',
                    {
                        p_user_id: userId,
                        p_board_id: boardId,
                        p_action: action,
                        p_element: element as any
                    }
                );
            });
            
            const results = await Promise.all(processPromises);
            
            // Check for errors
            const errors = results
                .filter(result => result.error)
                .map(result => result.error);
                
            if (errors.length > 0) {
                console.error(`Batch operation errors (${errors.length} of ${elements.length} failed):`, errors);
                // Continue with successful results
            }
            
            // Extract successful results
            const successfulElements = results
                .filter(result => !result.error && result.data)
                .map(result => result.data);
                
            return NextResponse.json({ 
                message: `Successfully processed ${successfulElements.length} of ${elements.length} elements`,
                elements: successfulElements
            }, { status: 200 });
        }
        
        // Regular single element operation
        const element = requestBody.element;
        if (!element) {
            return NextResponse.json({ message: 'Element data is required' }, { status: 400 });
        }

        // For image elements, make extra checks
        if (element.type === 'image') {
            
            // Clean up file_url if it's a URL instead of just a path
            if (element.file_url) {
                // If it contains a full Supabase URL, extract just the filename
                if (element.file_url.includes('/images/')) {
                    // Extract the part after /images/
                    const cleanPath = element.file_url.split('/images/').pop();
                    if (cleanPath) {
                        element.file_url = cleanPath;
                    }
                }
            }
            
        }
        
        // For article elements, ensure website_url is set properly
        if (element.type === 'article') {
            // If website_url isn't set but url is, use url as the website_url
            if (!element.website_url && element.url) {
                element.website_url = element.url;
            }
        }

        // Call RPC Function for single element
        const { data: resultData, error: rpcError } = await supabaseUserClient.rpc(
            'manage_element',
            {
                p_user_id: userId,
                p_board_id: boardId,
                p_action: action,
                p_element: element as any // Pass the whole element payload
            }
        );

        if (rpcError) {
             console.error(`RPC Error (manage_element - ${action}):`, rpcError);
             // Check for specific permission error from the function
             if (rpcError.message.includes('does not have permission')) {
                 return NextResponse.json({ message: 'Permission denied to edit this board', error: rpcError.message }, { status: 403 });
             }
              // Check for element not found errors
             if (rpcError.message.includes('not found')) {
                 return NextResponse.json({ message: 'Element not found on this board', error: rpcError.message }, { status: 404 });
             }
             // Check for validation errors from the function
             if (rpcError.message.includes('required for') || rpcError.message.includes('Invalid action')) {
                  return NextResponse.json({ message: 'Invalid input', error: rpcError.message }, { status: 400 });
             }
             // Generic server error
             return NextResponse.json({ message: `Failed to ${action} element`, error: rpcError.message }, { status: 500 });
        }

        // 5. Return Response from RPC
        let status = 200;
        let responseData: any = resultData; // Default to RPC result

        if (action === 'add') {
            status = 201;
            responseData = { message: `Element ${action} successful`, element: resultData };
        } else if (action === 'update') {
            status = 200;
             responseData = { message: `Element ${action} successful`, element: resultData };
        } else if (action === 'delete') {
            status = 200;
            // Use double assertion: unknown tells TS to trust us, then assert the final type
            const deleteResult = resultData as unknown as DeleteRpcResponse;
            responseData = { message: deleteResult.message }; // Base response

            // --- Trigger Storage Deletion ---
            if (deleteResult.deleted_file_path) {
                try {
                    // Construct the full URL for the internal API call
                    const deleteUrl = new URL('/api/storage/delete-image', request.nextUrl.origin);

                    // Prepare headers, forwarding cookies
                    const headers = new Headers();
                    headers.append('Content-Type', 'application/json');
                    // Forward necessary cookies for authentication
                    const cookiesHeader = request.headers.get('cookie');
                    if (cookiesHeader) {
                        headers.append('Cookie', cookiesHeader);
                    }


                    const deleteResponse = await fetch(deleteUrl.toString(), {
                        method: 'DELETE',
                        headers: headers,
                        body: JSON.stringify({ filePath: deleteResult.deleted_file_path }),
                    });

                    if (!deleteResponse.ok) {
                        const errorBody = await deleteResponse.text(); // Read error body as text
                        console.error(`Failed to delete image from storage via API call. Status: ${deleteResponse.status}. Response: ${errorBody}`);
                        // Decide if this failure should alter the main response?
                        // For now, we just log it. The element is deleted from DB.
                        // You could potentially add info to the responseData here.
                         // responseData.storage_deletion_status = 'failed';
                    } else {
                         // responseData.storage_deletion_status = 'success';
                    }
                } catch (fetchError: any) {
                     console.error(`Error making fetch call to delete image API: ${fetchError.message}`, fetchError);
                     // responseData.storage_deletion_status = 'error_calling_api';
                }
            }
             // Return the original success message from the RPC
             return NextResponse.json(responseData, { status: status });
        }

        // For add/update, RPC returns the element data (including the file_url which is the object path)
        // Signed URLs for rendering will be generated by the GET /api/board/[id] endpoint.
         return NextResponse.json({ message: `Element ${action} successful`, element: resultData }, { status });


    } catch (error: any) {
        console.error('Error processing element update:', error);
        // Handle potential JSON parsing errors from the initial request read
        if (error instanceof SyntaxError) {
             return NextResponse.json({ message: 'Invalid request body format', error: error.message }, { status: 400 });
         }
        return NextResponse.json({ message: 'Server error processing element update', error: error.message }, { status: 500 });
    }
} 