'use client';

import React, { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';

// Dynamically import PasswordProtection component with no SSR
const PasswordProtection = dynamic(
  () => import('./PasswordProtection'),
  { ssr: false }
);

export default function PasswordProtectionWrapper({
  children,
}: {
  children: React.ReactNode;
}) {
  // const [isAuthenticated, setIsAuthenticated] = useState(false);
  // const [isLoading, setIsLoading] = useState(true);

  // useEffect(() => {
  //   // Check if the user has already entered the correct password
  //   try {
  //     const hasPassword = localStorage.getItem('dev_password_auth') === 'true';
  //     setIsAuthenticated(hasPassword);
  //   } catch (error) {
  //     console.error('Error checking authentication:', error);
  //   } finally {
  //     setIsLoading(false);
  //   }
  // }, []);

  // // Show loading state
  // if (isLoading) {
  //   return <div className="flex items-center justify-center min-h-screen bg-gray-100">Loading...</div>;
  // }

  // if (!isAuthenticated) {
  //   return <PasswordProtection onAuthenticated={() => setIsAuthenticated(true)} />;
  // }

  return <>{children}</>;
} 