import React, { useState, useRef, useCallback, useEffect } from 'react';
import { X, Upload, Image, Loader2 } from 'lucide-react';
import { toast } from 'sonner';

interface ArticleImageUploadModalProps {
  isOpen: boolean;
  onClose: () => void;
  articleId: string | null; // ID of the article being updated
  onUploadComplete: (articleId: string, imagePath: string, localImageUrl: string) => void; // Callback with article ID and new path
}

const ArticleImageUploadModal: React.FC<ArticleImageUploadModalProps> = ({
  isOpen,
  onClose,
  articleId,
  onUploadComplete
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [file, setFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const file = e.dataTransfer.files[0];
      processFile(file);
    }
  }, []);

  const handleFileChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      processFile(file);
    }
  }, []);

  const processFile = (file: File) => {
    if (!file.type.match('image.*')) {
      toast.error('Only image files are allowed!');
      return;
    }
    // Check file size
    const MAX_FILE_SIZE_MB = 10;
    const MAX_FILE_SIZE_BYTES = MAX_FILE_SIZE_MB * 1024 * 1024;
    if (file.size > MAX_FILE_SIZE_BYTES) {
        toast.error(`File is too large. Max size is ${MAX_FILE_SIZE_MB}MB.`);
        return;
    }
    setFile(file);
    const reader = new FileReader();
    reader.onload = (e) => {
      if (e.target?.result) {
        setPreviewUrl(e.target.result as string);
      }
    };
    reader.readAsDataURL(file);
  };

  const uploadToSupabase = async (file: File) => {
    if (!file) return null;
    
    setIsUploading(true);
    
    try {
      // 1. Get the signed URL from our new API route
      const signedUrlResponse = await fetch('/api/storage/signed-upload-url', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          fileName: file.name,
          fileType: file.type,
          fileSize: file.size,
        }),
      });

      const signedUrlData = await signedUrlResponse.json();

      if (!signedUrlResponse.ok) {
        throw new Error(signedUrlData.message || 'Failed to get signed URL.');
      }
      
      const { signedUrl, path } = signedUrlData;

      // 2. Upload the file directly to Supabase Storage using the signed URL
      const uploadResponse = await fetch(signedUrl, {
        method: 'PUT',
        body: file,
        headers: {
            'Content-Type': file.type,
        },
      });

      if (!uploadResponse.ok) {
        const errorBody = await uploadResponse.text();
        console.error("Direct upload failed:", errorBody);
        throw new Error('Failed to upload image to storage.');
      }

      return path; // Return the path of the uploaded file
    } catch (error) {
      console.error('Upload error:', error);
      if (error instanceof Error) {
        toast.error(error.message);
      } else {
        toast.error('Failed to upload image. Please try again.');
      }
      return null;
    } finally {
      setIsUploading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!previewUrl || !file || !articleId) return;
    
    try {
      const imagePath = await uploadToSupabase(file);
      
      if (imagePath) {
        // Use the onUploadComplete callback with the article ID
        onUploadComplete(articleId, imagePath, previewUrl);
        handleCloseModal();
      }
    } catch (error) {
      console.error('Error updating article image:', error);
      toast.error('Failed to update article image');
    }
  };

  const resetForm = () => {
    setPreviewUrl(null);
    setFile(null);
    setIsDragging(false);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };
  
  const handleCloseModal = () => {
      resetForm();
      onClose();
  };

  const handleButtonClick = () => {
    fileInputRef.current?.click();
  };

  // Reset form state when the modal is opened with a new articleId or closed
  useEffect(() => {
    if (isOpen) {
      resetForm();
    }
  }, [isOpen]);

  if (!isOpen || !articleId) return null;

  return (
    // Using similar styling as ImageUploadModal
    <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50">
      <div className="bg-noir-100 rounded-lg w-full max-w-md p-4 shadow-xl">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-white text-lg font-medium">Change Article Image</h2>
          <button 
            onClick={handleCloseModal}
            className="text-white/70 hover:text-white"
            aria-label="Close"
          >
            <X size={20} />
          </button>
        </div>

        <form onSubmit={handleSubmit}>
          <div 
            className={`border-2 border-dashed rounded-lg p-8 mb-4 flex flex-col items-center justify-center cursor-pointer ${
              isDragging ? 'border-noir-accent bg-noir-50/20' : 'border-white/20'
            }`}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
            onClick={handleButtonClick} // Trigger file input on click
          >
            {previewUrl ? (
              <div className="w-full max-h-64 overflow-hidden flex items-center justify-center">
                <img 
                  src={previewUrl} 
                  alt="Preview" 
                  className="max-w-full max-h-full object-contain"
                />
              </div>
            ) : (
              <>
                <Upload size={48} className="text-white/50 mb-2" />
                <p className="text-white/80 text-center">
                  Drag & drop new image or click to select
                </p>
              </>
            )}
            
            <input
              type="file"
              accept="image/*"
              onChange={handleFileChange}
              ref={fileInputRef}
              className="hidden"
              id={`article-image-upload-${articleId}`} // Unique ID might be useful
            />
          </div>

          <div className="flex justify-end space-x-2">
            <button
              type="button"
              onClick={handleCloseModal}
              className="px-4 py-2 text-white/70 hover:text-white"
              disabled={isUploading}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={!previewUrl || isUploading}
              className={`px-4 py-2 rounded flex items-center justify-center ${
                previewUrl && !isUploading
                  ? 'bg-noir-accent text-white hover:bg-opacity-90' 
                  : 'bg-noir-50/50 text-white/50 cursor-not-allowed'
              }`}
            >
              {isUploading ? (
                <>
                  <Loader2 size={16} className="animate-spin mr-2" />
                  Uploading...
                </>
              ) : (
                'Confirm Change'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ArticleImageUploadModal; 