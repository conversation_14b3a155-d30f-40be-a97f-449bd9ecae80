import React from 'react';
import { BoardProvider } from '../../../context/BoardContext';
import { DragProvider } from '../../../context/DragContext';
import DetectiveBoard from './DetectiveBoard';

/**
 * Main detective board component that wraps the board with the provider
 */
interface DetectiveBoardContainerProps {
  initialBoardName?: string;
}

/**
 * Container component that provides the board context to the detective board
 */
const DetectiveBoardContainer: React.FC<DetectiveBoardContainerProps> = ({
  initialBoardName = 'Untitled board'
}) => {
  return (
    <DragProvider>
      <BoardProvider initialBoardName={initialBoardName}>
        <DetectiveBoard />
      </BoardProvider>
    </DragProvider>
  );
};

export default DetectiveBoardContainer; 