'use server';

import { cookies } from 'next/headers';
import { createServerActionClient } from '@supabase/auth-helpers-nextjs';
import type { Database } from '@/lib/database.types';

/**
 * Server action to prefetch public boards data
 */
export async function prefetchPublicBoards({
  limit = 12,
  searchQuery = '',
  sortBy = 'likes',
  sortOrder = 'desc',
  timePeriod = 'allTime'
}: {
  limit?: number;
  searchQuery?: string;
  sortBy?: string;
  sortOrder?: string;
  timePeriod?: string;
} = {}) {
  try {
    const supabase = createServerActionClient<Database>({ cookies });
    
    // Fetch public boards data
    let query = supabase
      .from('board_sharing')
      .select(`
        id, likes, created_at, updated_at, public_board, board_id, total_views,
        boards!inner (
          id, board_name, preview_image_url, created_at, updated_at, user_id,
          popularity_score,
          comments(count)
        )
      `)
      .eq('public_board', true);
    
    // Apply time period filter
    if (timePeriod !== 'allTime') {
      const now = new Date();
      let startDate: Date;
      
      if (timePeriod === 'last7days') {
        startDate = new Date();
        startDate.setDate(startDate.getDate() - 7);
      } else if (timePeriod === 'last30days') {
        startDate = new Date();
        startDate.setDate(startDate.getDate() - 30);
      } else {
        startDate = new Date(0);
      }
      
      if (timePeriod !== 'allTime' && timePeriod !== 'all') {
        query = query.gte('created_at', startDate.toISOString());
      }
    }
    
    // Apply sorting
    if (sortBy === 'popularity_score') {
      query = query.order('popularity_score', { foreignTable: 'boards', ascending: sortOrder === 'asc' });
    } else if (sortBy === 'likes') {
      query = query.order('likes', { ascending: sortOrder === 'asc' });
    } else if (sortBy === 'recent') {
      query = query.order('created_at', { ascending: sortOrder === 'asc' });
    }
    
    // Apply pagination
    query = query.limit(limit);
    
    const { data: boardSharingItems, error } = await query;
    
    if (error) {
      console.error('Error fetching public boards:', error);
      return {
        boards: [],
        pagination: {
          total: 0,
          offset: 0,
          limit,
          hasMore: false
        }
      };
    }
    
    // Count total boards for pagination
    const { count, error: countError } = await supabase
      .from('board_sharing')
      .select('id', { count: 'exact', head: true })
      .eq('public_board', true);
    
    if (countError) {
      console.error('Error counting public boards:', countError);
    }
    
    // Process board data to match client format WITHOUT including signed URLs
    // Let the client fetch these later with React Query
    const processedBoards = boardSharingItems.map(item => {
      const board = item.boards;
      const commentCountResult = board.comments as unknown as ({ count: number }[] | null);
      const commentCount = commentCountResult && commentCountResult.length > 0 ? commentCountResult[0].count : 0;
      
      return {
        id: board.id,
        name: board.board_name,
        // Just store the original URL - client will handle signed URL generation
        previewImageUrl: board.preview_image_url,
        likes: item.likes || 0,
        publicSince: item.created_at,
        commentCount,
        totalViews: item.total_views || 0,
        popularityScore: board.popularity_score || 0
      };
    });
    
    return {
      boards: processedBoards,
      pagination: {
        total: count || 0,
        offset: 0,
        limit,
        hasMore: (count || 0) > limit
      }
    };
  } catch (error) {
    console.error('Error in prefetchPublicBoards server action:', error);
    return {
      boards: [],
      pagination: {
        total: 0,
        offset: 0,
        limit,
        hasMore: false
      }
    };
  }
} 