'use client';

import { useState, useEffect, useRef, RefObject } from 'react';

interface UseIntersectionObserverProps {
  rootMargin?: string;
  threshold?: number;
  triggerOnce?: boolean;
}

/**
 * A hook that uses the Intersection Observer API to detect when an element is visible in the viewport
 * @param options Configuration options for the intersection observer
 * @returns [ref, isIntersecting] where ref is the reference to attach to the element, and isIntersecting is true when the element is visible
 */
export function useIntersectionObserver({
  rootMargin = '0px',
  threshold = 0,
  triggerOnce = false,
}: UseIntersectionObserverProps = {}): [RefObject<HTMLElement>, boolean] {
  const [isIntersecting, setIsIntersecting] = useState(false);
  const observedRef = useRef<HTMLElement>(null);
  const hasTriggered = useRef(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        // If we only want to trigger once and we've already triggered, don't update the state
        if (triggerOnce && hasTriggered.current) {
          return;
        }

        setIsIntersecting(entry.isIntersecting);
        
        // If this is the first time the element is intersecting and we only want to trigger once, mark it as triggered
        if (triggerOnce && entry.isIntersecting) {
          hasTriggered.current = true;
        }
      },
      { rootMargin, threshold }
    );

    const currentElement = observedRef.current;
    if (currentElement) {
      observer.observe(currentElement);
    }

    return () => {
      if (currentElement) {
        observer.unobserve(currentElement);
      }
    };
  }, [rootMargin, threshold, triggerOnce]);

  return [observedRef, isIntersecting];
} 