import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Heart, Loader2 } from 'lucide-react';
import BoardPreview from '../board/BoardPreview';

interface PublicBoard {
  id: string;
  name: string;
  previewImageUrl: string | null;
  likes: number;
  publicSince: string;
}

interface PublicBoardsProps {
  limit?: number;
}

const PublicBoards: React.FC<PublicBoardsProps> = ({ limit = 4 }) => {
  const router = useRouter();
  const [publicBoards, setPublicBoards] = useState<PublicBoard[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPublicBoards = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const response = await fetch(`/api/board/public?limit=${limit}`);
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to fetch public boards');
        }
        
        const data = await response.json();
        setPublicBoards(data.boards || []);
      } catch (error) {
        console.error('Error fetching public boards:', error);
        setError(error instanceof Error ? error.message : 'Failed to load public boards');
      } finally {
        setIsLoading(false);
      }
    };

    fetchPublicBoards();
  }, [limit]);

  // Format date to a more readable format
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));
    
    if (diffInDays === 0) {
      return 'Today';
    } else if (diffInDays === 1) {
      return 'Yesterday';
    } else if (diffInDays < 7) {
      return `${diffInDays} days ago`;
    } else if (diffInDays < 30) {
      return `${Math.floor(diffInDays / 7)} weeks ago`;
    } else {
      return date.toLocaleDateString('en-US', { 
        year: 'numeric', 
        month: 'short', 
        day: 'numeric' 
      });
    }
  };

  const handleBoardClick = (boardId: string) => {
    router.push(`/board/${boardId}`);
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-8">
        <Loader2 className="h-8 w-8 animate-spin text-noir-accent" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-400">{error}</p>
      </div>
    );
  }

  if (publicBoards.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-400">No public boards available at the moment.</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
      {publicBoards.map(board => (
        <div 
          key={board.id}
          className="bg-noir-800 rounded-lg overflow-hidden shadow-lg border border-noir-700 hover:border-noir-accent transition-all duration-300 cursor-pointer hover:transform hover:scale-105"
          onClick={() => handleBoardClick(board.id)}
        >
          <div className="aspect-video">
            {board.previewImageUrl ? (
              <img 
                src={board.previewImageUrl} 
                alt={board.name}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center bg-noir-900">
                <span className="text-gray-500 text-sm">No preview</span>
              </div>
            )}
          </div>
          
          <div className="p-4">
            <h3 className="font-semibold text-white text-lg mb-2 truncate">{board.name}</h3>
            <div className="flex justify-between items-center">
              <div className="flex items-center text-red-500">
                <Heart size={16} className="mr-1 fill-current" />
                <span>{board.likes}</span>
              </div>
              <div className="text-xs text-gray-400">
                {formatDate(board.publicSince)}
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default PublicBoards; 