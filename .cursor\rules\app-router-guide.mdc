---
description: overview of app folder 
globs: 
alwaysApply: false
---
# App Router Guide (src/app)

This directory contains the core routing structure for the Next.js application, following the App Router convention.

## High-Level Overview

The `src/app` directory is the root of the application's routes. Each folder within it typically represents a URL path segment. Special files like `layout.tsx` and `page.tsx` define the UI for these routes.

-   **`api/`**: Contains route handlers for backend API endpoints.
-   **`auth/`**: Likely contains routes and UI related to user authentication (e.g., sign-in, sign-up pages).
-   **`board/`**: Likely contains routes for displaying and interacting with individual detective boards (e.g., `board/[id]`/).
-   **`public-board/`**: Likely contains routes for the read-only view of shared or public boards.
-   **`public-boards/`**: Likely contains a route to display a gallery or list of all public boards.
-   **`recent/`**: Likely contains a route to display the user's recently viewed or edited boards.

## Core Files

-   **[layout.tsx](mdc:src/app/layout.tsx)**: This is the root layout for the entire application. It defines the base HTML structure (`<html>`, `<body>`) and wraps all page content. In this specific implementation:
    -   It sets up global styles (`globals.css`).
    -   It configures metadata (title, description).
    -   It uses `createServerComponentClient` from Supabase to handle authentication server-side, fetching the user session and profile based on cookies.
    -   It dynamically imports and wraps the application in the `AuthProvider` context, passing the server-fetched session and profile data to initialize the client-side auth state.
    -   It includes a `PasswordProtectionWrapper`, suggesting potential site-wide password protection logic.
    -   It includes the `Toaster` component for displaying notifications.
-   **[page.tsx](mdc:src/app/page.tsx)**: This file defines the UI for the root URL path (`/`). It serves as the main landing page or dashboard of the application.
    -   It's marked with `'use client'`, indicating it's a Client Component.
    -   It renders the `MainLayout` component to provide the overall page structure.
    -   It primarily displays the `Dashboard` component.
    -   It includes logic to handle and display a `VerificationModal` if the authentication flow requires the user to verify their email after login or sign-up.

