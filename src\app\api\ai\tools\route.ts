import { NextRequest, NextResponse } from 'next/server';
import { getAuthenticatedUser } from '@/utils/authUtils';
import { boardService } from '@/services/boardService';
import { v4 as uuidv4 } from 'uuid';
import type { BoardItem, Position, Connection } from '@/types';
import { ToolResult } from '@/types/ai-messages';

// Tool definitions that match the Cursor tools format
export const AI_TOOLS = [
  {
    "description": "Read the current state of the detective board including all elements, connections, and metadata. Use this to understand what's currently on the board before making changes.",
    "name": "read_board",
    "parameters": {
      "properties": {},
      "required": [],
      "type": "object"
    }
  },
  {
    "description": "Create a new element on the detective board. Use this to add sticky notes, text, articles, or images to the board. For image elements, provide the image URL in the 'url' parameter and a caption in the 'content' parameter - the system will automatically download and store the image.",
    "name": "create_element",
    "parameters": {
      "properties": {
        "type": {
          "description": "The type of element to create",
          "enum": ["sticky-yellow", "sticky-red", "sticky-blue", "text", "article", "image"],
          "type": "string"
        },
        "position": {
          "description": "Position on the board where to place the element",
          "properties": {
            "x": {"type": "number"},
            "y": {"type": "number"}
          },
          "required": ["x", "y"],
          "type": "object"
        },
        "content": {
          "description": "The text content of the element. For image elements, this should be the caption/description of the image.",
          "type": "string"
        },
        "title": {
          "description": "Title for article elements",
          "type": "string"
        },
        "url": {
          "description": "URL for article elements or the image URL for image elements. For image elements, this should be the direct link to the image file.",
          "type": "string"
        }
      },
      "required": ["type", "position", "content"],
      "type": "object"
    }
  },
  {
    "description": "Create a connection between two elements on the board to show relationships.",
    "name": "create_connection", 
    "parameters": {
      "properties": {
        "from_element_id": {
          "description": "ID of the source element",
          "type": "string"
        },
        "to_element_id": {
          "description": "ID of the target element", 
          "type": "string"
        },
        "label": {
          "description": "Optional label for the connection",
          "type": "string"
        }
      },
      "required": ["from_element_id", "to_element_id"],
      "type": "object"
    }
  },
  {
    "description": "Move the user's viewport to focus on a specific area of the board or specific elements.",
    "name": "move_viewport",
    "parameters": {
      "properties": {
        "target_position": {
          "description": "Position to center the viewport on",
          "properties": {
            "x": {"type": "number"},
            "y": {"type": "number"}
          },
          "required": ["x", "y"],
          "type": "object"
        },
        "zoom_level": {
          "description": "Zoom level (0.1 to 3.0, where 1.0 is default)",
          "type": "number",
          "minimum": 0.1,
          "maximum": 3.0
        },
        "element_id": {
          "description": "ID of specific element to focus on (alternative to target_position)",
          "type": "string"
        }
      },
      "required": [],
      "type": "object"
    }
  },
  {
    "description": "Update an existing element on the board by changing its content, position, or other properties.",
    "name": "update_element",
    "parameters": {
      "properties": {
        "element_id": {
          "description": "ID of the element to update",
          "type": "string"
        },
        "updates": {
          "description": "Properties to update on the element",
          "properties": {
            "content": {"type": "string"},
            "title": {"type": "string"},
            "url": {"type": "string"},
            "position": {
              "properties": {
                "x": {"type": "number"},
                "y": {"type": "number"}
              },
              "type": "object"
            }
          },
          "type": "object"
        }
      },
      "required": ["element_id", "updates"],
      "type": "object"
    }
  },
  {
    "description": "Delete an element from the board.",
    "name": "delete_element",
    "parameters": {
      "properties": {
        "element_id": {
          "description": "ID of the element to delete",
          "type": "string"
        }
      },
      "required": ["element_id"],
      "type": "object"
    }
  },
  {
    "description": "Delete a connection between two elements on the detective board. You can specify either the connection ID directly, or the source and target element IDs to find and delete the connection.",
    "name": "delete_connection",
    "parameters": {
      "properties": {
        "connection_id": {
          "description": "The ID of the connection to delete (if known)",
          "type": "string"
        },
        "from_element_id": {
          "description": "ID of the source element (alternative to connection_id)",
          "type": "string"
        },
        "to_element_id": {
          "description": "ID of the target element (alternative to connection_id)",
          "type": "string"
        }
      },
      "type": "object"
    }
  },
  {
    "description": "Search the web for real-time information using Exa's search endpoint. Best for finding multiple relevant sources and recent content on a topic. do not include specific years in searches unless explicitly requested by the user. If a user says find recent information do not assume a date and just use the word recent",
    "name": "exa_search",
    "parameters": {
      "properties": {
        "search_term": {
          "description": "The search term to look up on the web. Be specific and include relevant keywords for better results. Do not include specific years unless explicitly requested by the user.",
          "type": "string"
        },
        "num_results": {
          "description": "Number of search results to return (default: 5, max: 10)",
          "type": "integer",
          "minimum": 1,
          "maximum": 10,
          "default": 5
        },
        "search_options": {
          "description": "Advanced Exa search options (e.g. type, category, date filters, domain filters). Omit to let the AI choose sensible defaults.",
          "type": "object",
          "properties": {
            "type": { "type": "string", "enum": ["auto", "neural", "keyword"], "description": "Search type selection" },
            "category": { "type": "string", "description": "Result category, e.g. company, research paper" },
            "include_domains": { "type": "array", "items": { "type": "string" } },
            "exclude_domains": { "type": "array", "items": { "type": "string" } },
            "start_published_date": { "type": "string", "description": "ISO 8601 start published date" },
            "end_published_date": { "type": "string", "description": "ISO 8601 end published date" }
          }
        }
      },
      "required": ["search_term"],
      "type": "object"
    }
  },
  {
    "description": "Get a direct answer to a specific question using Exa's answer endpoint. Best for factual questions that need a concise, well-sourced answer. do not include specific years in questions unless explicitly requested by the user.",
    "name": "exa_answer",
    "parameters": {
      "properties": {
        "question": {
          "description": "A specific question you want answered. Works best with factual questions like 'What is...', 'When did...', 'How much...'. Do not include specific years unless explicitly requested by the user.",
          "type": "string"
        }
      },
      "required": ["question"],
      "type": "object"
    }
  },
  {
    "description": "Conduct comprehensive research on a complex topic using Exa's research endpoint. Best for in-depth analysis, trends, and complex topics requiring detailed investigation. do not include specific years in research topics unless explicitly requested by the user.",
    "name": "exa_research",
    "parameters": {
      "properties": {
        "research_topic": {
          "description": "The topic you want to research in depth. Can be broad or complex topics that require comprehensive analysis. Do not include specific years unless explicitly requested by the user.",
          "type": "string"
        }
      },
      "required": ["research_topic"],
      "type": "object"
    }
  }
];

// Simplified tool execution handlers
class AIToolsExecutor {
  static async readBoard(userId: string, boardId: string, supabase: any): Promise<ToolResult> {
    try {
      console.log(`[AI Tools] readBoard called for boardId: ${boardId}, userId: ${userId}`);

      // Use the authenticated Supabase client directly instead of boardService
      const { data: rawBoardDataAny, error: boardError } = await supabase
        .from('boards')
        .select(`
          *,
          elements:elements(*),
          connections:connections(*),
          board_sharing:board_sharing(*)
        `)
        .eq('id', boardId)
        .maybeSingle();

      if (boardError) {
        console.error(`DB error fetching board ${boardId}:`, boardError);
        return {
          success: false,
          error: 'Failed to fetch board data'
        };
      }

      if (!rawBoardDataAny) {
        console.log(`Board ${boardId} not found or access denied.`);
        return {
          success: false,
          error: 'Board not found or access denied'
        };
      }

      const elements = rawBoardDataAny.elements || [];
      const connections = rawBoardDataAny.connections || [];
      const strokes = rawBoardDataAny.strokes || [];

      return {
        success: true,
        data: {
          elements: elements.map((e: any) => ({
            id: e.id,
            type: e.type,
            content: e.content,
            title: e.title,
            url: e.url,
            position: e.position
          })),
          connections: connections.map((c: any) => ({
            id: c.id,
            fromId: c.fromId,
            toId: c.toId,
            label: c.label
          })),
          elementCount: elements.length,
          connectionCount: connections.length,
          strokeCount: strokes.length,
          message: `Board read successfully with ${elements.length} elements and ${connections.length} connections`
        }
      };
    } catch (error) {
      console.error('Error reading board:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to read board'
      };
    }
  }

  static async readBoardFromData(boardData: any): Promise<ToolResult> {
    try {
      const elements = boardData.elements || [];
      const connections = boardData.connections || [];
      const strokes = boardData.strokes || [];

      console.log(`[AI Tools] readBoardFromData called with ${elements.length} elements, ${connections.length} connections`);
      console.log(`[AI Tools] Element IDs: ${elements.map((e: any) => e.id).join(', ')}`);

      // Create detailed element summaries for better AI understanding
      const elementSummaries = elements.map((e: any) => {
        const summary = `ID: ${e.id}, Type: ${e.type}, Content: "${e.content?.substring(0, 50) || 'No content'}"`;
        return summary + (e.content && e.content.length > 50 ? '...' : '');
      });

      // Create connection summaries
      const connectionSummaries = connections.map((c: any) => {
        const fromElement = elements.find((e: any) => e.id === c.fromId);
        const toElement = elements.find((e: any) => e.id === c.toId);
        return `${c.fromId} (${fromElement?.type || 'unknown'}) -> ${c.toId} (${toElement?.type || 'unknown'})${c.label ? ` [${c.label}]` : ''}`;
      });

      return {
        success: true,
        data: {
          elements: elements.map((e: any) => ({
            id: e.id,
            type: e.type,
            content: e.content,
            title: e.title,
            url: e.url,
            position: e.position
          })),
          connections: connections.map((c: any) => ({
            id: c.id,
            fromId: c.fromId,
            toId: c.toId,
            label: c.label
          })),
          elementCount: elements.length,
          connectionCount: connections.length,
          strokeCount: strokes.length,
          elementSummaries,
          connectionSummaries,
          availableElementIds: elements.map((e: any) => e.id),
          message: `Board read successfully with ${elements.length} elements and ${connections.length} connections. Available element IDs: ${elements.map((e: any) => e.id).join(', ')}`
        }
      };
    } catch (error) {
      console.error('Error reading board from data:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to read board from provided data'
      };
    }
  }

  static async createElement(type: string, position: Position, content: string, title?: string, url?: string, userId?: string, boardId?: string, request?: NextRequest): Promise<ToolResult> {
    try {
      console.log(`[AI Tools] ===== CREATE ELEMENT CALLED =====`);
      console.log(`[AI Tools] createElement called with: type=${type}, position=${JSON.stringify(position)}, content="${content}"`);

      // Validate and correct the element type
      const validTypes = ['sticky-yellow', 'sticky-red', 'sticky-blue', 'text', 'article', 'image'];
      let correctedType = type.toLowerCase();

      // Map common incorrect types to valid ones
      const typeMapping: Record<string, string> = {
        'sticky_note': 'sticky-yellow',
        'sticky-note': 'sticky-yellow',
        'sticky': 'sticky-yellow',
        'note': 'sticky-yellow',
        'sticky_yellow': 'sticky-yellow',
        'sticky_red': 'sticky-red',
        'sticky_blue': 'sticky-blue'
      };

      if (typeMapping[correctedType]) {
        correctedType = typeMapping[correctedType];
        console.log(`[AI Tools] Corrected element type from '${type}' to '${correctedType}'`);
      } else if (!validTypes.includes(correctedType)) {
        console.warn(`[AI Tools] Invalid element type '${type}', defaulting to 'sticky-yellow'`);
        correctedType = 'sticky-yellow';
      }

      const elementId = uuidv4();
      console.log(`[AI Tools] Generated new element ID: ${elementId}`);

      // For image elements, download and store the image
      let imageUrl = url;
      let file_url: string | undefined;
      let proxySucceeded = false;

      if (correctedType === 'image' && url) {
        console.log(`[AI Tools] Image element detected, downloading image from: ${url}`);
        try {
          // Call the proxy-image API to download and store the image
          const proxyUrl = `${process.env.VERCEL_URL || 'http://localhost:3000'}/api/storage/proxy-image`;
          console.log(`[AI Tools] Calling proxy-image at: ${proxyUrl}`);

          const proxyResponse = await fetch(proxyUrl, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Cookie': request?.headers.get('cookie') || '', // Forward cookies for auth
            },
            body: JSON.stringify({ imageUrl: url })
          });

          console.log(`[AI Tools] Proxy response status: ${proxyResponse.status} ${proxyResponse.statusText}`);

          if (proxyResponse.ok) {
            const imageData = await proxyResponse.json();
            console.log(`[AI Tools] Proxy response data:`, imageData);
            if (imageData.success && imageData.path) {
              console.log(`[AI Tools] Successfully downloaded and stored image: ${imageData.path}`);
              file_url = imageData.path;
              proxySucceeded = true;
              // Keep the original URL as imageUrl for immediate display
            } else {
              console.warn(`[AI Tools] Image proxy returned unsuccessful result:`, imageData);
              // Don't set file_url if proxy failed
            }
          } else {
            const errorText = await proxyResponse.text();
            console.warn(`[AI Tools] Failed to proxy image: ${proxyResponse.status} ${proxyResponse.statusText}`, errorText);
            console.warn(`[AI Tools] Error response body:`, errorText);

            // Try to parse error message for better user feedback
            try {
              const errorData = JSON.parse(errorText);
              if (errorData.message) {
                console.warn(`[AI Tools] Proxy error details: ${errorData.message}`);
              }
            } catch (e) {
              // Error text is not JSON, that's fine
            }
          }
        } catch (error) {
          console.error(`[AI Tools] Error downloading image:`, error);
          // Continue with the original URL if download fails
        }
      }

      const element: BoardItem = {
        id: elementId,
        type: correctedType as any,
        content,
        position,
        ...(title && { title }),
        // For images, only set URL if proxy succeeded or if it's not an image
        ...(url && (correctedType !== 'image' || proxySucceeded) && { url: imageUrl }),
        ...(file_url && { file_url })
      };

      console.log(`[AI Tools] Created element object:`, element);

      // Create appropriate message based on image proxy success
      let message = `Created ${element.type} element at position (${position.x}, ${position.y})`;
      if (correctedType === 'image' && url && !proxySucceeded) {
        message += '. Note: Image could not be downloaded and stored - the original URL may be invalid or inaccessible.';
      }

      const result = {
        success: true,
        data: {
          element,
          elementId: element.id,
          type: element.type,
          position: element.position,
          content: element.content,
          ...(title && { title }),
          // For images, only include URL if proxy succeeded or if it's not an image
          ...(url && (correctedType !== 'image' || proxySucceeded) && { url: imageUrl }),
          ...(file_url && { file_url }),
          message
        }
      };

      console.log(`[AI Tools] createElement returning result:`, result);

      console.log(`[AI Tools] Returning result:`, result);
      return result;
    } catch (error) {
      console.error('Error creating element:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create element'
      };
    }
  }

  static async createConnection(fromElementId: string, toElementId: string, label?: string, userId?: string, boardId?: string): Promise<ToolResult> {
    try {
      console.log(`[AI Tools] ===== CREATE CONNECTION CALLED =====`);
      console.log(`[AI Tools] createConnection called with: fromId=${fromElementId}, toId=${toElementId}, label="${label || 'none'}"`);

      // Validate element IDs
      if (!fromElementId || typeof fromElementId !== 'string') {
        console.error(`[AI Tools] Invalid fromElementId: ${fromElementId}`);
        return {
          success: false,
          error: 'Invalid source element ID. Please provide a valid element ID.'
        };
      }

      if (!toElementId || typeof toElementId !== 'string') {
        console.error(`[AI Tools] Invalid toElementId: ${toElementId}`);
        return {
          success: false,
          error: 'Invalid target element ID. Please provide a valid element ID.'
        };
      }

      // Check for self-connection
      if (fromElementId === toElementId) {
        console.error(`[AI Tools] Attempted self-connection: ${fromElementId}`);
        return {
          success: false,
          error: 'Cannot create a connection from an element to itself.'
        };
      }

      const connectionId = uuidv4();
      console.log(`[AI Tools] Generated new connection ID: ${connectionId}`);

      const connection: Connection = {
        id: connectionId,
        fromId: fromElementId,
        toId: toElementId,
        ...(label && { label })
      };

      console.log(`[AI Tools] Created connection object:`, connection);

      const result = {
        success: true,
        data: {
          connection,
          connectionId: connection.id,
          fromId: connection.fromId,
          toId: connection.toId,
          ...(label && { label }),
          message: `Created connection from ${fromElementId} to ${toElementId}${label ? ` with label "${label}"` : ''}`
        }
      };

      console.log(`[AI Tools] createConnection returning result:`, result);
      return result;
    } catch (error) {
      console.error('Error creating connection:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create connection'
      };
    }
  }

  static async moveViewport(targetPosition?: Position, zoomLevel?: number, elementId?: string, userId?: string, boardId?: string): Promise<ToolResult> {
    try {
      return {
        success: true,
        data: {
          action: 'move_viewport',
          targetPosition,
          zoomLevel,
          elementId,
          message: `Viewport move instruction${elementId ? ` to element ${elementId}` : targetPosition ? ` to position (${targetPosition.x}, ${targetPosition.y})` : ''}`
        }
      };
    } catch (error) {
      console.error('Error moving viewport:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to move viewport'
      };
    }
  }

  static async updateElement(elementId: string, updates: Partial<BoardItem>, userId?: string, boardId?: string): Promise<ToolResult> {
    try {
      console.log(`[AI Tools] ===== UPDATE ELEMENT CALLED =====`);
      console.log(`[AI Tools] updateElement called with: elementId=${elementId}, updates=${JSON.stringify(updates)}`);

      if (!elementId || typeof elementId !== 'string') {
        return {
          success: false,
          error: 'Invalid element ID provided'
        };
      }

      if (!updates || typeof updates !== 'object') {
        return {
          success: false,
          error: 'Invalid updates provided'
        };
      }

      // Create the update operation data that will be processed by the frontend
      const updateOperation = {
        action: 'update',
        elementId,
        updates,
        message: `Updated element ${elementId}`
      };

      console.log(`[AI Tools] updateElement returning operation:`, updateOperation);

      return {
        success: true,
        data: {
          operation: updateOperation,
          elementId,
          updates,
          message: `Updated element ${elementId}`
        }
      };
    } catch (error) {
      console.error('Error updating element:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update element'
      };
    }
  }

  static async deleteElement(elementId: string, userId?: string, boardId?: string): Promise<ToolResult> {
    try {
      console.log(`[AI Tools] ===== DELETE ELEMENT CALLED =====`);
      console.log(`[AI Tools] deleteElement called with: elementId=${elementId}`);

      if (!elementId || typeof elementId !== 'string') {
        return {
          success: false,
          error: 'Invalid element ID provided'
        };
      }

      // Create the delete operation data that will be processed by the frontend
      const deleteOperation = {
        action: 'delete',
        elementId,
        message: `Deleted element ${elementId}`
      };

      console.log(`[AI Tools] deleteElement returning operation:`, deleteOperation);

      return {
        success: true,
        data: {
          operation: deleteOperation,
          elementId,
          message: `Deleted element ${elementId}`
        }
      };
    } catch (error) {
      console.error('Error deleting element:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete element'
      };
    }
  }

  static async deleteConnection(connectionId?: string, fromElementId?: string, toElementId?: string, userId?: string, boardId?: string): Promise<ToolResult> {
    try {
      console.log(`[AI Tools] ===== DELETE CONNECTION CALLED =====`);
      console.log(`[AI Tools] deleteConnection called with: connectionId=${connectionId}, fromElementId=${fromElementId}, toElementId=${toElementId}`);

      // Validate that we have either connection ID or both element IDs
      if (!connectionId && (!fromElementId || !toElementId)) {
        return {
          success: false,
          error: 'Either connection_id or both from_element_id and to_element_id must be provided'
        };
      }

      // Create the delete operation data that will be processed by the frontend
      const deleteOperation = {
        action: 'delete',
        connectionId,
        fromElementId,
        toElementId,
        message: connectionId
          ? `Deleted connection ${connectionId}`
          : `Deleted connection between ${fromElementId} and ${toElementId}`
      };

      console.log(`[AI Tools] deleteConnection returning operation:`, deleteOperation);

      return {
        success: true,
        data: {
          operation: deleteOperation,
          connectionId,
          fromElementId,
          toElementId,
          message: deleteOperation.message
        }
      };
    } catch (error) {
      console.error('Error deleting connection:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete connection'
      };
    }
  }

  static async exaSearch(searchTerm: string, numResults: number = 5, searchOptions: any = {}, userId?: string): Promise<ToolResult> {
    try {
      const apiKey = process.env.EXA_AI_KEY;
      if (!apiKey) {
        throw new Error('Exa AI API key is not configured');
      }

      const response = await fetch('https://api.exa.ai/search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': apiKey,
        },
        body: JSON.stringify({
          query: searchTerm,
          numResults: Math.min(numResults, 10),
          ...searchOptions,
          contents: searchOptions?.contents || { text: true, highlights: true },
          useAutoprompt: true
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`Exa search API returned ${response.status}: ${errorData.message || response.statusText}`);
      }

      const data = await response.json();
      
      const results = data.results?.map((result: any) => ({
        title: result.title || 'No title',
        url: result.url || '',
        snippet: result.text?.substring(0, 300) || result.highlights?.join(' ') || 'No content available',
        publishedDate: result.publishedDate || null,
        author: result.author || null,
        type: 'search_result'
      })) || [];

      return {
        success: true,
        data: {
          searchTerm,
          results,
          totalResults: results.length,
          source: 'Exa AI Search',
          requestId: data.requestId,
          message: `Exa search for "${searchTerm}" completed. Found ${results.length} results.`
        }
      };
    } catch (error: any) {
      console.error(`[AI Tools] Exa search failed:`, error);
      return {
        success: false,
        error: error.message || 'Exa search failed to complete'
      };
    }
  }

  static async exaAnswer(question: string, userId?: string): Promise<ToolResult> {
    try {
      const apiKey = process.env.EXA_AI_KEY;
      if (!apiKey) {
        throw new Error('Exa AI API key is not configured');
      }

      const response = await fetch('https://api.exa.ai/answer', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`,
        },
        body: JSON.stringify({
          model: 'exa',
          messages: [{ role: 'user', content: question }],
          stream: false
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`Exa answer API returned ${response.status}: ${errorData.message || response.statusText}`);
      }

      const data = await response.json();
      const messageResp = data.choices?.[0]?.message || {};
      const answer = messageResp.content || 'No answer provided';

      const citations: any[] = [];
      const directCitations = Array.isArray((messageResp as any).citations) ? (messageResp as any).citations : [];

      directCitations.forEach((cite: any, index: number) => {
        if (cite?.url) {
          citations.push({
            title: cite.title || `Answer Source ${index + 1}`,
            url: cite.url,
            snippet: cite.snippet || cite.highlight || 'Citation from answer content',
            author: cite.author || null,
            publishedDate: cite.publishedDate || null,
            type: 'answer_citation'
          });
        }
      });

      return {
        success: true,
        data: {
          question,
          answer,
          citations,
          totalCitations: citations.length,
          source: 'Exa AI Answer',
          usage: data.usage,
          message: `Exa answered "${question}" with ${citations.length} supporting citations.`
        }
      };
    } catch (error: any) {
      console.error(`[AI Tools] Exa answer failed:`, error);
      return {
        success: false,
        error: error.message || 'Exa answer failed to complete'
      };
    }
  }

  static async exaResearch(researchTopic: string, userId?: string): Promise<ToolResult> {
    try {
      console.log('[AI Tools] exaResearch called with topic:', researchTopic);
      
      const apiKey = process.env.EXA_AI_KEY;
      if (!apiKey) {
        throw new Error('Exa AI API key is not configured');
      }

      const response = await fetch('https://api.exa.ai/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`,
        },
        body: JSON.stringify({
          model: 'exa-research',
          messages: [{ role: 'user', content: `Conduct comprehensive research on: ${researchTopic}. Provide detailed analysis with citations and sources.` }],
          stream: false
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`Exa research API returned ${response.status}: ${errorData.message || response.statusText}`);
      }

      const data = await response.json();
      
      let researchContent = '';
      let citations: any[] = [];

      data.choices?.forEach((choice: any) => {
        const message = choice.message || {};
        if (message.function_call) {
          try {
            const args = JSON.parse(message.function_call.arguments || '{}');
            const chunkReport = args.report || args.researchReport || args.research_content || '';
            researchContent += chunkReport + ' ';

            if (Array.isArray(args.citations)) {
              args.citations.forEach((cite: any) => {
                citations.push({
                  title: cite.title || `Research Source ${citations.length + 1}`,
                  url: cite.url || cite.link || '',
                  snippet: cite.snippet || cite.highlight || '',
                  author: cite.author || null,
                  publishedDate: cite.publishedDate || null,
                  type: 'research_citation'
                });
              });
            }
          } catch (e) {
            console.warn('[AI Tools] Failed to parse function_call in choice:', choice.index, e);
          }
        } else if (message.content) {
          researchContent += message.content + ' ';
        }
      });

      researchContent = researchContent.trim() || 'No research content provided';

      return {
        success: true,
        data: {
          researchTopic,
          researchContent,
          citations,
          totalSources: citations.length,
          source: 'Exa AI Research',
          usage: data.usage,
          message: `Exa research on "${researchTopic}" completed with ${citations.length} supporting sources.`
        }
      };
    } catch (error: any) {
      console.error(`[AI Tools] Exa research failed:`, error);
      return {
        success: false,
        error: error.message || 'Exa research failed to complete'
      };
    }
  }
}

export async function POST(request: NextRequest) {
  try {
    const { tool_name, parameters, board_id, board_data } = await request.json();

    if (!tool_name || !parameters) {
      return NextResponse.json({ error: 'Missing tool_name or parameters' }, { status: 400 });
    }

    // Get authenticated user
    const { user, error: authError, supabase } = await getAuthenticatedUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    // Execute the appropriate tool
    let result: ToolResult;

    switch (tool_name) {
      case 'read_board':
        if (board_data) {
          // Use provided board data instead of database lookup
          result = await AIToolsExecutor.readBoardFromData(board_data);
        } else {
          // Fallback to database lookup for backwards compatibility
          result = await AIToolsExecutor.readBoard(user.id, board_id, supabase);
        }
        break;
        
      case 'create_element':
        result = await AIToolsExecutor.createElement(
          parameters.type,
          parameters.position,
          parameters.content,
          parameters.title,
          parameters.url,
          user.id,
          board_id,
          request
        );
        break;
        
      case 'create_connection':
        result = await AIToolsExecutor.createConnection(
          parameters.from_element_id,
          parameters.to_element_id,
          parameters.label,
          user.id,
          board_id
        );
        break;
        
      case 'move_viewport':
        result = await AIToolsExecutor.moveViewport(
          parameters.target_position,
          parameters.zoom_level,
          parameters.element_id,
          user.id,
          board_id
        );
        break;
        
      case 'update_element':
        result = await AIToolsExecutor.updateElement(
          parameters.element_id,
          parameters.updates,
          user.id,
          board_id
        );
        break;
        
      case 'delete_element':
        result = await AIToolsExecutor.deleteElement(
          parameters.element_id,
          user.id,
          board_id
        );
        break;

      case 'delete_connection':
        result = await AIToolsExecutor.deleteConnection(
          parameters.connection_id,
          parameters.from_element_id,
          parameters.to_element_id,
          user.id,
          board_id
        );
        break;

      case 'exa_search':
        console.log('[AI Tools] exa_search called with parameters:', parameters);
        // Handle various parameter formats
        const searchTerm = parameters.search_term || parameters.query || parameters.search_query;
        const numResults = parameters.num_results || parameters.max_results || parameters.maxResults || 5;
        
        if (!searchTerm || typeof searchTerm !== 'string') {
          result = {
            success: false,
            error: 'Invalid search term. Please provide a valid search term string.'
          };
        } else {
          result = await AIToolsExecutor.exaSearch(
            searchTerm,
            numResults,
            parameters.search_options,
            user.id
          );
        }
        break;
        
      case 'exa_answer':
        console.log('[AI Tools] exa_answer called with parameters:', parameters);
        // Handle various parameter formats
        const question = parameters.question || parameters.query || parameters.search_term;
        
        if (!question || typeof question !== 'string') {
          result = {
            success: false,
            error: 'Invalid question. Please provide a valid question string.'
          };
        } else {
          result = await AIToolsExecutor.exaAnswer(
            question,
            user.id
          );
        }
        break;
        
      case 'exa_research':
        console.log('[AI Tools] exa_research called with parameters:', parameters);
        // Handle various parameter formats the AI might use
        const researchTopic = parameters.research_topic || parameters.query || parameters.search_term || parameters.topic;
        
        if (!researchTopic || typeof researchTopic !== 'string') {
          result = {
            success: false,
            error: 'Invalid research topic. Please provide a valid research topic string.'
          };
        } else {
          result = await AIToolsExecutor.exaResearch(
            researchTopic,
            user.id
          );
        }
        break;
        
      default:
        return NextResponse.json({ error: `Unknown tool: ${tool_name}` }, { status: 400 });
    }

    return NextResponse.json(result);

  } catch (error: any) {
    console.error(`[AI Tools] Error executing tool:`, error);
    return NextResponse.json({ 
      success: false, 
      error: error.message || 'Tool execution failed' 
    }, { status: 500 });
  }
}

// GET endpoint to return available tools
export async function GET() {
  return NextResponse.json({
    tools: AI_TOOLS,
    message: 'Available AI tools for detective board manipulation'
  });
}