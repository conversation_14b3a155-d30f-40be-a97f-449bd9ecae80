import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { randomUUID } from 'crypto';
import type { Database } from '@/lib/database.types'; // Ensure this path is correct

// Generate a random 6-character alphanumeric code
function generateVerificationCode(): string {
  const characters = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789';
  let code = '';
  for (let i = 0; i < 6; i++) {
    code += characters.charAt(Math.floor(Math.random() * characters.length));
  }
  return code;
}

export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient<Database>({ cookies });
    const { email, userId } = await request.json();

    if (!email && !userId) {
      return NextResponse.json({ error: 'Email or User ID is required' }, { status: 400 });
    }

    // --- Find user in public.users by email or ID ---
    let userQuery = supabase.from('users').select('id, email, is_verified');

    if (userId) {
      userQuery = userQuery.eq('id', userId);
    } else if (email) {
      userQuery = userQuery.eq('email', email);
    } else {
         // Should not happen due to initial check, but prevents query errors
         return NextResponse.json({ error: 'Email or User ID is required' }, { status: 400 });
    }

    const { data: user, error: userError } = await userQuery.single();

    if (userError || !user) {
      console.error("Resend Code Error - User Fetch:", userError);
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const effectiveUserId = user.id; // Use the ID found in the database
    const effectiveEmail = user.email; // Use the email found in the database

    // --- If user is already verified, don't send a new code ---
    if (user.is_verified) {
      return NextResponse.json({ message: 'User is already verified' }, { status: 400 });
    }

    // --- Check if there's a recent verification code (less than 1 minute old) ---
    const oneMinuteAgo = new Date(Date.now() - 60 * 1000).toISOString();
    const { data: recentCode, error: recentCodeError } = await supabase
      .from('verification_codes')
      .select('id') // Only need to check for existence
      .eq('user_id', effectiveUserId)
      .gt('sent_at', oneMinuteAgo)
      .limit(1); // Check if at least one exists

    if (recentCodeError) {
        console.error("Resend Code Error - Recent Code Check:", recentCodeError);
        // Proceed with caution, maybe allow sending anyway? Or return error?
        // Let's return an error for now to prevent potential spamming if check fails
         return NextResponse.json({ error: 'Failed to check recent codes' }, { status: 500 });
    }

    if (recentCode && recentCode.length > 0) {
      return NextResponse.json({
        error: 'Please wait before requesting a new code',
        retryAfter: 60 // seconds
      }, { status: 429 }); // Too Many Requests
    }

    // --- Generate new verification code ---
    const code = generateVerificationCode();
    const expiresAt = new Date(Date.now() + 30 * 60 * 1000); // 30 minutes
    const sentAt = new Date();
    const verificationId = randomUUID(); // Generate ID for the new code row

    // --- Delete any existing codes for this user ---
    const { error: deleteError } = await supabase
      .from('verification_codes')
      .delete()
      .eq('user_id', effectiveUserId);

    if (deleteError) {
        console.error("Resend Code Error - Deleting Old Codes:", deleteError);
        // Log error but proceed, as inserting the new code is more critical
    }

    // --- Insert new verification code ---
    const { error: insertError } = await supabase
      .from('verification_codes')
      .insert({
        id: verificationId,
        user_id: effectiveUserId,
        code: code,
        sent_at: sentAt.toISOString(),
        expires_at: expiresAt.toISOString()
      });

    if (insertError) {
      console.error("Resend Code Error - Inserting New Code:", insertError);
      return NextResponse.json({ error: 'Failed to save new verification code' }, { status: 500 });
    }

    // --- Send verification email (using existing external API route) ---
    const origin = request.headers.get('origin') || 'http://localhost:3000';
    try {
        const emailResponse = await fetch(`${origin}/api/send-verification-email`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ email: effectiveEmail, code }) // Use the correct user email
        });

        if (!emailResponse.ok) {
            const errorBody = await emailResponse.text();
            console.error(`Failed to send verification email. Status: ${emailResponse.status}, Body: ${errorBody}`);
            // Don't block the response for email failure, but indicate potential issue
            return NextResponse.json({
                message: 'Verification code generated, but failed to send email.',
                email: effectiveEmail,
                verificationSentAt: sentAt.toISOString(),
                // emailError: true // Optionally add a flag
            }, { status: 207 }); // Multi-Status or 200 with info
        }
    } catch (emailApiError) {
         console.error('Error calling email sending API:', emailApiError);
         return NextResponse.json({
                message: 'Verification code generated, but failed to trigger email sending.',
                email: effectiveEmail,
                verificationSentAt: sentAt.toISOString(),
                // emailError: true
            }, { status: 207 }); // Multi-Status or 200 with info
    }

    // --- Success ---
    return NextResponse.json({
      message: 'Verification code sent',
      email: effectiveEmail,
      verificationSentAt: sentAt.toISOString()
    }, { status: 200 });

  } catch (error: any) {
    console.error('Resend Code Route Error:', error);
    return NextResponse.json({
      error: 'An unexpected error occurred while resending the verification code'
    }, { status: 500 });
  }
  // No finally block needed
} 