/**
 * Types related to application services
 */

import { Board, ElementAction, BoardSavePayload, BoardSaveResponse, PenStroke } from './board';
import { BoardItem } from './item';
import { Connection } from './connection';

/**
 * Interface for board-related operations
 */
export interface BoardService {
  getBoard: (boardId: string) => Promise<Board>;
  saveBoard: (payload: BoardSavePayload) => Promise<BoardSaveResponse>;
  updateElement: (boardId: string, element: BoardItem, action: ElementAction) => Promise<{ element?: BoardItem }>;
  updateBatchElements: (boardId: string, elements: BoardItem[], action: ElementAction) => Promise<{ elements?: BoardItem[] }>;
  updateConnection: (boardId: string, connection: Connection, action: ElementAction) => Promise<{ connection?: Connection }>;
  updateBatchConnections: (boardId: string, connections: Connection[], action: ElementAction) => Promise<{ connections?: Connection[] }>;
  updateStrokesOnly: (boardId: string, strokes: PenStroke[]) => Promise<void>;
  deleteBoard: (boardId: string) => Promise<boolean>;
  listBoards: () => Promise<BoardItem[]>;
}

/**
 * Interface for authentication operations
 */
export interface AuthService {
  getAuthToken: () => string | null;
  isAuthenticated: () => boolean;
  redirectToAuth: () => void;
}

/**
 * Interface for extensible item registry
 */
export interface ItemRegistry {
  registerItemType: (plugin: ItemPlugin) => void;
  getItemTypes: () => ItemPlugin[];
  getItemPlugin: (type: string) => ItemPlugin | undefined;
}

/**
 * Definition for an item plugin to extend the board's functionality
 */
export interface ItemPlugin {
  type: string;
  displayName: string;
  component: React.ComponentType<any>;
  toolbarIcon: React.ReactNode;
  defaultProps: Record<string, any>;
  factory: (position: { x: number, y: number }) => BoardItem;
  height: number;
  width: number;
}

/**
 * Interface for command pattern operations
 */
export interface BoardOperation {
  execute: (boardState: Board) => Board;
  undo?: (boardState: Board) => Board;
  type: string;
  payload: any;
}

/**
 * Interface for the operation history manager
 */
export interface OperationHistory {
  addOperation: (operation: BoardOperation) => void;
  undo: () => BoardOperation | undefined;
  redo: () => BoardOperation | undefined;
  canUndo: () => boolean;
  canRedo: () => boolean;
  clear: () => void;
} 