import { NextRequest, NextResponse } from 'next/server';
import type { Database } from '@/lib/database.types';
import { getAuthenticatedUser } from '@/utils/authUtils';

export async function POST(request: NextRequest) {
    try {
        // 1. Get User Session
        const { user, error: authError, supabase: supabaseClient } = await getAuthenticatedUser();
        if (authError || !user) {
            console.error("Update Strokes - User Session Error:", authError);
            return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
        }
        const userId = user.id;

        // 2. Parse Request Body
        const requestBody = await request.json();
        const { boardId, strokes } = requestBody;
        
        // 3. Basic validation
        if (!boardId) {
            return NextResponse.json({ message: 'Board ID is required' }, { status: 400 });
        }
        
        if (!strokes || !Array.isArray(strokes)) {
            return NextResponse.json({ message: 'Strokes array is required' }, { status: 400 });
        }
        
        // 4. Verify board ownership/access
        const { data: boardPermission, error: permissionError } = await supabaseClient
            .from('board_shares')
            .select('permission_level')
            .eq('board_id', boardId)
            .eq('user_id', userId)
            .maybeSingle();
        
        const { data: ownBoard, error: ownBoardError } = await supabaseClient
            .from('boards')
            .select('id')
            .eq('id', boardId)
            .eq('user_id', userId)
            .maybeSingle();
            
        if ((!boardPermission || boardPermission.permission_level !== 'edit') && !ownBoard) {
            return NextResponse.json({ message: 'Permission denied to update this board' }, { status: 403 });
        }
        
        // 5. Update only the strokes column
        const { error: updateError } = await supabaseClient
            .from('boards')
            .update({ strokes, updated_at: new Date().toISOString() })
            .eq('id', boardId);
            
        if (updateError) {
            console.error("Error updating strokes:", updateError);
            return NextResponse.json({ message: 'Failed to update strokes', error: updateError.message }, { status: 500 });
        }
        
        return NextResponse.json({ message: 'Strokes updated successfully' }, { status: 200 });
        
    } catch (error: any) {
        console.error("Unexpected error in update-strokes route:", error);
        return NextResponse.json({ message: 'An unexpected error occurred', error: error.message }, { status: 500 });
    }
} 