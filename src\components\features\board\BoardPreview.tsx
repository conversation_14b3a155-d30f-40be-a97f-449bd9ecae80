import React, { useState, useEffect } from 'react';
import { Loader } from 'lucide-react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import type { Database } from '@/lib/database.types';

interface BoardPreviewProps {
  boardId: string;
  className?: string;
}

const BoardPreview: React.FC<BoardPreviewProps> = ({ boardId, className = '' }) => {
  const [previewImageUrl, setPreviewImageUrl] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchBoardPreview = async () => {
      if (!boardId) return;
      
      setIsLoading(true);
      setError(null);
      
      try {
        const response = await fetch(`/api/board/preview?boardId=${boardId}`);
        
        if (!response.ok) {
          const errorData = await response.json();
          console.error('Preview fetch error:', errorData);
          throw new Error(`Failed to fetch board preview: ${errorData.error || response.statusText}`);
        }
        
        const data = await response.json();
        
        if (data.previewImageUrl) {
          setPreviewImageUrl(data.previewImageUrl);
        } else {
          setPreviewImageUrl(null);
        }
      } catch (error) {
        console.error('Error fetching board preview:', error);
        setError('Failed to load preview');
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchBoardPreview();
  }, [boardId]);

  if (isLoading) {
    return (
      <div className={`bg-noir-800 border border-noir-700 rounded-md overflow-hidden flex items-center justify-center aspect-video ${className}`}>
        <Loader className="animate-spin w-5 h-5 text-gray-500" />
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-noir-800 border border-noir-700 rounded-md overflow-hidden flex items-center justify-center aspect-video ${className}`}>
        <div className="text-xs text-gray-500">Error loading preview</div>
      </div>
    );
  }

  // If we have a preview image URL, show it
  if (previewImageUrl) {
    return (
      <div className={`bg-noir-800 border border-noir-700 rounded-md overflow-hidden aspect-video ${className}`}>
        <img 
          src={previewImageUrl} 
          alt="Board preview" 
          className="w-full h-full object-cover"
          onError={(e) => {
            console.error('Error loading preview image:', previewImageUrl);
            setPreviewImageUrl(null); // Reset to show "No preview available" message
          }}
        />
      </div>
    );
  }

  // No preview image available
  return (
    <div className={`bg-noir-800 border border-noir-700 rounded-md overflow-hidden flex items-center justify-center aspect-video ${className}`}>
      <div className="text-xs text-gray-500">No preview available</div>
    </div>
  );
};

export default BoardPreview; 