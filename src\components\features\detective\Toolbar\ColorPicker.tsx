import React from 'react';

interface ColorPickerProps {
  colors: string[];
  currentColor: string;
  onColorChange: (color: string) => void;
  disabled?: boolean;
}

/**
 * Color picker component for selecting sticky note colors
 */
const ColorPicker: React.FC<ColorPickerProps> = ({
  colors,
  currentColor,
  onColorChange,
  disabled = false
}) => {
  return (
    <div className={`flex space-x-1 ${disabled ? 'opacity-40' : ''}`}>
      {colors.map(color => (
        <button
          key={color}
          className={`w-5 h-5 rounded-full transition-transform ${
            currentColor === color ? 'scale-125 ring-2 ring-white' : ''
          } ${disabled ? 'cursor-not-allowed' : ''}`}
          style={{ backgroundColor: color }}
          onClick={disabled ? undefined : () => onColorChange(color)}
          aria-label={`Select ${color} color`}
          disabled={disabled}
        />
      ))}
    </div>
  );
};

export default ColorPicker; 