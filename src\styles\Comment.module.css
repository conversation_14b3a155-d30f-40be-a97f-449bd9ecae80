/* General <PERSON>ont and Sidebar Background - Variables moved to globals.css */

.comment {
  max-width: 80%;
  background-color: var(--sticky-yellow); /* Example: yellow sticky */
  margin-bottom: 1.5rem; /* Increased spacing */
  padding: 1rem 1.2rem;
  border-radius: 8px; /* Rounded corners */
  border: 1px solid rgba(0, 0, 0, 0.08);
  box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.1); /* Subtle shadow */
  font-family: var(--comment-font);
  color: var(--text-color);
  line-height: 1.6; /* Increased line height */
}

.comment.new-comment {
  animation: fadeInUp 0.3s ease;
}

.nested-comments {
  margin-left: 2rem; /* Increased indent for SVG line */
  /* border-left: 3px solid #C0392B; */ /* Removed - Replaced by SVG */
  /* padding-left: 1rem; */ /* Removed - Handled by margin */
  position: relative; /* Ensure positioning context if not set by parent */
}

/* Apply different background for replies for visual depth */
.nested-comments > .comment {
  background-color: var(--sticky-pink); /* Example: pink sticky for replies */
}

/* Deeper replies can cycle colors or get slightly darker/lighter */
.nested-comments > .nested-comments > .comment {
   background-color: var(--kraft-paper); /* Example: kraft paper */
}

.user-message {
  margin-left: auto;
  background-color: var(--sticky-pink);
}
.assistant-message {
  margin-right: auto;
  background-color: var(--sticky-yellow);
}

.comment-username {
  font-weight: bold;
  color: var(--accent-color); /* Use accent color */
  font-size: 0.95rem;
}

.comment-time {
  font-size: 0.75rem;
  color: var(--subtle-text);
  margin-top: 0.1rem; /* Reduced top margin */
}

.comment-body {
  margin-top: 0.5rem;
  font-size: 0.95rem; /* Slightly larger body text */
  color: var(--text-color);
  margin-bottom: 0.75rem;
}

.comment-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-top: 0.5rem;
}

/* Style reply button */
.reply-button {
  font-size: 0.85rem; /* Slightly larger */
  color: var(--accent-color);
  cursor: pointer;
  transition: color 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-family: var(--comment-font);
  font-weight: bold;
  background: none;
  border: none;
  padding: 0;
}

.reply-button:hover {
  color: #7a3c0f; /* Darker brown, replacing darken() */
}

/* Style reply input container */
.reply-input-container {
  margin-top: 0.75rem;
  margin-bottom: 0.5rem;
  animation: slideDown 0.2s ease;
}

/* Style vote buttons */
.vote-container {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  /* Remove bg/padding/border, rely on button styling */
}

.vote-button {
  cursor: pointer;
  color: var(--subtle-text);
  transition: all 0.2s ease;
  padding: 2px;
  display: flex;
  align-items: center;
  line-height: 1;
  background: none;
  border: none;
}

.vote-button:hover {
  color: var(--accent-color);
  transform: scale(1.1);
}

.vote-button.upvoted {
  color: #00A7B1; /* Use blue sticky note color */
}

.vote-button.downvoted {
  /* Keep a distinct color for downvote if desired, or use subtle text */
  color: #b52a2b; /* Use red sticky note color */
}

.vote-score {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--text-color);
  min-width: 1rem;
  text-align: center;
  padding: 0 2px;
  font-family: var(--comment-font);
}

/* Style Add Comment/Reply Textarea */
.add-comment-input {
  background-color: var(--paper-bg); /* Match paper background */
  border: 1px dashed var(--connector-color); /* Dashed border like torn paper */
  padding: 0.75rem;
  color: var(--text-color);
  border-radius: 6px;
  width: 100%;
  resize: vertical; /* Allow vertical resize */
  font-family: var(--comment-font);
  font-size: 0.95rem;
  line-height: 1.6;
  min-height: 60px; /* Minimum height */
}

.add-comment-input:focus {
  outline: none;
  border-color: var(--accent-color); /* Accent border on focus */
  border-style: solid; /* Solid border on focus */
  box-shadow: 0 0 0 2px rgba(139, 69, 19, 0.2); /* Subtle brown shadow */
}

/* Style Sidebar */
.sidebar {
  position: fixed;
  right: 0;
  top: 0;
  height: 100%;
  min-width: 320px;
  max-width: 600px;
  background-color: var(--paper-bg); /* Paper background */
  /* Optional: Add paper texture via background image */
  /* background-image: url('/path/to/paper-texture.png'); */
  box-shadow: -3px 0 8px rgba(0, 0, 0, 0.15); /* Softer shadow */
  z-index: 100;
  transition: transform 0.3s ease;
  will-change: width, transform;
}

.sidebar-content {
  width: 100%;
  height: 100%;
  overflow-x: hidden;
  /* padding-left: 12px; Removed, handled by inner container padding */
  contain: content;
}

/* Add padding to the inner content area */
.sidebar-content > .p-4 { /* Targeting the direct child div with padding */
  padding: 1.5rem; /* Increase overall padding */
  padding-left: calc(1.5rem + 12px); /* Keep space for resize handle */
}

/* Style Sidebar Header */
.sidebar-content h2 {
  font-family: var(--comment-font);
  color: var(--accent-color);
  font-size: 1.5rem; /* Larger header */
}

/* Style Close Button */
.sidebar-content button[aria-label="Close comments"] {
  color: var(--accent-color);
}
.sidebar-content button[aria-label="Close comments"]:hover {
  color: #7a3c0f; /* Darker brown, replacing darken() */
}


/* Style Resize Handle */
.resize-handle {
  position: absolute;
  left: -6px;
  top: 0;
  width: 12px;
  height: 100%;
  cursor: ew-resize;
  background: transparent;
  transition: background-color 0.2s ease;
  z-index: 1; /* Ensure handle is clickable */
}

.resize-handle::after {
  content: '';
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 2px; /* Thinner handle line */
  background-color: var(--connector-color); /* Use connector color */
  transition: background-color 0.2s ease, width 0.2s ease;
  transform: translateX(-50%); /* Center the line */
}

.resize-handle:hover::after,
.resize-handle.resizing::after {
  background-color: var(--accent-color); /* Accent on hover/resize */
  width: 4px;
}

/* Style scrollbar for webkit browsers */
.sidebar-content ::-webkit-scrollbar {
  width: 8px;
}

.sidebar-content ::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

.sidebar-content ::-webkit-scrollbar-thumb {
  background: var(--button-border);
  border-radius: 4px;
}

.sidebar-content ::-webkit-scrollbar-thumb:hover {
  background: var(--accent-color);
}


@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Style for the main Add Comment / Reply buttons */
/* These need to be targeted in the TSX, adding a specific class */
.commentSubmitButton {
  padding: 0.6rem 1rem;
  border-radius: 20px; /* Rounded pill shape */
  font-family: var(--comment-font);
  font-weight: bold;
  font-size: 0.95rem;
  color: var(--text-color);
  background-color: var(--button-bg);
  border: 1px solid var(--button-border);
  cursor: pointer;
  transition: all 0.2s ease;
  width: 100%; /* Make full width */
  margin-top: 0.5rem; /* Spacing */
}

.commentSubmitButton:hover:not(:disabled) {
  background-color: #d4ccba; /* Darker beige, replacing darken() */
  border-color: #bbaea0; /* Darker border beige, replacing darken() */
  box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.1);
}

.commentSubmitButton:disabled {
  background-color: #e0e0e0; /* More distinct disabled state */
  border-color: #ccc;
  color: #999;
  cursor: not-allowed;
  opacity: 0.7;
}

/* Collapse Button Style */
.collapseButton {
  background: none;
  border: none;
  padding: 0 4px;
  margin-right: 6px; /* Space between button and username */
  cursor: pointer;
  color: var(--subtle-text);
  font-size: 1rem;
  line-height: 1;
  display: inline-block; /* Make inline */
  vertical-align: middle; /* Align with text */
  transition: transform 0.2s ease;
}

.collapseButton:hover {
  color: var(--accent-color);
}

.collapseButton.collapsed {
  transform: rotate(-90deg);
}

/* Add styles for the edit button and indicator */
.edit-button {
  /* Similar style to reply button */
  font-size: 0.85rem;
  color: var(--accent-color);
  cursor: pointer;
  transition: color 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-family: var(--comment-font);
  font-weight: bold;
  background: none;
  border: none;
  padding: 0;
}

.edit-button:hover {
  color: #7a3c0f; /* Darker brown */
}

.editedIndicator {
  font-size: 0.7rem;
  color: var(--subtle-text);
  font-style: italic;
  margin-left: 4px;
}

/* Optional: Container for edit mode */
.editContainer {
  margin-top: 0.5rem;
  margin-bottom: 0.75rem;
}

/* No Comments Message Style */
.noCommentsMessage {
  background-color: rgba(0, 0, 0, 0.03); /* Very subtle background */
  border: 1px dashed var(--connector-color);
  border-radius: 8px;
  padding: 1.5rem;
  text-align: center;
  color: var(--subtle-text);
  font-family: var(--comment-font);
  font-size: 0.95rem;
}

/* Override Text Selection Color for Sidebar */
.sidebar ::-moz-selection { /* Code for Firefox */
  background-color: #a8d1ff; /* Light blue background */
  color: #fff; /* White text */
}

.sidebar ::selection {
  background-color: #a8d1ff; /* Light blue background */
  color: #fff; /* White text */
} 