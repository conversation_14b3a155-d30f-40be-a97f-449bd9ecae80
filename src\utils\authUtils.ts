import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import type { Database } from '@/lib/database.types';

/**
 * Optimized authentication utility for API routes
 * Since middleware already validates the session, we can get user data from the session
 * without making additional getUser() calls to Supabase
 */
export async function getAuthenticatedUser() {
  const cookieStore = cookies();
  const supabase = createRouteHandlerClient<Database>({ cookies: () => cookieStore });

  try {
    // Get the session from the local storage (cookies) without making API call
    const { data: { session }, error } = await supabase.auth.getSession();

    if (error) {
      return { user: null, error: 'Authentication error', supabase };
    }

    if (!session?.user) {
      return { user: null, error: 'Not authenticated', supabase };
    }

    // Return the user from the session (no API call needed)
    return { user: session.user, error: null, supabase };
  } catch (error) {
    return { user: null, error: 'Failed to get authentication', supabase };
  }
}

/**
 * Alternative for cases where you absolutely need fresh user data
 * Use this sparingly, only when you need the most up-to-date user information
 */
export async function getAuthenticatedUserFresh() {
  const cookieStore = cookies();
  const supabase = createRouteHandlerClient<Database>({ cookies: () => cookieStore });

  try {
    const { data: { user }, error } = await supabase.auth.getUser();

    if (error) {
      return { user: null, error: 'Authentication error', supabase };
    }

    if (!user) {
      return { user: null, error: 'Not authenticated', supabase };
    }

    return { user, error: null, supabase };
  } catch (error) {
    return { user: null, error: 'Failed to get authentication', supabase };
  }
} 