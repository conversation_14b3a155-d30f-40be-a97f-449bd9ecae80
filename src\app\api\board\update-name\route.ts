import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import type { Database } from '@/lib/database.types';

import { getAuthenticatedUser } from '@/utils/authUtils';
export async function PUT(request: NextRequest) {
  try {
    const { user, error: authError, supabase } = await getAuthenticatedUser();

    if (authError || !user) {
      console.error("Update Board Name - User Session Error:", authError);
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }
    const userId = user.id;

    const { boardId, newName } = await request.json();

    if (!boardId || typeof boardId !== 'string') {
      return NextResponse.json({ message: 'Invalid board ID' }, { status: 400 });
    }

    if (!newName || typeof newName !== 'string' || newName.trim().length === 0) {
      return NextResponse.json({ message: 'Board name cannot be empty' }, { status: 400 });
    }

    // 1. Verify the board exists and belongs to the current user
    const { data: existingBoard, error: fetchError } = await supabase
      .from('boards')
      .select('user_id')
      .eq('id', boardId)
      .single();

    if (fetchError) {
      console.error('Error fetching board for update:', fetchError);
      if (fetchError.code === 'PGRST116') { // PGRST116: Row to retrieve was not found
        return NextResponse.json({ message: 'Board not found' }, { status: 404 });
      }
      return NextResponse.json({ message: 'Error fetching board details', error: fetchError.message }, { status: 500 });
    }

    if (!existingBoard) {
      return NextResponse.json({ message: 'Board not found' }, { status: 404 });
    }

    if (existingBoard.user_id !== userId) {
      return NextResponse.json({ message: 'Forbidden: You do not own this board' }, { status: 403 });
    }

    // 2. Update the board name
    const { data: updatedBoard, error: updateError } = await supabase
      .from('boards')
      .update({ board_name: newName.trim(), updated_at: new Date().toISOString() })
      .eq('id', boardId)
      .select('id, board_name, updated_at') // Select the fields you want to return
      .single();

    if (updateError) {
      console.error('Error updating board name:', updateError);
      return NextResponse.json({ message: 'Failed to update board name', error: updateError.message }, { status: 500 });
    }

    if (!updatedBoard) {
      // This case should ideally not be reached if the select and update were successful on an existing row
      return NextResponse.json({ message: 'Failed to retrieve updated board data' }, { status: 500 });
    }

    return NextResponse.json({ message: 'Board name updated successfully', board: updatedBoard }, { status: 200 });

  } catch (error) {
    console.error('Generic error updating board name:', error);
    let errorMessage = 'An unknown error occurred';
    if (error instanceof Error) {
        errorMessage = error.message;
    }
    // Check if the error is due to JSON parsing
    if (error instanceof SyntaxError && error.message.includes('JSON')) {
        return NextResponse.json({ message: 'Invalid request body: Malformed JSON' }, { status: 400 });
    }
    return NextResponse.json({ message: 'Server error processing request', error: errorMessage }, { status: 500 });
  }
}