import React from 'react';
import { <PERSON><PERSON><PERSON>t, Search, Heart, MessageSquare, Clock } from 'lucide-react';
import MainLayout from '@/components/layout/MainLayout';
import ReadableFontWrapper from '@/components/layout/ReadableFontWrapper';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { prefetchPublicBoards } from '@/actions/publicBoardsActions';
import { PublicBoardsClient } from './public-boards-client';

// This is the server component that will pre-fetch data
export default async function PublicBoardsPage({
  searchParams,
}: {
  searchParams?: {
    search?: string;
    sortBy?: string;
    sortOrder?: string;
    timePeriod?: string;
  };
}) {
  // Extract search parameters with defaults
  const searchQuery = searchParams?.search || '';
  const sortBy = searchParams?.sortBy || 'likes';
  const sortOrder = searchParams?.sortOrder || 'desc';
  const timePeriod = searchParams?.timePeriod || 'allTime';
  
  // Pre-fetch data using server action
  const initialData = await prefetchPublicBoards({
    limit: 12,
    searchQuery,
    sortBy,
    sortOrder,
    timePeriod,
  });
  
  return (
    <MainLayout>
      <ReadableFontWrapper>
        <PublicBoardsClient 
          initialData={initialData}
          initialSearchQuery={searchQuery}
          initialSortBy={sortBy}
          initialSortOrder={sortOrder}
          initialTimePeriod={timePeriod}
        />
      </ReadableFontWrapper>
    </MainLayout>
  );
} 