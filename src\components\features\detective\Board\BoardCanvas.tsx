import React, { useMemo, useRef } from 'react';
import { motion } from '../../../../utils/MotionWrapper';
import { Grid } from './Grid';
import ConnectionInProgress from '../Connections/ConnectionInProgress';
import { BoardViewState, Position } from '../../../../types';
import { Pencil, Eraser } from 'lucide-react';
import styles from './BoardCanvas.module.css';

interface BoardCanvasProps {
  children: React.ReactNode;
  viewState: BoardViewState;
  boardDimensions: { width: number; height: number };
  containerRef: React.RefObject<HTMLDivElement>;
  onWheel: (event: React.WheelEvent<Element>) => void;
  onCanvasPointerDown: (event: React.PointerEvent<Element>) => void;
  onCanvasPointerMove?: (event: React.PointerEvent<Element>) => void;
  onCanvasPointerUp?: (event: React.PointerEvent<Element>) => void;
  isPenModeActive: boolean;
  isErasing: boolean;
  hoveredStrokeId?: string | null;
  connectStartItemPosition: Position | null;
  isSelectionModeActive: boolean;
}

/**
 * The main canvas component for the detective board
 */
const BoardCanvas: React.FC<BoardCanvasProps> = ({
  children,
  viewState,
  boardDimensions,
  containerRef,
  onWheel,
  onCanvasPointerDown,
  onCanvasPointerMove,
  onCanvasPointerUp,
  isPenModeActive,
  isErasing,
  hoveredStrokeId,
  connectStartItemPosition,
  isSelectionModeActive
}) => {
  const { scale, position, isGrabbing, connectMode, connectStart, selectedItemId, presentationMode } = viewState;

  const boardRef = useRef<HTMLDivElement>(null);

  const borderStyles = useMemo(() => {
    const borderWidth = Math.max(2, Math.round(5 / scale));
    const opacity = Math.min(0.5, 0.2 + (0.4 / scale));
    return {
      border: { borderWidth: `${borderWidth}px`, opacity: opacity }
    };
  }, [scale]);

  const getCursorStyle = () => {
    // If in selection mode, cursor should be crosshair to indicate selection ability
    if (isSelectionModeActive) {
      return 'crosshair';
    }
    // In presentation mode, always use grab cursor
    if (presentationMode) {
      return isGrabbing ? 'grabbing' : 'grab';
    }
    
    if (isErasing) {
      return 'crosshair';
    }
    if (isPenModeActive) {
      return 'url(/pen-cursor.png), cell';
    }
    if (isGrabbing) {
      return 'grabbing';
    }
    return 'grab';
  };

  const handleBoardClick = (e: React.PointerEvent<HTMLDivElement>) => {
    // On mobile, ensure we're handling touch events properly
    if (e.pointerType === 'touch') {
      e.preventDefault();
    }
    onCanvasPointerDown(e);
  };

  return (
    <div
      id="board-canvas"
      ref={containerRef}
      className={`${styles.boardCanvas} bg-noir-300 film-grain ${connectMode ? 'connection-mode' : ''}`}
      onWheel={onWheel}
      onPointerDown={handleBoardClick}
      onPointerMove={onCanvasPointerMove}
      onPointerUp={onCanvasPointerUp}
      style={{
        cursor: getCursorStyle(),
      }}
    >
      {/* --- BEGIN TRY-CATCH --- */}
      {(() => {
        try {
          return (
            <motion.div
              data-testid="board-motion-div"
              className={styles.boardMotionContainer}
              style={{
                scale,
                x: position.x,
                y: position.y,
                pointerEvents: isPenModeActive ? 'none' : 'auto',
              }}
            >
              {/* Grid background */}
              <Grid />
              
              {/* Board boundary - simple dotted border with no corner markers */}
              <div 
                className="absolute border-dashed border-white rounded-lg pointer-events-none"
                style={{
                  width: boardDimensions.width,
                  height: boardDimensions.height,
                  left: 0,
                  top: 0,
                  borderWidth: borderStyles.border.borderWidth,
                  opacity: borderStyles.border.opacity
                }}
              />
              
              {/* Board content */}
              {children}
              
              {/* Connection in progress */}
              {connectMode && connectStart && connectStartItemPosition && (
                <ConnectionInProgress 
                  startItem={{
                    id: connectStart,
                    position: connectStartItemPosition
                  }}
                  scale={scale}
                  boardPosition={position}
                />
              )}
            </motion.div>
          );
        } catch (error) {
          console.error("[BoardCanvas] CRITICAL ERROR rendering main motion.div content:", error);
          // Return a placeholder or error message instead of crashing
          return <div style={{ color: 'red', padding: '20px', fontSize: '20px' }}>Error rendering board content. Check console.</div>;
        }
      })()}
      {/* --- END TRY-CATCH --- */}

      {/* Connection mode indicator */}
      {connectMode && (
        <div 
          className="fixed top-4 left-1/2 transform -translate-x-1/2 z-50 bg-noir-accent text-noir-800 px-4 py-2 rounded-full shadow-lg flex items-center space-x-2 pointer-events-none"
        >
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"></path>
            <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"></path>
          </svg>
          <span>
            {connectStart ? 'Select destination item' : 'Select first item to connect'}
          </span>
        </div>
      )}

      {/* Pen/Eraser mode indicator */}
      {isPenModeActive && (
        <div className={`fixed top-16 left-1/2 transform -translate-x-1/2 z-50 ${isErasing ? 'bg-red-500' : 'bg-green-500'} text-white px-4 py-2 rounded-full shadow-lg flex items-center space-x-2 pointer-events-none`}>
          {isErasing ? <Eraser size={18}/> : <Pencil size={18} />}
          <span>{isErasing ? 'Eraser Mode Active' : 'Drawing Mode Active'}</span>
        </div>
      )}

      {/* Selection mode indicator */}
      {isSelectionModeActive && (
        <div className="fixed top-16 left-1/2 transform -translate-x-1/2 z-50 bg-blue-500 text-white px-4 py-2 rounded-full shadow-lg flex items-center space-x-2 pointer-events-none">
          <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <rect x="3" y="3" width="18" height="18" rx="2" ry="2" strokeDasharray="3 3"/>
          </svg>
          <span>Selection Mode Active - Drag to select items</span>
        </div>
      )}

      {/* Status bar */}
      <div className="fixed bottom-2 left-2 text-white/30 text-xs pointer-events-none">
        {scale.toFixed(2)}x
      </div>

      {/* Instructions only show when not in connect mode and not in presentation mode */}
      {!connectMode && !isPenModeActive && !presentationMode && (
        <motion.div 
          className="fixed top-4 left-1/2 transform -translate-x-1/2 glass-morphism py-2 px-4 rounded-full text-sm text-white/70 pointer-events-none"
          initial={{ y: -50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.5, duration: 0.3 }}
        >
          Drag background to move • Scroll to zoom • Double-click text to edit
        </motion.div>
      )}
    </div>
  );
};

export default BoardCanvas; 