import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import type { Database } from '@/lib/database.types';

import { getAuthenticatedUser } from '@/utils/authUtils';
// Define the bucket name
const BUCKET_NAME = 'images';

// Helper function to generate a unique filename
function generateUniqueFileName(imageUrl: string, attempt: number = 1): string {
  let baseName = 'article-';
  let fileExt = 'jpg'; // Default extension

  try {
    const urlObj = new URL(imageUrl);
    const pathSegments = urlObj.pathname.split('/');
    const urlFileName = pathSegments[pathSegments.length - 1];
    
    if (urlFileName && urlFileName.includes('.')) {
      const parts = urlFileName.split('.');
      fileExt = parts.pop() || 'jpg'; // Extract extension
      baseName += parts.join('.'); // Use filename without extension
    } else {
      const domain = urlObj.hostname.replace(/[^a-z0-9]/gi, '-');
      baseName += `${domain}`; // Use domain as base
    }
  } catch (error) {
    // Fallback if URL parsing fails
    baseName += `image`;
  }

  // Add attempt number if > 1 and timestamp
  const timestamp = Date.now();
  const attemptSuffix = attempt > 1 ? `-retry${attempt}` : '';
  
  // Ensure the final name doesn't get excessively long
  const maxBaseNameLength = 100; 
  baseName = baseName.substring(0, maxBaseNameLength);

  return `${baseName}${attemptSuffix}-${timestamp}.${fileExt}`;
}


/**
 * API endpoint to proxy image downloads and upload to storage
 * This avoids CORS issues that may occur on the client side
 */
export async function POST(request: NextRequest) {
  try {
    // Get the authenticated user
    const { user, error: authError, supabase } = await getAuthenticatedUser();
    
    if (authError || !user) {
      console.error("Image Proxy Error:", authError);
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }
    
    // Parse the request body
    const { imageUrl } = await request.json();
    
    if (!imageUrl) {
      return NextResponse.json({ message: 'No image URL provided' }, { status: 400 });
    }
    
    console.log(`Proxying image from: ${imageUrl}`);
    
    // Download the image from the remote URL
    const imageResponse = await fetch(imageUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      },
    });
    
    if (!imageResponse.ok) {
      console.error(`Failed to fetch image: ${imageResponse.statusText}`);
      return NextResponse.json(
        { message: `Failed to fetch image: ${imageResponse.statusText}` }, 
        { status: 500 }
      );
    }
    
    // Get the image data as ArrayBuffer
    const imageBuffer = await imageResponse.arrayBuffer();
    
    // Get content type from the response headers or use default
    const contentType = imageResponse.headers.get('content-type') || 'image/jpeg';
    
    // Generate initial unique file name
    let attempt = 1;
    let fileName = generateUniqueFileName(imageUrl, attempt);
    let filePath = `${user.id}/${fileName}`;
    
    // --- Upload attempt 1 ---
    console.log(`Attempting to upload image to path: ${filePath}`);
    let { data, error } = await supabase
      .storage
      .from(BUCKET_NAME)
      .upload(filePath, imageBuffer, {
        contentType,
        cacheControl: '3600',
        upsert: false // Important: Don't overwrite existing files initially
      });
    
    // --- Handle "Resource Exists" Error and Retry ---
    if (error && error.message.includes('The resource already exists')) {
      console.warn(`Storage Upload Warning: Resource already exists at ${filePath}. Retrying with a new name.`);
      attempt++;
      fileName = generateUniqueFileName(imageUrl, attempt); // Generate new name for retry
      filePath = `${user.id}/${fileName}`;
      
      console.log(`Retrying upload with new path: ${filePath}`);
      // --- Upload attempt 2 ---
      const retryResult = await supabase
        .storage
        .from(BUCKET_NAME)
        .upload(filePath, imageBuffer, {
          contentType,
          cacheControl: '3600',
          upsert: false // Still use upsert: false for the retry
        });
        
      // Update data and error with the retry result
      data = retryResult.data;
      error = retryResult.error;
    }
    
    // --- Final Error Handling ---
    if (error) {
      // Log error from initial attempt or retry attempt
      console.error("Storage Upload Error:", error.message);
      // Include Supabase error message in the response
      return NextResponse.json(
        { message: 'Failed to upload image', error: error.message }, 
        { status: 500 } 
      );
    }

    // --- Success Check ---
    // Ensure we actually have data before proceeding (should be guaranteed if error is null, but good practice)
    if (!data) {
        console.error("Storage Upload Error: Upload succeeded according to error status, but no data returned.");
        return NextResponse.json(
            { message: 'Failed to upload image', error: 'Upload process completed without error, but no file path was returned.' }, 
            { status: 500 } 
        );
    }
    
    // --- Success ---
    console.log(`Image successfully uploaded to: ${data.path}`);
    // Return the path to the uploaded image (from initial or retry attempt)
    return NextResponse.json({ 
      success: true, 
      path: data.path, // Use the path from the successful upload
      originalUrl: imageUrl
    });
    
  } catch (error: any) { // Catch errors from fetch, JSON parsing, etc.
    console.error("Server Error:", error);
    // Check if error has a message property
    const errorMessage = error?.message ? error.message : String(error);
    return NextResponse.json(
      { message: 'Server error', error: errorMessage }, 
      { status: 500 }
    );
  }
} 