import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import type { Database } from '@/lib/database.types';

import { getAuthenticatedUser } from '@/utils/authUtils';
// Helper to extract relative path from a storage URL
function extractRelativePath(url: string | null): string | null {
  if (!url) return null;
  try {
    // If it's already a relative path (doesn't start with http)
    if (!url.startsWith('http')) {
      // Basic check if it resembles uuid/filename format
      if (url.includes('/')) {
        return url;
      } else {
        console.warn(`Provided path is not a URL and doesn't seem relative: ${url}`);
        return null;
      }
    }

    // Parse the URL
    const parsedUrl = new URL(url);
    const pathSegments = parsedUrl.pathname.split('/');
    
    // Find the bucket name ('images' in this case)
    const bucketName = 'images';
    const bucketIndex = pathSegments.indexOf(bucketName);

    if (bucketIndex !== -1 && bucketIndex < pathSegments.length - 1) {
      // Join the segments after the bucket name
      return pathSegments.slice(bucketIndex + 1).join('/');
    }

    return null;
  } catch (error) {
    console.error(`Error parsing URL for relative path extraction: ${url}`, error);
    return null;
  }
}

export async function POST(request: NextRequest) {
  try {
    const { boardId, imageUrl } = await request.json();
    
    if (!boardId || !imageUrl) {
      return NextResponse.json(
        { error: 'Missing boardId or imageUrl' }, 
        { status: 400 }
      );
    }
    
    const { user, error: authError, supabase } = await getAuthenticatedUser();
    const userId = user?.id;
    
    // Check if this is a public board (anyone can view)
    const { data: boardSharing } = await supabase
      .from('board_sharing')
      .select('public_board')
      .eq('board_id', boardId)
      .single();
    
    const isPublic = boardSharing?.public_board === true;
    
    // Check if this is a preview image - they have a specific format
    const isPreviewImage = imageUrl.includes(`preview-${boardId}`);
    
    // Extract the relative path from the URL
    const relativePath = extractRelativePath(imageUrl);
    if (!relativePath) {
      return NextResponse.json(
        { error: 'Could not extract relative path from image URL' }, 
        { status: 400 }
      );
    }
    
    // Call edge function to get signed URL
    const edgeFunctionUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
      ? `${process.env.NEXT_PUBLIC_SUPABASE_URL}/functions/v1/get-public-image-url`
      : null;
      
    if (!edgeFunctionUrl) {
      return NextResponse.json(
        { error: 'Edge function URL not configured' }, 
        { status: 500 }
      );
    }
    
    const anonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
    if (!anonKey) {
      return NextResponse.json(
        { error: 'Supabase anon key not configured' }, 
        { status: 500 }
      );
    }
    
    // Call the edge function to get a signed URL with longer expiry
    const response = await fetch(edgeFunctionUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${anonKey}`,
        'apikey': anonKey
      },
      body: JSON.stringify({ 
        boardId, 
        filePath: relativePath, 
        userId 
      }),
    });
    
    if (!response.ok) {
      // Special handling for preview images that return 404
      if (response.status === 404 && isPreviewImage) {
        console.log(`Preview image 404, attempting to refresh for board ${boardId}`);
        
        // Try to get the latest preview URL from the database
        const { data: boardData } = await supabase
          .from('boards')
          .select('preview_image_url')
          .eq('id', boardId)
          .single();
          
        if (boardData?.preview_image_url) {
          const freshRelativePath = extractRelativePath(boardData.preview_image_url);
          
          if (freshRelativePath) {
            // Try again with the fresh URL
            const freshResponse = await fetch(edgeFunctionUrl, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${anonKey}`,
                'apikey': anonKey
              },
              body: JSON.stringify({ 
                boardId, 
                filePath: freshRelativePath, 
                userId 
              }),
            });
            
            if (freshResponse.ok) {
              const freshData = await freshResponse.json();
              
              if (freshData.signedUrl) {
                return NextResponse.json(
                  { 
                    signedUrl: freshData.signedUrl,
                    refreshed: true 
                  }, 
                  { 
                    status: 200,
                    headers: {
                      'Cache-Control': 'public, max-age=600, s-maxage=900'
                    }
                  }
                );
              }
            }
          }
        }
      }
      
      // Fall back to public URL if edge function fails
      const { data } = supabase.storage.from('images').getPublicUrl(relativePath);
      
      return NextResponse.json(
        { 
          signedUrl: data.publicUrl,
          isFallback: true
        }, 
        { 
          status: 200,
          headers: {
            'Cache-Control': 'public, max-age=300, s-maxage=600'
          }
        }
      );
    }
    
    const data = await response.json();
    return NextResponse.json(
      { signedUrl: data.signedUrl }, 
      { 
        status: 200,
        headers: {
          'Cache-Control': 'public, max-age=600, s-maxage=900'
        }
      }
    );
  } catch (error) {
    console.error('Error in signed-url API route:', error);
    return NextResponse.json(
      { error: 'Internal server error' }, 
      { status: 500 }
    );
  }
} 