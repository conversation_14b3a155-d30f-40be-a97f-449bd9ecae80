import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import type { Database } from '@/lib/database.types';

/**
 * API route for prefetching public boards data during server-side rendering
 */
export async function GET(request: NextRequest) {
  try {
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient<Database>({ cookies: () => cookieStore });
    const { searchParams } = new URL(request.url);
    
    // Get the same parameters as the client-side API
    const limit = parseInt(searchParams.get('limit') || '12', 10);
    const offset = parseInt(searchParams.get('offset') || '0', 10);
    const searchQuery = searchParams.get('search') || '';
    const sortBy = searchParams.get('sortBy') || 'likes';
    const sortOrder = searchParams.get('sortOrder') || 'desc';
    const timePeriod = searchParams.get('timePeriod') || 'allTime';
    
    // Forward the request to the regular public boards API
    const apiUrl = new URL('/api/board/public', request.url);
    apiUrl.search = searchParams.toString();
    
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      const errorData = await response.json();
      return NextResponse.json({ error: errorData.error || 'Failed to prefetch data' }, { status: response.status });
    }
    
    const data = await response.json();
    
    // Cache the response with cache-control header
    const result = NextResponse.json(data);
    result.headers.set('Cache-Control', 'public, max-age=30, s-maxage=60, stale-while-revalidate=300');
    
    return result;
  } catch (error) {
    console.error('[prefetch-public] Error:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
} 