import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import type { Database } from '@/lib/database.types';

export async function POST(request: NextRequest) {
  const cookieStore = cookies();
  // Create a supabase client for the current user session (or anon if not logged in)
  const supabase = createRouteHandlerClient<Database>({ cookies: () => cookieStore });

  try {
    const { boardId } = await request.json();

    if (!boardId) {
      return NextResponse.json({ error: 'Missing boardId' }, { status: 400 });
    }

    console.log(`API route received boardId: ${boardId}`);

    // Call the database function to log the view and increment the count
    const { error: rpcError } = await supabase.rpc('log_and_increment_view', {
      board_uuid: boardId,
    });

    if (rpcError) {
      console.error('Error calling log_and_increment_view RPC:', rpcError);
      // Don't expose detailed errors usually, but log them
      return NextResponse.json({ error: 'Failed to record view' }, { status: 500 });
    }

    // Successfully recorded the view (or at least called the function)
    return NextResponse.json({ success: true }, { status: 200 });

  } catch (error) {
    console.error('Error in /api/board/view:', error);
    // Handle JSON parsing errors or other unexpected issues
    return NextResponse.json({ error: 'An unexpected error occurred' }, { status: 500 });
  }
} 