import React from 'react';

interface ToolButtonProps {
  onClick: () => void;
  icon: React.ReactNode;
  label: string;
  isActive?: boolean;
  className?: string;
  disabled?: boolean;
}

/**
 * Reusable tool button component for the toolbar
 */
const ToolButton: React.FC<ToolButtonProps> = ({
  onClick,
  icon,
  label,
  isActive = false,
  className = '',
  disabled = false
}) => {
  return (
    <button 
      className={`p-3 rounded-full transition-colors relative group ${
        isActive ? 'bg-noir-50 text-noir-accent' : 'text-white'
      } ${disabled ? 'opacity-40 cursor-not-allowed' : 'hover:bg-noir-50'} ${className}`}
      onClick={disabled ? undefined : onClick}
      aria-label={label}
      disabled={disabled}
    >
      {icon}
      <span className="absolute -top-10 left-1/2 transform -translate-x-1/2 bg-noir-50 text-white px-2 py-1 rounded text-xs opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
        {label}
      </span>
    </button>
  );
};

export default ToolButton; 