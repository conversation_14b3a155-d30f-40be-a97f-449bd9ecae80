import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Send, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import styles from '@/styles/Comment.module.css';

interface ContextItem {
  id: string;
  content: string;
  color: string;
  createdAt: Date;
}

interface AIChatInputProps {
  onSubmit: (message: string, contextItems: ContextItem[]) => Promise<void>;
  isLoading: boolean;
  disabled: boolean;
  contextItems: ContextItem[];
  onContextItemsChange: (items: ContextItem[]) => void;
  onFocusContext: (contextId: string) => void;
  placeholder?: string;
}

const AIChatInput: React.FC<AIChatInputProps> = ({
  onSubmit,
  isLoading,
  disabled,
  contextItems,
  onContextItemsChange,
  onFocusContext,
  placeholder = "Research a topic..."
}) => {
  const [input, setInput] = useState('');
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Auto-resize the textarea based on content
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${Math.min(textareaRef.current.scrollHeight, 150)}px`;
    }
  }, [input]);

  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim() || isLoading || disabled) return;

    const currentInput = input;
    setInput('');
    
    try {
      await onSubmit(currentInput, contextItems);
    } catch (error) {
      // Restore input on error
      setInput(currentInput);
    }
  }, [input, isLoading, disabled, onSubmit, contextItems]);

  const handleContextClick = useCallback((contextId: string) => {
    onFocusContext(contextId);
  }, [onFocusContext]);

  const removeContextItem = useCallback((contextId: string) => {
    onContextItemsChange(contextItems.filter(item => item.id !== contextId));
  }, [contextItems, onContextItemsChange]);

  return (
    <form onSubmit={handleSubmit} className="flex flex-col space-y-2">
      <div className={`${styles['add-comment-input']} relative flex flex-col`}>
        {contextItems.length > 0 && (
          <div className="p-2 flex gap-1 flex-wrap">
            {contextItems.map((item, index) => (
              <div
                key={item.id}
                className="flex items-center gap-1 px-1.5 py-0.5 rounded border border-white/30 text-xs text-white font-bold hover:scale-105 transition-transform"
                style={{ backgroundColor: item.color }}
                title={`Context ${index + 1}: ${item.content.substring(0, 50)}...`}
              >
                <button
                  type="button"
                  onClick={() => handleContextClick(item.id)}
                  className="hover:underline"
                >
                  {index + 1}
                </button>
                <button
                  type="button"
                  className="hover:bg-white/20 rounded px-0.5 leading-none"
                  onClick={(e) => {
                    e.stopPropagation();
                    removeContextItem(item.id);
                  }}
                  title={`Remove context ${index + 1}`}
                >
                  ×
                </button>
              </div>
            ))}
          </div>
        )}
        <div className="flex items-start gap-1">
          <textarea
            ref={textareaRef}
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                handleSubmit(e);
              }
            }}
            placeholder={placeholder}
            className="flex-1 min-w-0 w-full bg-transparent border-none focus:outline-none p-2 min-h-[40px] resize-none"
            disabled={isLoading || disabled}
            rows={1}
          />
          <Button
            type="submit"
            disabled={!input.trim() || isLoading || disabled}
            size="icon"
            className="h-8 w-8 flex-shrink-0 m-1"
          >
            {isLoading ? (
              <Loader2 size={14} className="animate-spin" />
            ) : (
              <Send size={14} />
            )}
          </Button>
        </div>
      </div>
    </form>
  );
};

export default AIChatInput;
export type { ContextItem }; 