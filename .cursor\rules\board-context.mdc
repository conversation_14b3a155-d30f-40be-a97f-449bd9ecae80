---
description: overview of board context folder
globs: 
alwaysApply: false
---
# Board Context (`BoardContext.tsx`)

This file ([src/context/BoardContext.tsx](mdc:src/context/BoardContext.tsx)) defines the central state management for the collaborative board feature.

## Key Responsibilities:

*   **Centralized State:** Manages the core `board` object (including items, connections, strokes), view state (zoom, pan, selection), and tool states (pen, eraser, connection mode, presentation mode).
*   **Hook Integration:** Combines functionality from several custom hooks:
    *   `useBoardState`: Manages board data (items, connections, strokes) and basic operations.
    *   `useZoomAndPan`: Handles canvas navigation (zoom, pan).
    *   `useConnections`: Manages creating, deleting, and tracking connections between items.
    *   `useModalState`: Controls the visibility and data for various modals (Article form, Save dialog, etc.).
    *   `useAutoSave`: Handles automatic saving of board state changes to the backend.
    *   `usePenTool`: (Although not explicitly listed as a hook import, functionality related to pen/eraser mode is managed here).
*   **Action Dispatch:** Provides functions to interact with the board state (e.g., `addItem`, `updateItem`, `deleteItem`, `addConnection`, `togglePenMode`, `togglePresentationMode`).
*   **Context Provision:** Wraps the application in `BoardProvider` to make the state and actions available throughout the component tree.
*   **Hook Export:** Exports the `useBoard` hook for components to easily access the board context value.

This context is essential for any component that needs to read or modify the board's state or interact with its elements.

