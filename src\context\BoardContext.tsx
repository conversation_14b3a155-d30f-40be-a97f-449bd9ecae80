import React, { createContext, useContext, ReactNode, useEffect, useState, useCallback, useRef } from 'react';
import { useParams } from 'next/navigation';
import { 
  useBoardState, 
  useZoomAndPan, 
  useConnections, 
  useModalState, 
  useAutoSave,
  usePenTool,
  useRealtimeBoardSync
} from '../hooks';
import { 
  Board, 
  BoardItem, 
  Position, 
  Connection, 
  ArticleFormState,
  ModalState,
  PenStroke,
  BoardViewState,
  FullBoardState
} from '../types';
import { initializeDefaultItemTypes } from '../services';
import { v4 as uuidv4 } from 'uuid';

// Initialize the item registry with default item types
initializeDefaultItemTypes();

/**
 * Interface for the board context value
 */
interface BoardContextValue {
  // Board state
  board: Board;
  boardId?: string;
  setBoard: React.Dispatch<React.SetStateAction<Board>>;
  
  // View state
  scale: number;
  position: Position;
  isGrabbing: boolean;
  selectedItemId: string | null;
  presentationMode: boolean;
  togglePresentationMode: () => void;
  setScale: (scale: number) => void;
  setPosition: (position: Position) => void;
  setScaleAndPosition: (scale: number, position: Position) => void;
  
  // Item operations
  addItem: (type: string, position?: Position, props?: Record<string, any>, options?: { skipSync?: boolean }) => Promise<BoardItem>;
  addBatchItems: (items: Array<{type: string, position?: Position, props?: Record<string, any>}>, options?: { skipSync?: boolean }) => Promise<BoardItem[]>;
  updateItem: (id: string, updates: Partial<BoardItem>, options?: { skipSync?: boolean }) => void;
  deleteItem: (id: string, options?: { skipSync?: boolean }) => void;
  updateItemPosition: (id: string, position: Position, options?: { skipSync?: boolean }) => void;
  updateItemContent: (id: string, content: string, options?: { skipSync?: boolean }) => void;
  selectItem: (id: string | null) => void;
  saveItemToServer: (itemId: string, action: 'add' | 'update' | 'delete', itemToSaveOverride?: BoardItem) => Promise<any>;
  
  // Zoom and pan operations
  handleWheel: (e: React.WheelEvent) => void;
  handleCanvasPointerDown: (event: React.PointerEvent<Element>) => void;
  resetView: () => void;
  boardDimensions: { width: number; height: number };
  
  // Connection operations
  connections: Connection[];
  connectMode: boolean;
  connectStart: string | null;
  toggleConnectMode: () => void;
  startConnection: (itemId: string) => void;
  completeConnection: (itemId: string) => void;
  cancelConnection: () => void;
  deleteConnection: (id: string, options?: { skipSync?: boolean }) => void;
  addConnection: (fromId: string, toId: string, options?: { skipSync?: boolean }) => Connection;
  addBatchConnections: (connections: Array<{fromId: string, toId: string, label?: string}>, options?: { skipSync?: boolean }) => Connection[];
  
  // Modal operations
  modalState: ModalState;
  openArticleForm: (initialData?: ArticleFormState) => void;
  closeArticleForm: () => void;
  openSaveModal: () => void;
  closeSaveModal: () => void;
  openExitConfirmation: (destination: string) => void;
  closeExitConfirmation: () => void;
  openImageUploadModal: () => void;
  closeImageUploadModal: () => void;
  setArticleFormData: (data: Partial<ArticleFormState>) => void;
  handleArticleFormSubmit: (data: ArticleFormState) => ArticleFormState;
  handleSaveFormSubmit: (boardName: string) => Promise<void>;
  
  // Save operations
  saveBoard: () => Promise<string | null>;
  isSaving: boolean;
  lastSaved: Date | null;
  debouncedSaveBoardState: (boardState: FullBoardState) => void;
  removeStrokesNearPoint: (point: Position, radius: number, options?: { skipSync?: boolean }) => void;

  // Pen tool operations
  isPenModeActive: boolean;
  togglePenMode: () => void;
  addStroke: (stroke: PenStroke, options?: { skipSync?: boolean }) => void;
  isErasing: boolean;
  toggleEraserMode: () => void;

  // --- Remote Cursors ---
  remoteCursors: Record<string, { x: number; y: number; lastSeen: number }>;

  // Realtime (Presence/Broadcast)
  userCount: number;
  isMultiUser: boolean;
  broadcast: (event: string, payload: any) => void;
  onBroadcast: (event: string | '*', handler: (payload: any) => void) => void;

  // Selection Mode
  isSelectionModeActive: boolean;
  selectedItemIds: string[];
  toggleSelectionMode: () => void;
  setSelectedItemIds: (ids: string[]) => void;
  moveSelectedItemsByDelta: (delta: { dx: number; dy: number }, options?: { skipSync?: boolean }) => void;

  // New board creation state
  isCreatingNewBoard: boolean;
  setIsCreatingNewBoard: React.Dispatch<React.SetStateAction<boolean>>;
}

// Create the context with a default undefined value
const BoardContext = createContext<BoardContextValue | undefined>(undefined);

/**
 * Props for the BoardProvider component
 */
interface BoardProviderProps {
  children: ReactNode;
  initialBoardName?: string;
}

/**
 * Provider component for the board context
 */
export const BoardProvider: React.FC<BoardProviderProps> = ({ 
  children, 
  initialBoardName = 'Untitled board'
}) => {
  // Get board ID from URL params
  const params = useParams();
  const boardId = params?.id as string;

  // State for new board creation process
  const [isCreatingNewBoard, setIsCreatingNewBoard] = useState<boolean>(false);

  // Use the auto save hook first to get the debounced saver
  const {
    saveBoard: saveEntireBoard,
    saveElementChange,
    saveConnectionChange,
    isSaving,
    lastSaved,
    debouncedSaveBoardState
  } = useAutoSave(boardId, initialBoardName);
  
  // --- Realtime Sync (Presence + Broadcast) ---
  const {
    userCount,
    isMultiUser,
    broadcast,
    onBroadcast
  } = useRealtimeBoardSync(boardId);
  
  // --- State for presentation mode ---
  const [presentationMode, setPresentationMode] = useState<boolean>(false);

  // Use the board state hook, passing the debounced saver
  const { 
    board, 
    viewState, 
    setViewState,
    addItem, 
    addBatchItems,
    updateItem, 
    deleteItem, 
    updateItemPosition,
    updateItemPositionByDelta,
    updateItemContent, 
    selectItem: initialSelectItem,
    addConnection: initialAddConnection,
    addBatchConnections: initialAddBatchConnections,
    addStroke: initialAddStroke,
    removeStrokesNearPoint: initialRemoveStrokesNearPoint,
    setBoard,
    saveItemToServer
  } = useBoardState(boardId, debouncedSaveBoardState);

  // Use the zoom and pan hook
  const {
    scale,
    position,
    isGrabbing,
    setScale,
    handleWheel,
    handleCanvasPointerDown,
    resetView,
    boardDimensions,
    setPosition,
    setScaleAndPosition
  } = useZoomAndPan();

  /* --------------------------------------------------
   *  Emit helper and addConnection wrapper (needs to be
   *  declared BEFORE we call useConnections).
   * -------------------------------------------------- */

  // Emit helper – only broadcast when multi-user mode is active
  const emit = useCallback((event: string, payload: any) => {
    if (isMultiUser) {
      broadcast(event, payload);
    }
  }, [isMultiUser, broadcast]);

  // Wrapper used *before* useConnections so that new connections get broadcast
  const addConnectionAndBroadcast = useCallback(
    (fromId: string, toId: string, options?: { skipSync?: boolean }): Connection => {
      const newConn = initialAddConnection(fromId, toId, options);
      emit('conn_added', { connection: newConn });
      return newConn;
    },
    [initialAddConnection, emit]
  );

  // Use the connections hook
  const connectionsHook = useConnections(board.connections, boardId, addConnectionAndBroadcast, (updatedConnections) => {
    setBoard((prevBoard: Board) => ({
      ...prevBoard,
      connections: updatedConnections
    }));
  });

  // Ensure the hook state stays in sync with board.connections
  useEffect(() => {
    if (board.connections?.length) {
      if (connectionsHook.connections.length === 0 || board.connections.length !== connectionsHook.connections.length) {
        connectionsHook.setConnectionsFromBoard(board.connections);
      }
    }
  }, [board.connections, connectionsHook.connections.length, connectionsHook.setConnectionsFromBoard]);

  // Sync viewState with zoom and pan state and other relevant states
  useEffect(() => {
    setViewState({
      scale,
      position,
      isGrabbing: viewState.isGrabbing,
      selectedItemId: viewState.selectedItemId,
      connectMode: connectionsHook.connectMode,
      connectStart: connectionsHook.connectStart,
      presentationMode,
    });
  }, [
    scale, position,
    viewState.selectedItemId, 
    connectionsHook.connectMode, connectionsHook.connectStart, 
    presentationMode, setViewState
  ]);

  // Use the modal state hook
  const {
    modalState,
    openArticleForm,
    closeArticleForm,
    openSaveModal,
    closeSaveModal,
    openExitConfirmation,
    closeExitConfirmation,
    openImageUploadModal,
    closeImageUploadModal,
    setArticleFormData,
    handleArticleFormSubmit,
    handleSaveFormSubmit: modalHandleSaveFormSubmit
  } = useModalState(boardId, initialBoardName, setIsCreatingNewBoard);

  // Save wrapper that includes all board elements
  const saveBoard = async (): Promise<string | null> => {
    return saveEntireBoard();
  };

  // --- Pen/Eraser Mode State (Managed Here) ---
  const [isPenModeActive, setIsPenModeActive] = useState<boolean>(false);
  const [isErasing, setIsErasing] = useState<boolean>(false);

  // --- Selection Mode State ---
  const [isSelectionModeActive, setIsSelectionModeActive] = useState<boolean>(false);
  const [selectedItemIds, setSelectedItemIds] = useState<string[]>([]);

  // Master selectItem that handles single and multi-selection logic
  const selectItem = useCallback((id: string | null) => {
    initialSelectItem(id);
    if (id !== null) {
      setSelectedItemIds([]);
      if (isPenModeActive) {
        setIsPenModeActive(false);
        setIsErasing(false);
      }
    }
  }, [initialSelectItem, isPenModeActive]);

  const handleSetSelectedItemIds = useCallback((ids: string[]) => {
    setSelectedItemIds(ids);
    if (ids.length > 0) {
      initialSelectItem(null);
    }
  }, [initialSelectItem]);

  const togglePenMode = useCallback(() => {
    setIsPenModeActive(prev => {
      const nextState = !prev;
      if (nextState) {
        setIsErasing(false);
        selectItem(null);
        if (connectionsHook.connectMode) connectionsHook.toggleConnectMode();
        setIsSelectionModeActive(false);
      }
      return nextState;
    });
  }, [selectItem, connectionsHook]);

  const toggleEraserMode = useCallback(() => {
    if (isPenModeActive) {
       setIsErasing(prev => !prev);
       if (!isErasing) {
          setIsSelectionModeActive(false);
          setSelectedItemIds([]);
          selectItem(null);
       }
    }
  }, [isPenModeActive, isErasing, selectItem]);

  const mainToggleConnectMode = useCallback(() => {
    connectionsHook.toggleConnectMode();
    setIsPenModeActive(false);
    setIsErasing(false);
    setIsSelectionModeActive(false);
    selectItem(null);
  }, [connectionsHook, selectItem]);

  const toggleSelectionMode = useCallback(() => {
    setIsSelectionModeActive(prev => {
      const nextState = !prev;
      if (nextState) {
        selectItem(null);
        if (connectionsHook.connectMode) connectionsHook.toggleConnectMode();
        setIsPenModeActive(false);
        setIsErasing(false);
      } else {
        setSelectedItemIds([]);
      }
      return nextState;
    });
  }, [selectItem, connectionsHook]);

  const togglePresentationMode = useCallback(() => {
    setPresentationMode(prev => {
      const nextState = !prev;
      if (nextState) {
        selectItem(null);
        if (connectionsHook.connectMode) connectionsHook.toggleConnectMode();
        setIsPenModeActive(false); 
        setIsErasing(false);
        setIsSelectionModeActive(false); 
      }
      return nextState;
    });
  }, [selectItem, connectionsHook]);

  /* --------------------------------------------------
   *  Step 2: Wrapped mutation functions (emit defined above)
   * -------------------------------------------------- */

  // ---------- Item wrappers ----------
  const wrappedAddItem = useCallback(async (type: string, position?: Position, props?: Record<string, any>, options?: { skipSync?: boolean }) => {
    const newItem = await addItem(type, position, props, options);
    emit('item_added', { item: newItem });
    return newItem;
  }, [addItem, emit]);

  const wrappedUpdateItem = useCallback((id: string, updates: Partial<BoardItem>, options?: { skipSync?: boolean }) => {
    updateItem(id, updates, options);
    emit('item_updated', { item: { id, ...updates } });
  }, [updateItem, emit]);

  const wrappedDeleteItem = useCallback((id: string, options?: { skipSync?: boolean }) => {
    const itemToDelete = board.elements.find(item => item.id === id);
    deleteItem(id, options);
    if (itemToDelete) {
      emit('item_deleted', { id, item: itemToDelete });
    }
  }, [board.elements, deleteItem, emit]);

  const wrappedUpdateItemPosition = useCallback((id: string, position: Position, options?: { skipSync?: boolean }) => {
    console.log('updateItemPosition called', id, position, options);
    updateItemPosition(id, position, options);
    emit('item_moved', { id, position });
  }, [updateItemPosition, emit]);

  const wrappedUpdateItemContent = useCallback((id: string, content: string, options?: { skipSync?: boolean }) => {
    updateItemContent(id, content, options);
    emit('item_content', { id, content });
  }, [updateItemContent, emit]);

  // ---------- Connection wrappers ----------
  const wrappedAddConnection = addConnectionAndBroadcast;

  const wrappedDeleteConnection = useCallback((id: string, options?: { skipSync?: boolean }) => {
    const connToDelete = connectionsHook.connections.find(conn => conn.id === id);
    connectionsHook.deleteConnection(id, options);
    if (connToDelete) {
      emit('conn_deleted', { id, connection: connToDelete });
    }
  }, [connectionsHook.connections, connectionsHook.deleteConnection, emit]);

  // ---------- Stroke wrappers ----------
  const wrappedAddStroke = useCallback((stroke: PenStroke, options?: { skipSync?: boolean }) => {
    initialAddStroke(stroke, options);
    emit('stroke_added', { stroke });
  }, [initialAddStroke, emit]);

  const wrappedRemoveStrokesNearPoint = useCallback((point: Position, radius: number, options?: { skipSync?: boolean }) => {
    initialRemoveStrokesNearPoint(point, radius, options);
    emit('stroke_erased', { point, radius });
  }, [initialRemoveStrokesNearPoint, emit]);

  // ---------- BATCH WRAPPERS ----------
  const wrappedAddBatchItems = useCallback(async (
    items: Array<{ type: string; position?: Position; props?: Record<string, any> }>,
    options?: { skipSync?: boolean }
  ) => {
    const newItems = await addBatchItems(items, options);
    emit('items_batch_added', { items: newItems });
    return newItems;
  }, [addBatchItems, emit]);

  const wrappedAddBatchConnections = useCallback((
    conns: Array<{ fromId: string; toId: string; label?: string }>,
    options?: { skipSync?: boolean }
  ) => {
    const newConnections = initialAddBatchConnections(conns, options);
    emit('conns_batch_added', { connections: newConnections });
    return newConnections;
  }, [initialAddBatchConnections, emit]);

  const wrappedMoveSelectedItemsByDelta = useCallback((delta: { dx: number; dy: number }, options?: { skipSync?: boolean }) => {
    if (selectedItemIds.length === 0) return;

    selectedItemIds.forEach(itemId => {
      updateItemPositionByDelta(itemId, delta, { ...options, skipSync: true }); // Internal updates skip individual sync
    });

    if (!options?.skipSync) {
      // Emit a single event for the batch move for other clients
      emit('items_moved_delta', { itemIds: selectedItemIds, delta });
    }
  }, [selectedItemIds, updateItemPositionByDelta, emit]);

  /* --------------------------------------------------
   *  Step 3: Register inbound broadcast handlers
   * -------------------------------------------------- */

  useEffect(() => {
    if (!boardId || boardId === 'new') return;

    // --- Item handlers ---
    onBroadcast('item_added', ({ item }) => {
      console.log(`[BoardContext] 📥 Received realtime board update: item_added`, item);
      console.log(`[BoardContext] Current board has ${board.elements.length} elements before adding`);

      // Check if item already exists (prevent duplicates)
      const exists = board.elements.some(existing => existing.id === item.id);
      if (!exists) {
        console.log(`[BoardContext] 📋 Adding item to board with ID ${item.id}`);
        // Use setBoard to directly add the item (safer than addItem which might reset)
        setBoard(currentBoard => {
          console.log(`[BoardContext] setBoard callback: adding item ${item.id} to ${currentBoard.elements.length} existing elements`);
          return {
            ...currentBoard,
            elements: [...currentBoard.elements, item]
          };
        });
      } else {
        console.log(`[BoardContext] ⚠️ Item ${item.id} already exists in board, skipping addition`);
      }
    });

    onBroadcast('item_updated', ({ item }) => {
      console.log(`📥 Received realtime board update: item_updated`, item);
      const { id, ...updates } = item;
      
      // Always use current board state via callback to avoid stale data
      setBoard(currentBoard => {
        // Check if item exists in CURRENT board state
        const itemExists = currentBoard.elements.some(e => e.id === id);
        console.log(`📋 [ITEM UPDATED] Item with ID ${id} exists in currentBoard: ${itemExists}`);
        
        if (!itemExists) {
          console.warn(`⚠️ Cannot update item ${id} - not found in current board state`);
          return currentBoard; // Return unchanged
        }
        
        // Update found, applying all updates
        console.log(`📋 Directly updating item ${id} with:`, updates);
        return {
          ...currentBoard,
          elements: currentBoard.elements.map(item => 
            item.id === id 
              ? { ...item, ...updates } 
              : item
          )
        };
      });
    });

    onBroadcast('item_deleted', ({ id }) => {
      console.log(`📥 Received realtime board update: item_deleted`, { id });
      
      // Log current board elements for debugging
      console.log(`📋 Current board elements before delete:`, board.elements.map(e => ({ id: e.id, type: e.type })));
      const itemExists = board.elements.some(e => e.id === id);
      console.log(`📋 Item with ID ${id} exists in board: ${itemExists}`);
      
      // Directly update the board state instead of using deleteItem
      setBoard(currentBoard => {
        // Log for debugging
        const elementExists = currentBoard.elements.some(e => e.id === id);
        console.log(`📋 Element ${id} exists in currentBoard: ${elementExists}`);
        
        if (!elementExists) {
          console.warn(`⚠️ Cannot delete item ${id} - not found in current board state`);
          return currentBoard; // Return unchanged board if item not found
        }
        
        // Filter out the deleted item
        return {
          ...currentBoard,
          elements: currentBoard.elements.filter(item => item.id !== id),
          // Also remove any connections involving this item
          connections: currentBoard.connections.filter(
            conn => conn.fromId !== id && conn.toId !== id
          )
        };
      });
    });

    onBroadcast('item_moved', ({ id, position }) => {
      console.log(`📥 Received realtime board update: item_moved`, { id, position });
      
      // Always use current board state via callback to avoid stale data
      setBoard(currentBoard => {
        // Check if item exists in CURRENT board state
        const itemExists = currentBoard.elements.some(e => e.id === id);
        console.log(`📋 [ITEM MOVED] Item with ID ${id} exists in currentBoard: ${itemExists}`);
        
        if (!itemExists) {
          console.warn(`⚠️ Cannot move item ${id} - not found in current board state`);
          return currentBoard; // Return unchanged
        }
        
        // Update found, updating position
        console.log(`📋 Directly updating position for ${id}`, position);
        return {
          ...currentBoard,
          elements: currentBoard.elements.map(item => 
            item.id === id 
              ? { ...item, position } 
              : item
          )
        };
      });
    });

    onBroadcast('item_content', ({ id, content }) => {
      console.log(`📥 Received realtime board update: item_content`, { id, content });
      
      // Always use current board state via callback to avoid stale data
      setBoard(currentBoard => {
        // Check if item exists in CURRENT board state
        const itemExists = currentBoard.elements.some(e => e.id === id);
        console.log(`📋 [ITEM CONTENT] Item with ID ${id} exists in currentBoard: ${itemExists}`);
        console.log(`📋 Current board has ${currentBoard.elements.length} elements with IDs:`, 
                   currentBoard.elements.map(e => e.id).join(', '));
        
        if (!itemExists) {
          console.warn(`⚠️ Cannot update content for item ${id} - not found in current board state`);
          return currentBoard; // Return unchanged
        }
        
        // Update found, updating content
        console.log(`📋 Directly updating item content for ${id}`, content);
        return {
          ...currentBoard,
          elements: currentBoard.elements.map(item => 
            item.id === id 
              ? { ...item, content } 
              : item
          )
        };
      });
    });

    // --- Connection handlers ---
    onBroadcast('conn_added', ({ connection }) => {
      console.log(`📥 Received realtime board update: conn_added`, connection);
      // Insert the received connection directly to preserve original ID
      setBoard(curr => ({
        ...curr,
        connections: [...curr.connections, connection]
      }));
    });

    onBroadcast('conn_deleted', ({ id }) => {
      console.log(`📥 Received realtime board update: conn_deleted`, { id });
      setBoard(curr => ({
        ...curr,
        connections: curr.connections.filter(conn => conn.id !== id)
      }));
    });

    // Batch connections
    onBroadcast('conns_batch_added', ({ connections: conns }) => {
      console.log(`📥 Received realtime board update: conns_batch_added`, conns);
      if (Array.isArray(conns) && conns.length) {
        initialAddBatchConnections(
          conns.map((c: any) => ({ fromId: c.fromId, toId: c.toId, label: c.label })),
          { skipSync: true }
        );
      }
    });

    // --- Stroke handlers ---
    onBroadcast('stroke_added', ({ stroke }) => {
      console.log(`📥 Received realtime board update: stroke_added`, stroke);
      initialAddStroke(stroke, { skipSync: true });
    });

    onBroadcast('stroke_erased', ({ point, radius }) => {
      console.log(`📥 Received realtime board update: stroke_erased`, { point, radius });
      initialRemoveStrokesNearPoint(point, radius, { skipSync: true });
    });

    // Batch items
    onBroadcast('items_batch_added', ({ items }) => {
      console.log(`📥 Received realtime board update: items_batch_added`, items);
      if (Array.isArray(items) && items.length) {
        setBoard(curr => {
          // Filter out any items that already exist to avoid duplicates
          const existingIds = new Set(curr.elements.map(e => e.id));
          const newItems = items.filter((it: any) => !existingIds.has(it.id));
          return {
            ...curr,
            elements: [...curr.elements, ...newItems]
          };
        });
      }
    });

    // Handler for batch item moves
    onBroadcast('items_moved_batch', ({ items: movedItems }) => {
      console.log(`📥 Received realtime board update: items_moved_batch`, movedItems);
      if (Array.isArray(movedItems) && movedItems.length) {
        setBoard(currentBoard => {
          const updatedElements = currentBoard.elements.map(element => {
            const movedItem = movedItems.find((mi: any) => mi.id === element.id);
            if (movedItem) {
              return { ...element, position: movedItem.position };
            }
            return element;
          });
          return { ...currentBoard, elements: updatedElements };
        });
      }
    });
  }, [boardId, onBroadcast, board, setBoard, addItem, updateItem, deleteItem, updateItemPosition, updateItemContent, initialAddConnection, initialAddBatchConnections, initialRemoveStrokesNearPoint]);

  // Deactivate selection mode if connect mode is activated
  useEffect(() => {
    if (connectionsHook.connectMode) {
      setIsSelectionModeActive(false);
      setSelectedItemIds([]);
    }
  }, [connectionsHook.connectMode]);

  // Display connection status in development
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log(`🧑‍🤝‍🧑 Board realtime status: ${userCount} connected users, multiUser=${isMultiUser}`);
    }
  }, [userCount, isMultiUser]);

  // Log when board state changes for debugging content updates
  useEffect(() => {
    if (board.elements.length > 0) {
      // Only track the first element's content for logging purposes
      const firstItemWithContent = board.elements.find(item => 'content' in item);
      if (firstItemWithContent && 'content' in firstItemWithContent) {
        console.log(`📝 Board state updated. First item content sample: "${
          firstItemWithContent.content?.substring(0, 20)
        }${firstItemWithContent.content?.length > 20 ? '...' : ''}"`);
      }
    }
  }, [board]);

  /* --------------------------------------------------
   *  Cursor sync: broadcast my cursor + track remotes
   * -------------------------------------------------- */

  // Unique client identifier for this browser tab
  const clientIdRef = React.useRef<string>(uuidv4());

  // State to hold remote cursor positions
  const [remoteCursors, setRemoteCursors] = useState<Record<string, { x: number; y: number; lastSeen: number }>>({});

  // Cursor optimization refs
  const lastCursorPositionRef = useRef<{ x: number; y: number } | null>(null);
  const lastCursorSentTimeRef = useRef<number>(0);
  const cursorInactivityTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const pendingCursorUpdateRef = useRef<{ x: number; y: number } | null>(null);
  const batchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Performance tracking (optional - can be removed in production)
  const cursorUpdateCountRef = useRef<number>(0);
  const cursorSkippedCountRef = useRef<number>(0);
  const cursorBatchedCountRef = useRef<number>(0);

  // Batch cursor updates to reduce frequency
  const flushPendingCursorUpdate = useCallback(() => {
    if (pendingCursorUpdateRef.current) {
      const { x, y } = pendingCursorUpdateRef.current;

      emit('cursor_move', {
        userId: clientIdRef.current,
        x,
        y
      });

      // Update tracking refs
      lastCursorPositionRef.current = { x, y };
      lastCursorSentTimeRef.current = Date.now();
      cursorUpdateCountRef.current++;
      pendingCursorUpdateRef.current = null;

      // Clear batch timeout
      if (batchTimeoutRef.current) {
        clearTimeout(batchTimeoutRef.current);
        batchTimeoutRef.current = null;
      }
    }
  }, [emit]);

  // Optimized cursor movement with adaptive throttling, delta threshold, and batching
  const sendCursorUpdate = useCallback((boardX: number, boardY: number, isInactivityUpdate = false) => {
    const now = Date.now();
    const lastPosition = lastCursorPositionRef.current;
    const timeSinceLastSent = now - lastCursorSentTimeRef.current;

    // For inactivity updates, always send immediately to ensure final position accuracy
    if (isInactivityUpdate) {
      flushPendingCursorUpdate();
      return;
    }

    // Movement delta threshold - only send if cursor moved significantly
    const MOVEMENT_THRESHOLD = 5; // pixels
    if (lastPosition) {
      const deltaX = Math.abs(boardX - lastPosition.x);
      const deltaY = Math.abs(boardY - lastPosition.y);
      const totalDelta = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

      // If movement is too small and we sent an update recently, skip
      if (totalDelta < MOVEMENT_THRESHOLD && timeSinceLastSent < 100) {
        cursorSkippedCountRef.current++;
        return;
      }
    }

    // Adaptive throttling based on movement speed
    const minThrottleTime = lastPosition ?
      (Math.sqrt(Math.pow(boardX - lastPosition.x, 2) + Math.pow(boardY - lastPosition.y, 2)) > 20 ? 30 : 80) : 30;

    // Store pending update for batching
    pendingCursorUpdateRef.current = { x: boardX, y: boardY };

    // If enough time has passed, send immediately
    if (timeSinceLastSent >= minThrottleTime) {
      flushPendingCursorUpdate();
    } else {
      // Otherwise, batch the update
      cursorBatchedCountRef.current++;

      // Clear existing batch timeout
      if (batchTimeoutRef.current) {
        clearTimeout(batchTimeoutRef.current);
      }

      // Set new batch timeout
      batchTimeoutRef.current = setTimeout(() => {
        flushPendingCursorUpdate();
      }, Math.max(10, minThrottleTime - timeSinceLastSent)); // Minimum 10ms delay
    }

    // Log performance stats every 100 updates (optional - can be removed in production)
    if ((cursorUpdateCountRef.current + cursorBatchedCountRef.current) % 100 === 0) {
      const totalAttempts = cursorUpdateCountRef.current + cursorSkippedCountRef.current + cursorBatchedCountRef.current;
      const reductionPercentage = (((cursorSkippedCountRef.current + cursorBatchedCountRef.current) / totalAttempts) * 100).toFixed(1);
    }
  }, [emit, flushPendingCursorUpdate]);

  // Send my cursor position with optimizations
  useEffect(() => {
    if (!isMultiUser) return;

    const handleMove = (e: PointerEvent) => {
      // Convert viewport coords to board coords
      const boardX = (e.clientX - position.x) / scale;
      const boardY = (e.clientY - position.y) / scale;

      // Clear any existing inactivity timeout
      if (cursorInactivityTimeoutRef.current) {
        clearTimeout(cursorInactivityTimeoutRef.current);
      }

      // Send cursor update with optimizations
      sendCursorUpdate(boardX, boardY);

      // Set inactivity timeout - send final accurate position after movement stops
      cursorInactivityTimeoutRef.current = setTimeout(() => {
        sendCursorUpdate(boardX, boardY, true); // Mark as inactivity update
      }, 200); // Wait 200ms after last movement
    };

    // Handle cursor leaving the window
    const handlePointerLeave = () => {
      // Clear inactivity timeout
      if (cursorInactivityTimeoutRef.current) {
        clearTimeout(cursorInactivityTimeoutRef.current);
      }

      // Flush any pending updates
      flushPendingCursorUpdate();

      // Send cursor leave event (optional - could be used to hide cursor)
      emit('cursor_leave', { userId: clientIdRef.current });
    };

    window.addEventListener('pointermove', handleMove);
    window.addEventListener('pointerleave', handlePointerLeave);
    document.addEventListener('mouseleave', handlePointerLeave); // Fallback for mouse leave

    return () => {
      window.removeEventListener('pointermove', handleMove);
      window.removeEventListener('pointerleave', handlePointerLeave);
      document.removeEventListener('mouseleave', handlePointerLeave);

      // Clean up timeouts
      if (cursorInactivityTimeoutRef.current) {
        clearTimeout(cursorInactivityTimeoutRef.current);
      }
      if (batchTimeoutRef.current) {
        clearTimeout(batchTimeoutRef.current);
      }
    };
  }, [isMultiUser, position.x, position.y, scale, sendCursorUpdate, emit, flushPendingCursorUpdate]);

  // Receive others' cursor positions
  useEffect(() => {
    onBroadcast('cursor_move', ({ userId, x, y }) => {
      if (!userId || userId === clientIdRef.current) return; // ignore self
      setRemoteCursors(prev => ({ ...prev, [userId]: { x, y, lastSeen: Date.now() } }));
    });
  }, [onBroadcast]);

  // Periodically prune stale cursors (older than 5s)
  useEffect(() => {
    const STALE_MS = 5000;
    const interval = setInterval(() => {
      setRemoteCursors(prev => {
        const now = Date.now();
        const updated: typeof prev = {};
        Object.entries(prev).forEach(([id, data]) => {
          if (now - data.lastSeen < STALE_MS) {
            updated[id] = data;
          }
        });
        return updated;
      });
    }, 2000); // prune every 2s

    return () => clearInterval(interval);
  }, []);

  // Create the context value object
  const contextValue: BoardContextValue = {
    // Board state
    board,
    boardId,
    setBoard,
    
    // View state
    scale,
    position,
    isGrabbing,
    selectedItemId: viewState.selectedItemId,
    presentationMode,
    togglePresentationMode,
    setScale,
    setPosition,
    setScaleAndPosition,
    
    // Item operations
    addItem: wrappedAddItem,
    addBatchItems: wrappedAddBatchItems,
    updateItem: wrappedUpdateItem,
    deleteItem: wrappedDeleteItem,
    updateItemPosition: wrappedUpdateItemPosition,
    updateItemContent: wrappedUpdateItemContent,
    selectItem,
    saveItemToServer,
    
    // Zoom and pan operations
    handleWheel,
    handleCanvasPointerDown,
    resetView,
    boardDimensions,
    
    // Connection operations
    connections: connectionsHook.connections,
    connectMode: connectionsHook.connectMode,
    connectStart: connectionsHook.connectStart,
    toggleConnectMode: mainToggleConnectMode,
    startConnection: connectionsHook.startConnection,
    completeConnection: connectionsHook.completeConnection,
    cancelConnection: connectionsHook.cancelConnection,
    deleteConnection: wrappedDeleteConnection,
    addConnection: wrappedAddConnection,
    addBatchConnections: wrappedAddBatchConnections,
    
    // Modal operations
    modalState,
    openArticleForm,
    closeArticleForm,
    openSaveModal,
    closeSaveModal,
    openExitConfirmation,
    closeExitConfirmation,
    openImageUploadModal,
    closeImageUploadModal,
    setArticleFormData,
    handleArticleFormSubmit,
    handleSaveFormSubmit: modalHandleSaveFormSubmit,
    
    // Save operations
    saveBoard,
    isSaving,
    lastSaved,
    debouncedSaveBoardState,
    removeStrokesNearPoint: wrappedRemoveStrokesNearPoint,

    // Pen tool operations
    isPenModeActive,
    togglePenMode,
    addStroke: wrappedAddStroke,
    isErasing,
    toggleEraserMode,

    // Realtime
    userCount,
    isMultiUser,
    broadcast,
    onBroadcast,

    // Remote cursors
    remoteCursors,

    // Selection Mode
    isSelectionModeActive,
    selectedItemIds,
    toggleSelectionMode,
    setSelectedItemIds: handleSetSelectedItemIds,
    moveSelectedItemsByDelta: wrappedMoveSelectedItemsByDelta,

    // New board creation state
    isCreatingNewBoard,
    setIsCreatingNewBoard,
  };

  return (
    <BoardContext.Provider value={contextValue}>
      {children}
    </BoardContext.Provider>
  );
};

/**
 * Custom hook to use the board context
 */
export const useBoard = (): BoardContextValue => {
  const context = useContext(BoardContext);
  
  if (context === undefined) {
    throw new Error('useBoard must be used within a BoardProvider');
  }
  
  return context;
}; 