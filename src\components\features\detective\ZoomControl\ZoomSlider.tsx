import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ZoomIn, ZoomOut, ChevronsRight, ChevronsLeft } from 'lucide-react';

interface ZoomSliderProps {
  scale: number;
  setScale: (scale: number) => void;
  minScale: number;
  maxScale: number;
  presentationMode?: boolean;
}

const ZoomSlider: React.FC<ZoomSliderProps> = ({ scale, setScale, minScale, maxScale, presentationMode }) => {
  const [isVisible, setIsVisible] = useState(true);

  const handleZoomChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newScale = parseFloat(e.target.value);
    setScale(newScale);
  };

  const zoomIn = () => {
    const nextScale = scale * 1.25;
    setScale(Math.min(nextScale, maxScale));
  };

  const zoomOut = () => {
    const nextScale = scale / 1.25;
    setScale(Math.max(nextScale, minScale));
  };

  if (presentationMode) {
    return null;
  }

  return (
    <div className="fixed top-1/2 right-4 transform -translate-y-1/2 z-50 flex items-center">
      <AnimatePresence>
        {isVisible && (
          <motion.div
            initial={{ x: 100, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            exit={{ x: 100, opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="p-2 glass-morphism rounded-xl flex flex-col items-center space-y-4"
          >
            <button onClick={zoomIn} className="p-2 text-white hover:bg-noir-50 rounded-full transition-colors" aria-label="Zoom in">
              <ZoomIn size={20} />
            </button>
            <div className="h-32 w-8 flex items-center justify-center">
              <input
                type="range"
                min={minScale}
                max={maxScale}
                step={(maxScale - minScale) / 100}
                value={scale}
                onChange={handleZoomChange}
                className="zoom-slider"
              />
            </div>
            <button onClick={zoomOut} className="p-2 text-white hover:bg-noir-50 rounded-full transition-colors" aria-label="Zoom out">
              <ZoomOut size={20} />
            </button>
          </motion.div>
        )}
      </AnimatePresence>
      <button
        onClick={() => setIsVisible(!isVisible)}
        className="ml-2 p-2 glass-morphism rounded-full text-white hover:bg-noir-50 transition-colors"
        aria-label={isVisible ? 'Hide zoom slider' : 'Show zoom slider'}
      >
        {isVisible ? <ChevronsRight size={20} /> : <ChevronsLeft size={20} />}
      </button>
    </div>
  );
};

export default ZoomSlider; 