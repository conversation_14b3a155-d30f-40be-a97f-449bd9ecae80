// src/components/features/detective/Strokes/StrokeRenderer.tsx
import React from 'react';
import { PenStroke, Position } from '../../../../types'; // Adjust path as needed

interface StrokeRendererProps {
  strokes: PenStroke[];
  currentStroke?: PenStroke | null; // <-- Add optional currentStroke prop
  hoveredStrokeId?: string | null; // <-- Add hoveredStrokeId prop
  scale?: number; // Optional: Pass scale if needed for optimization (e.g., adjusting stroke width)
}

// Helper function to convert points array to SVG path data string
const pointsToPathData = (points: Position[]): string => {
  if (points.length < 1) return '';

  let pathData = `M ${points[0].x} ${points[0].y}`;

  if (points.length < 2) {
    // Draw a tiny dot if only one point
    pathData += ` L ${points[0].x + 0.1} ${points[0].y + 0.1}`;
  } else {
    // Use L for lines between points (could use Q or C for curves later if desired)
    for (let i = 1; i < points.length; i++) {
      pathData += ` L ${points[i].x} ${points[i].y}`;
    }
  }
  return pathData;
};

const HIGHLIGHT_COLOR = '#FF6347'; // Tomato red for highlight
const HIGHLIGHT_WIDTH_INCREASE = 2; // Add 2px to width when highlighted

export const StrokeRenderer: React.FC<StrokeRendererProps> = ({ 
  strokes, 
  currentStroke, // <-- Destructure currentStroke
  hoveredStrokeId, // <-- Destructure hoveredStrokeId
  scale = 1 
}) => {
  // Render null only if there are no completed strokes AND no current stroke
  if ((!strokes || strokes.length === 0) && !currentStroke) {
    return null;
  }

  return (
    <svg
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        pointerEvents: 'none', // Make sure SVG doesn't intercept pointer events meant for the board
        overflow: 'visible', // Ensure strokes aren't clipped by SVG boundaries
      }}
    >
      <g>
        {/* Render completed strokes */}
        {strokes.map((stroke) => {
          const isHovered = stroke.id === hoveredStrokeId;
          return (
            <path
              key={stroke.id}
              d={pointsToPathData(stroke.points)}
              // Apply highlight styles if hovered
              stroke={isHovered ? HIGHLIGHT_COLOR : stroke.color}
              strokeWidth={isHovered 
                  ? (stroke.strokeWidth + HIGHLIGHT_WIDTH_INCREASE) / scale 
                  : stroke.strokeWidth / scale
              }
              fill="none"
              strokeLinecap="round" // Smooth ends
              strokeLinejoin="round" // Smooth corners
              // Optional: Add transition for smoother highlighting
              // style={{ transition: 'stroke 0.1s ease-in-out, stroke-width 0.1s ease-in-out' }}
            />
          );
        })}
        
        {/* Render the current stroke if it exists (no highlight needed) */}
        {currentStroke && (
          <path
            // Use a stable key or maybe omit key for the single current stroke
            key="current-drawing-stroke"
            d={pointsToPathData(currentStroke.points)}
            stroke={currentStroke.color}
            strokeWidth={currentStroke.strokeWidth / scale}
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
            // Optional: Add slight opacity or different styling for in-progress stroke
            // style={{ opacity: 0.8 }}
          />
        )}
      </g>
    </svg>
  );
};

export default StrokeRenderer; // Default export for lazy loading if needed
