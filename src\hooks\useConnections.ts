import { useState, useCallback, useEffect } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { Connection, UseConnectionsReturn } from '../types';
import { 
  operationHistory, 
  AddConnectionOperation, 
  DeleteConnectionOperation,
  boardService
} from '../services';

/**
 * Custom hook for managing connections between board items
 */
export const useConnections = (
  initialConnections: Connection[] = [],
  boardId?: string,
  onAddConnection?: (fromId: string, toId: string) => Connection,
  onUpdateConnections?: (connections: Connection[]) => void
): UseConnectionsReturn => {
  // Log initial connections for debugging

  // State for connections and connection mode
  const [connections, setConnections] = useState<Connection[]>(initialConnections);
  const [connectMode, setConnectMode] = useState<boolean>(false);
  const [connectStart, setConnectStart] = useState<string | null>(null);
  
  // Update connections when initialConnections change
  useEffect(() => {
    // Force update connections if initialConnections has content and current connections are empty
    // or if the initialConnections and connections arrays have different content
    if (
      (initialConnections.length > 0 && connections.length === 0) ||
      JSON.stringify(initialConnections) !== JSON.stringify(connections)
    ) {
      setConnections(initialConnections);
    }
  }, [initialConnections, connections]);

  /**
   * Toggle connection mode
   */
  const toggleConnectMode = useCallback(() => {
    setConnectMode(prev => !prev);
    setConnectStart(null);
  }, []);

  /**
   * Start creating a connection from an item
   */
  const startConnection = useCallback((itemId: string) => {
    if (connectMode) {
      setConnectStart(itemId);
    }
  }, [connectMode]);

  /**
   * Complete a connection to an item
   */
  const completeConnection = useCallback((toItemId: string) => {
    if (connectMode && connectStart && toItemId !== connectStart) {
      
      // Check if this connection already exists
      const exists = connections.some(conn => 
        (conn.fromId === connectStart && conn.toId === toItemId) || 
        (conn.fromId === toItemId && conn.toId === connectStart)
      );
      
      if (!exists) {
        // If parent component provided an addConnection callback, use it
        // This will ensure the connection is added to both the board state and connections state
        if (onAddConnection) {
          const newConnection = onAddConnection(connectStart, toItemId);
          // No need to call boardService.updateConnection as that's handled by the parent
        } else {
          // Otherwise use the old flow
          const newConnection: Connection = {
            id: uuidv4(),
            fromId: connectStart,
            toId: toItemId
          };
          
          // Create operation for history
          const operation = new AddConnectionOperation({ 
            connection: newConnection 
          });
          
          operationHistory.addOperation(operation);
          
          // Update state
          setConnections(prev => [...prev, newConnection]);
          
          // Auto-save if we have a boardId
          if (boardId && boardId !== 'new') {
            boardService.updateConnection(boardId, newConnection, 'add')
              .then(response => {
                // If server returned a new ID, update the connection in the state
                if (response.connection && response.connection.id !== newConnection.id) {
                  // Update the connection with the server-generated ID
                  const serverConnection = response.connection;

                  setConnections(prev => 
                    prev.map(conn => 
                      conn.id === newConnection.id 
                        ? { ...conn, id: serverConnection.id }
                        : conn
                    )
                  );
                }
              })
              .catch(error => console.error('Error saving connection:', error));
          }
        }
        
        // Reset connection start state
        setConnectStart(null);
        
        // Exit connection mode
        setConnectMode(false);
        
      } else {
      }
    } else {
    }
  }, [connections, connectMode, connectStart, boardId, onAddConnection]);

  /**
   * Cancel the current connection attempt
   */
  const cancelConnection = useCallback(() => {
    setConnectStart(null);
  }, []);

  /**
   * Delete a connection
   */
  const deleteConnection = useCallback((id: string, options?: { skipSync?: boolean }) => {
    const connectionToDelete = connections.find(conn => conn.id === id);
    
    if (connectionToDelete) {
      // Create operation for history
      const operation = new DeleteConnectionOperation({
        connectionId: id,
        connection: connectionToDelete
      });
      
      operationHistory.addOperation(operation);
      
      // Update state
      const updatedConnections = connections.filter(conn => conn.id !== id);
      setConnections(updatedConnections);
      
      // Notify parent about connections update if callback is provided
      if (onUpdateConnections) {
        onUpdateConnections(updatedConnections);
      }
      
      // Auto-save if we have a boardId and sync is NOT skipped
      if (!options?.skipSync && boardId && boardId !== 'new') {
        boardService.updateConnection(boardId, connectionToDelete, 'delete')
          .then(response => {
            // If the delete was successful, do nothing (already removed from state)
          })
          .catch(error => {
            console.error('Error deleting connection:', error);
            
            // If deletion failed because ID was different, try to find it by endpoints
            if (error.message && error.message.includes('not found')) {
              
              // Load the latest board data from the server to get the correct IDs
              if (boardId && boardId !== 'new') {
                boardService.getBoard(boardId)
                  .then(boardData => {
                    // Try to find a connection with matching from/to IDs
                    const serverConnection = boardData.connections.find(conn => 
                      (conn.fromId === connectionToDelete.fromId && conn.toId === connectionToDelete.toId) || 
                      (conn.fromId === connectionToDelete.toId && conn.toId === connectionToDelete.fromId)
                    );
                    
                    if (serverConnection) {
                      boardService.updateConnection(boardId, serverConnection, 'delete')
                        .then(() => console.log('Connection deleted successfully on retry'))
                        .catch(retryError => console.error('Error deleting connection on retry:', retryError));
                    }
                  })
                  .catch(loadError => console.error('Error loading board for connection retry:', loadError));
              }
            }
          });
      }
    }
  }, [connections, boardId, onUpdateConnections]);

  /**
   * Force update connections from board data
   */
  const setConnectionsFromBoard = useCallback((boardConnections: Connection[]) => {
    setConnections(boardConnections);
  }, []);

  return {
    connections,
    connectMode,
    connectStart,
    toggleConnectMode,
    startConnection,
    completeConnection,
    cancelConnection,
    deleteConnection,
    setConnectionsFromBoard
  };
}; 