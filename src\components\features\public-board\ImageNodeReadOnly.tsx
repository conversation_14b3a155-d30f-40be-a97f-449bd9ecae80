import React from 'react';
import { Position } from '@/types';
import AutoFitText from '@/components/ui/AutoFitText';

// Add the CSS styles for the Polaroid frame
const polaroidStyle = `
  @font-face {
    font-family: 'DryWhiteboardMarker';
    src: url('/fonts/DryWhiteboardMarker-Regular.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
  }

  .polaroid-frame-readonly {
    padding: 15px 15px 30px 15px;
    background-color: white;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    width: 100%;
    height: 100%;
    border-radius: 0.375rem; /* Equivalent to rounded-md */
  }
  
  .polaroid-image-container-readonly {
    border: 4px solid white;
    overflow: hidden;
    background-color: white;
    flex: none; /* Keep image aspect ratio, don't let it grow/shrink with flex */
  }
  
  .polaroid-image-readonly {
    width: 100%;
    height: auto;
    object-fit: contain;
    display: block;
  }
  
  .polaroid-text-readonly {
    margin-top: 15px;
    position: relative;
    z-index: 5;
    color: #000;
    font-weight: 500;
    flex: 1;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: 'DryWhiteboardMarker', sans-serif;
    width: 100%;
    box-sizing: border-box;
    overflow: hidden;
    min-height: 30px; /* Ensure some space for text even if short */
  }
  
  .polaroid-text-readonly > div {
    padding: 0 10px;
  }
  
  .whiteboard-text {
    font-family: 'DryWhiteboardMarker', sans-serif;
  }
`;

interface ImageNodeReadOnlyProps {
  id: string;
  content: string;
  position: Position;
  scale: number;
  width?: number;
  height?: number;
  imageUrl?: string;
  caption?: string;
  alt?: string;
}

const ImageNodeReadOnly: React.FC<ImageNodeReadOnlyProps> = ({
  position,
  width = 250,
  height = 250,
  imageUrl,
  caption,
  content,
  alt,
}) => {
  const imageSource = imageUrl;
  const imageCaption = caption || content;

  return (
    <>
      <style>{polaroidStyle}</style>
      <div
        className="absolute detective-node"
        style={{
          left: `${position.x}px`,
          top: `${position.y}px`,
          width: `${width}px`,
          height: `${height}px`,
        }}
      >
        <div className="polaroid-frame-readonly">
          {imageSource ? (
            <div className="polaroid-image-container-readonly">
              <img
                src={imageSource}
                alt={alt || "Image evidence"}
                className="polaroid-image-readonly"
              />
            </div>
          ) : (
            <div className="flex-grow relative flex items-center justify-center bg-gray-200 text-gray-500">
              No image
            </div>
          )}
          
          {imageCaption && (
            <div className="polaroid-text-readonly">
              <AutoFitText
                mode="multi"
                className="whiteboard-text"
                style={{ width: '100%', height: '100%' }}
                parentWidth={width - 30}
                parentHeight={(height / 4)}
              >
                {imageCaption}
              </AutoFitText>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default ImageNodeReadOnly; 