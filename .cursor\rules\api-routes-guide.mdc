---
description: overview of api routes
globs: 
alwaysApply: false
---
# API Routes Guide (src/app/api)

This directory contains all the backend API route handlers for the application, built using Next.js API Routes (App Router convention).

## High-Level Overview

Each subdirectory typically groups related API endpoints.

-   **`ai/`**: Handles interactions with AI services.
-   **`auth/`**: Manages user authentication API calls.
-   **`board/`**: Contains endpoints for managing detective boards, their elements, sharing, and interactions.
-   **`send-verification-email/`**: Dedicated endpoint for sending email verification links.
-   **`storage/`**: Handles file storage operations, likely interfacing with a service like Supabase Storage.

## Detailed API Routes

### AI

-   **`ai/route.ts` ([POST /api/ai](mdc:src/app/api/ai/route.ts))**: Likely processes requests related to AI features, possibly for analyzing board content or providing suggestions.

### Authentication (`auth/`)

-   **`check-exists/route.ts` ([POST /api/auth/check-exists](mdc:src/app/api/auth/check-exists/route.ts))**: Checks if a given email or username already exists in the system during sign-up.
-   **`resend-code/route.ts` ([POST /api/auth/resend-code](mdc:src/app/api/auth/resend-code/route.ts))**: Resends the email verification code to a user.
-   **`signin/route.ts` ([POST /api/auth/signin](mdc:src/app/api/auth/signin/route.ts))**: Handles user login attempts.
-   **`signout/route.ts` ([POST /api/auth/signout](mdc:src/app/api/auth/signout/route.ts))**: Handles user logout.
-   **`signup/route.ts` ([POST /api/auth/signup](mdc:src/app/api/auth/signup/route.ts))**: Handles new user registration.
-   **`verify/route.ts` ([POST /api/auth/verify](mdc:src/app/api/auth/verify/route.ts))**: Verifies a user's email address using a provided code.

### Board (`board/`)

-   **`[id]/route.ts` ([GET /api/board/:id](mdc:src/app/api/board/%5Bid%5D/route.ts))**: Fetches the data for a specific board by its ID.
-   **`comment/route.ts` ([POST, GET, PUT, DELETE /api/board/comment](mdc:src/app/api/board/comment/route.ts))**: Handles CRUD operations for comments on boards or board items.
-   **`comment/reaction/route.ts` ([POST, DELETE /api/board/comment/reaction](mdc:src/app/api/board/comment/reaction/route.ts))**: Adds or removes reactions to comments.
-   **`comment/replies/route.ts` ([GET /api/board/comment/replies](mdc:src/app/api/board/comment/replies/route.ts))**: Fetches replies to a specific comment.
-   **`delete/route.ts` ([POST /api/board/delete](mdc:src/app/api/board/delete/route.ts))**: Deletes a specific board.
-   **`extract-article/route.ts` ([POST /api/board/extract-article](mdc:src/app/api/board/extract-article/route.ts))**: Extracts content from a given article URL to populate an article item on the board.
-   **`get-shared-boards/route.ts` ([GET /api/board/get-shared-boards](mdc:src/app/api/board/get-shared-boards/route.ts))**: Fetches a list of boards shared with the current user.
-   **`like/route.ts` ([POST /api/board/like](mdc:src/app/api/board/like/route.ts))**: Allows a user to like or unlike a public board.
-   **`make-public/route.ts` ([POST /api/board/make-public](mdc:src/app/api/board/make-public/route.ts))**: Toggles the public visibility of a board.
-   **`preview/route.ts` ([POST /api/board/preview](mdc:src/app/api/board/preview/route.ts))**: Generates or updates the preview image/data for a board.
-   **`public/route.ts` ([GET /api/board/public](mdc:src/app/api/board/public/route.ts))**: Fetches data for a public board (likely a simplified or read-only version).
-   **`save/route.ts` ([POST /api/board/save](mdc:src/app/api/board/save/route.ts))**: Saves the state of a board (handles both creation and updates).
-   **`share-board/route.ts` ([POST /api/board/share-board](mdc:src/app/api/board/share-board/route.ts))**: Manages sharing settings for a board (e.g., sharing with specific users).
-   **`update-connection/route.ts` ([POST /api/board/update-connection](mdc:src/app/api/board/update-connection/route.ts))**: Handles adding, updating, or deleting connections between board items.
-   **`update-elements/route.ts` ([POST /api/board/update-elements](mdc:src/app/api/board/update-elements/route.ts))**: Handles adding, updating, or deleting items (elements) on the board, potentially supporting batch operations.
-   **`user-boards/route.ts` ([GET /api/board/user-boards](mdc:src/app/api/board/user-boards/route.ts))**: Fetches a list of all boards owned by the current user.
-   **`view/route.ts` ([POST /api/board/view](mdc:src/app/api/board/view/route.ts))**: Records a view count increment for a board.

### Email Verification

-   **`send-verification-email/route.ts` ([POST /api/send-verification-email](mdc:src/app/api/send-verification-email/route.ts))**: Sends the initial email verification link, likely triggered during signup or by `resend-code`.

### Storage (`storage/`)

-   **`delete-image/route.ts` ([POST /api/storage/delete-image](mdc:src/app/api/storage/delete-image/route.ts))**: Deletes an image file from storage.
-   **`proxy-image/route.ts` ([GET /api/storage/proxy-image](mdc:src/app/api/storage/proxy-image/route.ts))**: Acts as a proxy to fetch images, potentially adding authentication or transformations.
-   **`upload/route.ts` ([POST /api/storage/upload](mdc:src/app/api/storage/upload/route.ts))**: Handles general file uploads to storage.
-   **`upload-preview/route.ts` ([POST /api/storage/upload-preview](mdc:src/app/api/storage/upload-preview/route.ts))**: Specifically handles uploading board preview images.

