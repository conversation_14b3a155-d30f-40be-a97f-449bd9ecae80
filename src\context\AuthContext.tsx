'use client';

import React, { createContext, useContext, useState, useEffect, useRef } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { getSupabaseClient, clearSupabaseClient } from '@/lib/supabase-client';
import { ProfileCache } from '@/utils/profileCache';
import type { Session, SupabaseClient } from '@supabase/supabase-js';
import type { Database } from '@/lib/database.types';

interface User {
  id: string;
  email?: string;
  username?: string;
}

interface UserProfile {
    id: string;
    username: string | null;
    is_verified: boolean;
    email?: string | null;
    created_at?: string;
    updated_at?: string;
}

interface AuthContextType {
  supabase: SupabaseClient<Database>;
  isAuthenticated: boolean;
  user: User | null;
  session: Session | null;
  profile: UserProfile | null;
  isUsernameSetupRequired: boolean;
  pendingUserForSetup: { id: string; email?: string; } | null;
  signIn: (email: string, password: string) => Promise<any>;
  signUp: (email: string, username: string, password: string) => Promise<any>;
  signInWithGoogle: (idToken: string) => Promise<void>;
  signOut: () => Promise<void>;
  verifyEmail: (email: string, code: string, userId?: string) => Promise<void>;
  resendVerificationCode: (email: string, userId?: string) => Promise<void>;
  checkEmailExists: (email: string) => Promise<boolean>;
  checkUsernameExists: (username: string) => Promise<boolean>;
  completeUserProfile: (username: string) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: React.ReactNode;
  // --- Restore serverSession and serverProfile --- 
  serverSession: Session | null;
  serverProfile: UserProfile | null;
}

export function AuthProvider({ children, serverSession, serverProfile }: AuthProviderProps) {
  const [supabase] = useState(() => getSupabaseClient());
  // --- Restore state initialization using server props --- 
  const [session, setSession] = useState<Session | null>(serverSession);
  const [profile, setProfile] = useState<UserProfile | null>(serverProfile);
  const [user, setUser] = useState<User | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(
    !!serverSession && !!serverProfile?.is_verified && !!serverProfile?.username // Initial check based on props
  );
  // Don't assume username setup is required initially - start with false and only set true if needed
  const [isUsernameSetupRequired, setIsUsernameSetupRequired] = useState<boolean>(false);
  const [pendingUserForSetup, setPendingUserForSetup] = useState<{ id: string; email?: string; } | null>(null);

  // Add ref to track if we've already attempted profile verification on mount
  const hasAttemptedInitialProfileCheck = useRef(false);

  // Add cleanup for invalid tokens
  const clearInvalidTokens = () => {
    try {
      localStorage.removeItem('auth_token');
      localStorage.removeItem('auth_refresh_token');
      localStorage.removeItem('auth_user');
      console.log('[AuthContext] Cleared invalid tokens from localStorage');
    } catch (error) {
      console.warn('[AuthContext] Error clearing localStorage:', error);
    }
  };

  // Effect to check initial server-provided props for username setup requirement
  useEffect(() => {
    // Only run this check once on initial mount and if we haven't already attempted it
    const isFirstMount = !profile && !!serverSession?.user;
    
    if (isFirstMount && !hasAttemptedInitialProfileCheck.current && (!serverProfile || !serverProfile.username || !serverProfile.is_verified)) {
      hasAttemptedInitialProfileCheck.current = true; // Mark as attempted to prevent duplicate calls
      console.log("[AuthProvider Mount Check] Initial session exists, profile potentially incomplete.");
      // Only proceed if email is available from the session user
      if (serverSession.user.email) {
        // DON'T immediately set isUsernameSetupRequired to true - this is causing the flash
        // Instead, fetch the profile first to make sure
        
        const verifyProfileExistence = async () => {
          try {
            console.log("[AuthProvider Mount Check] Verifying profile existence before showing setup modal");

            // First check if we have a cached completion status
            if (serverSession?.user?.id) {
              const cachedCompletionStatus = ProfileCache.isProfileComplete(serverSession.user.id);
              if (cachedCompletionStatus === true) {
                console.log("[AuthProvider Mount Check] Profile already complete (cached), skipping verification");
                return;
              }
            }

            const response = await fetch('/api/auth/profile');

            // We need to check if the user is actually signed out (401 is expected in this case)
            if (response.status === 401) {
              console.log("[AuthProvider Mount Check] Got 401 Unauthorized - user is likely signed out");
              // User is signed out, clear auth state to prevent showing setup modal
              setSession(null);
              setProfile(null);
              setIsUsernameSetupRequired(false);
              setPendingUserForSetup(null);
              return;
            }
            
            if (response.ok) {
              const fetchedProfile = await response.json();
              if (fetchedProfile && fetchedProfile.username) {
                console.log("[AuthProvider Mount Check] Complete profile already exists, skipping username setup.");
                setProfile(fetchedProfile);
                return;
              }
            }
            
            // Only set username setup required if we couldn't verify a profile exists
            // AND we're not getting auth errors (which would mean user is signed out)
            console.log("[AuthProvider Mount Check] No complete profile found, username setup required");
            const isOAuthUser = !!serverSession.user.app_metadata?.provider;
            setPendingUserForSetup({ id: serverSession.user.id, email: serverSession.user.email });
            setIsUsernameSetupRequired(true);
          } catch (error) {
            console.error("[AuthProvider Mount Check] Error verifying profile:", error);
          }
        };
        
        // Run the verification
        verifyProfileExistence();
      } else {
        console.warn("[AuthProvider Mount Check] User email is missing in initial session, cannot determine profile setup need.");
      }
    } else if (profile?.username && profile.is_verified) {
      // If profile exists with username at any point, make sure setup is not required
      console.log("[AuthProvider Mount Check] Complete profile already exists in state, skipping username setup.");
      setIsUsernameSetupRequired(false);
      setPendingUserForSetup(null);
    }
  // This effect should only depend on the initial server props, but we need profile for the else condition
  }, [serverSession, serverProfile, profile]);

  // useEffect for deriving user/isAuthenticated from session/profile
  useEffect(() => {
    console.log('[AuthContext] useEffect - session/profile changed:', {
      hasSession: !!session?.user,
      hasProfile: !!profile,
      profileVerified: profile?.is_verified,
      profileUsername: profile?.username
    });

    if (session?.user) {
      // User is authenticated if they have a valid session
      setIsAuthenticated(true);

      if (profile && profile.is_verified && profile.username) {
        // Complete profile - set user object
        setUser({
          id: session.user.id,
          email: session.user.email,
          username: profile.username,
        });
        setIsUsernameSetupRequired(false);
        setPendingUserForSetup(null);
        console.log('[AuthContext] User fully authenticated with complete profile');
      } else if (profile && !profile.username) {
        // Incomplete profile - user needs to set username
        setUser({
          id: session.user.id,
          email: session.user.email,
          username: undefined,
        });
        console.log('[AuthContext] User authenticated but profile incomplete - username needed');
      } else {
        // No profile yet - might be loading or needs to be created
        setUser({
          id: session.user.id,
          email: session.user.email,
          username: undefined,
        });
        console.log('[AuthContext] User authenticated but no profile data yet');
      }
    } else {
      // No session - user is not authenticated
      setUser(null);
      setIsAuthenticated(false);
      setIsUsernameSetupRequired(false);
      setPendingUserForSetup(null);
      console.log('[AuthContext] No session - user not authenticated');
    }
  }, [session, profile]);

  // onAuthStateChange listener logic
  useEffect(() => {
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (_event, newSession) => {
        console.log(`[onAuthStateChange] Event: ${_event}, Session: ${newSession ? "Exists" : "Null"}, User ID: ${newSession?.user?.id}, Timestamp: ${new Date().toISOString()}`);

        if (_event === 'SIGNED_OUT') {
          console.log("[onAuthStateChange] Handling SIGNED_OUT. Clearing all auth state.");
          clearInvalidTokens(); // Clear any stale tokens
          clearSupabaseClient(); // Clear the singleton client
          ProfileCache.clearAllCache(); // Clear profile cache
          setSession(null);
          setProfile(null);
          setUser(null);
          setIsAuthenticated(false);
          setIsUsernameSetupRequired(false);
          setPendingUserForSetup(null);
          return;
        }

        // Handle token refresh errors by clearing invalid tokens
        if (_event === 'TOKEN_REFRESHED' && !newSession) {
          console.warn("[onAuthStateChange] Token refresh failed, clearing invalid tokens");
          clearInvalidTokens();
          clearSupabaseClient();
          setSession(null);
          setProfile(null);
          setUser(null);
          setIsAuthenticated(false);
          setIsUsernameSetupRequired(false);
          setPendingUserForSetup(null);
          return;
        }

        // Check if session is actually valid before proceeding with profile fetching
        if (!newSession) {
          console.log("[onAuthStateChange] No session detected, clearing auth state.");
          setSession(null);
          setProfile(null);
          setUser(null);
          setIsAuthenticated(false);
          setIsUsernameSetupRequired(false);
          setPendingUserForSetup(null);
          return;
        }

        if (newSession && newSession.user && (_event === 'SIGNED_IN' || _event === 'TOKEN_REFRESHED' || _event === 'INITIAL_SESSION')) {
          console.log(`[onAuthStateChange] Handling ${_event} for user ${newSession.user.id}. Setting session.`);

          // Check for account switching
          const previousUserId = session?.user?.id;
          const newUserId = newSession.user.id;

          if (previousUserId && previousUserId !== newUserId) {
            console.log(`[onAuthStateChange] Account switch detected: ${previousUserId} -> ${newUserId}`);
            ProfileCache.handleAccountSwitch(newUserId, previousUserId);

            // Clear current profile state immediately to prevent showing wrong user's data
            setProfile(null);
            setUser(null);
            setIsAuthenticated(false);
            setIsUsernameSetupRequired(false);
            setPendingUserForSetup(null);

            // Force refresh the session to ensure cookies are updated
            try {
              const supabase = getSupabaseClient();
              await supabase.auth.refreshSession();
              console.log(`[onAuthStateChange] Session refreshed for account switch`);
            } catch (refreshError) {
              console.warn(`[onAuthStateChange] Failed to refresh session:`, refreshError);
            }

            // Add a longer delay to allow session cookies to sync
            await new Promise(resolve => setTimeout(resolve, 1000));
          }

          setSession(newSession); // Set session immediately

          console.log(`[onAuthStateChange] PRE-PROFILE FETCH for user ${newSession.user.id}. Current profile state:`, profile);
          try {
            let fetchedProfile: UserProfile | null = null;
            let profileError: { message: string; code?: string | number; details?: string; hint?: string } | null = null;
            const PROFILE_FETCH_TIMEOUT_MS = 5000;

            try {
                console.log(`[onAuthStateChange] User ${newSession.user.id}: Attempting profile fetch with caching...`);

                // First try to get from cache
                const cachedProfile = ProfileCache.getCachedProfile(newSession.user.id);
                if (cachedProfile && cachedProfile.id === newSession.user.id) {
                    console.log(`[onAuthStateChange] Using cached profile for user ${newSession.user.id}`);
                    fetchedProfile = cachedProfile;
                    profileError = null;
                } else {
                    if (cachedProfile && cachedProfile.id !== newSession.user.id) {
                        console.warn(`[onAuthStateChange] Cached profile ID mismatch! Expected: ${newSession.user.id}, Got: ${cachedProfile.id}. Clearing all cache.`);
                        // Clear all cache to prevent any cross-contamination
                        ProfileCache.clearAllCache();
                    }
                    console.log(`[onAuthStateChange] No cached profile, fetching from API with timeout (${PROFILE_FETCH_TIMEOUT_MS}ms)...`);

                    const apiCall = async (): Promise<UserProfile> => {
                        return await ProfileCache.fetchProfile(newSession.user.id);
                    };

                    const timeoutPromise = new Promise<never>((_, reject) =>
                        setTimeout(() => reject(new Error(`Profile fetch timed out after ${PROFILE_FETCH_TIMEOUT_MS}ms`)), PROFILE_FETCH_TIMEOUT_MS)
                    );

                    fetchedProfile = await Promise.race([apiCall(), timeoutPromise]);
                    profileError = null; // Success
                }

            } catch (err: any) {
                console.warn(`[onAuthStateChange] Profile fetch via API for user ${newSession.user.id} failed or timed out: ${err.message}`);
                if (err.message && err.message.includes("Profile fetch timed out")) {
                    profileError = { message: err.message, code: 'PROFILE_FETCH_TIMEOUT_OR_ERROR' };
                } else if (err.message && err.message.includes("Profile ID mismatch")) {
                    // Handle profile ID mismatch as a special case - not a critical error
                    console.log(`[onAuthStateChange] Profile ID mismatch handled, treating as profile not found for user ${newSession.user.id}`);
                    profileError = { message: "Profile not found for current user", code: 404 };
                } else {
                    profileError = { message: err.message || "Unknown API fetch error", code: err.code || 'PROFILE_API_FETCH_ERROR', details: err.details };
                }
                fetchedProfile = null;
            }
            
            console.log(`[onAuthStateChange] User ${newSession.user.id}: Profile API call processing FINISHED. Error: ${profileError ? `{ code: '${profileError.code}', message: '${profileError.message}' }` : 'null'}, FetchedProfile: ${fetchedProfile ? 'exists' : 'null'}`);
            
            console.log('[onAuthStateChange] Profile fetch raw result (after API call/race):', { fetchedProfile, profileError: profileError ? { message: profileError.message, code: profileError.code, details: profileError.details, hint: profileError.hint } : null });

            // Updated error handling logic:
            const isCriticalError = profileError &&
                                    profileError.code !== 404 && // 404 is "Not Found" from API (like PGRST116)
                                    profileError.code !== 'PROFILE_FETCH_TIMEOUT_OR_ERROR' &&
                                    !profileError.message?.includes('Profile ID mismatch'); // Profile ID mismatch is not critical

            if (isCriticalError) {
              console.error("[onAuthStateChange] Error fetching profile (NOT 404 or TIMEOUT):", profileError?.message, profileError);
              setProfile(null);
              console.log("[onAuthStateChange] setProfile(null) due to critical fetch error.");
              // Potentially set isUsernameSetupRequired to true here if it's an OAuth user,
              // as they might need a profile created. But be cautious if the error means something more severe.
            } else if (fetchedProfile) {
              console.log("[onAuthStateChange] Profile fetched successfully via API:", fetchedProfile);
              setProfile(fetchedProfile);
              console.log("[onAuthStateChange] setProfile(fetchedProfile) called.");

              if (!fetchedProfile.username) {
                console.log("[onAuthStateChange] Profile exists but USERNAME IS MISSING. Setting up for username completion.");
                
                // Add a small delay before showing the username setup modal
                setTimeout(() => {
                  // Double-check if profile has been updated in the meantime
                  if (!profile?.username) {
                    setPendingUserForSetup({ id: newSession.user.id, email: newSession.user.email || fetchedProfile.email || undefined });
                    setIsUsernameSetupRequired(true);
                    console.log("[onAuthStateChange] Username setup states updated (isUsernameSetupRequired=true).");
                  } else {
                    console.log("[onAuthStateChange] Profile was updated with username after check, setup not required.");
                  }
                }, 500);
              } else {
                console.log("[onAuthStateChange] Profile has username. Username setup not required by this path.");
              }
            } else { // This case implies (profileError.code === 404) OR (profileError.code === 'PROFILE_FETCH_TIMEOUT_OR_ERROR') OR (fetchedProfile is null due to some non-critical handled error)
              const notFoundOrTimeoutMessage = profileError ? `(details: ${profileError.message})` : '(no profile data returned from API)';
              console.log(`[onAuthStateChange] No profile found or fetch issue for user ${newSession.user.id} ${notFoundOrTimeoutMessage}.`);

              if (profile && profileError && profileError.code === 'PROFILE_FETCH_TIMEOUT_OR_ERROR') {
                console.warn(`[onAuthStateChange] Profile fetch timed out during ${_event} for user ${newSession.user.id}, but an existing profile for user ID ${profile.id} (username: ${profile.username}) will be preserved. Error: ${profileError.message}`);
              } else {
                setProfile(null);
                const reason = profileError?.code === 404 ? "profile not found (404 from API)" :
                               profileError?.code === 'PROFILE_FETCH_TIMEOUT_OR_ERROR' ? "fetch timed out without pre-existing profile" :
                               "profile fetch issue (API)";
                console.log(`[onAuthStateChange] setProfile(null) because: ${reason}.`);
                
                const isOAuthUser = !!newSession.user.app_metadata?.provider;
                if (_event === 'SIGNED_IN' || isOAuthUser || (profileError && (profileError.code === 404 || profileError.code === 'PROFILE_FETCH_TIMEOUT_OR_ERROR'))) {
                   console.log(`[onAuthStateChange] Event is ${_event} and/or isOAuthUser is ${isOAuthUser} (or profile not found/timeout where state wasn't preserved). Setting up for username completion.`);
                 
                  // Add a small delay before showing username setup modal to prevent flashing
                  // This gives time for any parallel profile requests to complete first
                  setTimeout(() => {
                    // Re-check if profile exists before showing modal (might be set by another async call)
                    if (!profile?.username) {
                      setPendingUserForSetup({ id: newSession.user.id, email: newSession.user.email || undefined });
                      setIsUsernameSetupRequired(true);
                      console.log("[onAuthStateChange] Username setup states updated for new/profile-less user (isUsernameSetupRequired=true).");
                    } else {
                      console.log("[onAuthStateChange] Profile was found after timeout, username setup not required.");
                    }
                  }, 500);
                } else {
                  console.log(`[onAuthStateChange] Event is ${_event}. Profile not found/fetch issue (API), but not automatically triggering username setup for this event type under current conditions.`);
                }
              }
            }
          } catch (e: any) {
            console.error(`[onAuthStateChange] CATCH BLOCK for profile fetch for user ${newSession.user.id} at ${new Date().toISOString()}: Message: ${e.message}, Stack: ${e.stack}`, e);
            setProfile(null); // Reset profile on any unexpected error during the process
            console.log("[onAuthStateChange] setProfile(null) due to CATCH BLOCK exception.");
            // Decide if username setup should be forced here. If it's an OAuth user, probably.
            const isOAuthUser = !!newSession.user.app_metadata?.provider;
            if (isOAuthUser) {
                console.log("[onAuthStateChange] CATCH BLOCK: Is OAuth user, setting up for username completion as a fallback.");
                setPendingUserForSetup({ id: newSession.user.id, email: newSession.user.email || undefined });
                setIsUsernameSetupRequired(true);
            }
          }
          console.log(`[onAuthStateChange] POST-PROFILE FETCH for user ${newSession.user.id}. Current profile state:`, profile, `isUsernameSetupRequired: ${isUsernameSetupRequired}`);
        } else if (!newSession && _event !== 'USER_UPDATED' && _event !== 'PASSWORD_RECOVERY') {
          console.log(`[onAuthStateChange] Event ${_event} resulted in null session. Clearing auth state.`);
          setSession(null);
          setProfile(null);
          setUser(null);
          setIsAuthenticated(false);
          setIsUsernameSetupRequired(false);
          setPendingUserForSetup(null);
        } else {
          console.log(`[onAuthStateChange] Unhandled event/session combination: Event: ${_event}, HasSession: ${!!newSession}`);
        }
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, [supabase]); // Simplified dependencies, as serverSession/serverProfile are for initial mount only.

  const signIn = async (email: string, password: string) => {
    try {
      console.log("Starting sign in request (via custom API)");
      const response = await fetch('/api/auth/signin', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ login: email, password }),
      });
      const data = await response.json();
      
      console.log("Sign in API response:", JSON.stringify({
        status: response.status,
        ok: response.ok,
        hasSession: !!data.session,
        hasUserId: !!data.userId,
        needsVerification: !!data.needsVerification
      }));

      if (!response.ok) {
        throw new Error(data.error || `Sign in failed with status: ${response.status}`);
      }

      if (data.needsVerification) {
          console.log("Sign in API indicates verification needed.");
          return data;
      }

      console.log("Sign in API call successful, updating state directly...");

      // The most reliable approach - force session refresh
      if (data.session) {
          try {
              console.log("Forcing session refresh with Supabase client directly");
              
              // Create a timeout to ensure we don't hang forever
              const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error("setSession timeout after 1000ms")), 1000);
              });
              
              // Try setting the session with a timeout safeguard
              try {
                await Promise.race([
                  supabase.auth.setSession({
                    access_token: data.session.access_token,
                    refresh_token: data.session.refresh_token
                  }),
                  timeoutPromise
                ]);
              } catch (timeoutErr) {
                console.warn("Session refresh timed out:", timeoutErr);
              }
              
              console.log("Session setup attempted, forcing reload anyway");
              // Force reload regardless of success
              window.location.reload();
              
              return data;
          } catch (sessionError) {
              console.error("Error setting session directly:", sessionError);
              // Continue to fallback approach
          }
      }

      // Fall back to manual state updates if setSession didn't work
      if (data.session) {
          // Set session state first
          console.log("Falling back to manual state updates");
          setSession(data.session);

          if (data.userId) {
               console.log("Fetching profile data for userId:", data.userId, "using cached profile fetch");
               let profileData: UserProfile | null = null;
               let error: any = null;

               try {
                 profileData = await ProfileCache.fetchProfile(data.userId);
               } catch (e) {
                 error = e;
               }

               console.log("Profile fetch result from API:", JSON.stringify({
                 success: !!profileData,
                 hasProfile: !!profileData,
                 isVerified: profileData?.is_verified
               }));
               
               if (!error && profileData?.is_verified) {
                  // Update all states in a predictable order
                  console.log("Profile verified, updating all auth states...");
                  
                  // Important: Update all states at once to ensure consistency
                  setUser({
                    id: data.userId,
                    email: data.email || data.session.user.email,
                    username: profileData.username ?? undefined,
                  });
                  setIsAuthenticated(true);
                  setProfile(profileData);
                  
                  // Ensure state updates are processed before returning
                  console.log("Auth state updates complete, isAuthenticated =", true);
                  
                  console.log("About to trigger page reload for auth state refresh");
                  // Force a window reload to ensure auth state is updated app-wide
                  window.location.reload();
                  
                  // Force a small delay to ensure state propagation
                  await new Promise(resolve => setTimeout(resolve, 1000));
               } else {
                  console.error("Error pre-fetching profile after sign in or profile not verified:", error);
                  if (profileData) {
                    console.log("Profile data exists but verification status is:", profileData.is_verified);
                  }
                  setSession(null);
                  setUser(null);
                  setIsAuthenticated(false);
               }
          } else {
             console.warn("Sign in API response successful but missing userId for profile fetch.");
             setSession(null);
          }
      } else {
          console.warn("Sign in API response successful but missing session data.");
          setSession(null);
      }

      return data;

    } catch (error) {
      console.error("Sign In Error (via custom API):", error);
      if (error instanceof Error) { throw error; }
      else { throw new Error('An unexpected error occurred during sign-in'); }
    }
  };

  const signInWithGoogle = async (idToken: string): Promise<void> => {
    console.log("[AuthContext] Attempting Google sign-in with ID token.");
    if (!idToken) {
      console.error("[AuthContext] signInWithGoogle called without an ID token.");
      throw new Error("Google sign-in failed: No ID token provided.");
    }

    return new Promise<void>((resolve, reject) => {
      let authListener: any = null;
      let timeoutId: NodeJS.Timeout | null = null;
      let resolved = false;

      const cleanup = () => {
        if (authListener) {
          authListener.unsubscribe();
          authListener = null;
        }
        if (timeoutId) {
          clearTimeout(timeoutId);
          timeoutId = null;
        }
      };

      const resolveOnce = (userId: string) => {
        if (!resolved) {
          resolved = true;
          console.log(`[AuthContext] Google sign-in successful for user: ${userId} - resolving promise...`);
          cleanup();
          console.log(`[AuthContext] Cleanup completed, calling resolve()`);
          resolve();
          console.log(`[AuthContext] resolve() called successfully`);
        } else {
          console.log(`[AuthContext] resolveOnce called but already resolved for user: ${userId}`);
        }
      };

      const rejectOnce = (error: Error) => {
        if (!resolved) {
          resolved = true;
          console.log(`[AuthContext] Google sign-in failed: ${error.message}`);
          cleanup();
          reject(error);
        }
      };

      // Set up timeout with fallback reload
      timeoutId = setTimeout(() => {
        console.log("[AuthContext] Timeout fired. Resolved status:", resolved);

        // Always check session regardless of resolved status
        // This handles the case where promise resolved but UI is still stuck
        console.log("[AuthContext] Checking session after timeout...");

        // Check one more time if auth actually succeeded
        console.log("[AuthContext] About to call supabase.auth.getSession()...");

        const sessionCheckPromise = supabase.auth.getSession();
        console.log("[AuthContext] getSession() promise created, waiting for result...");

        sessionCheckPromise.then(({ data: { session } }) => {
          console.log(`[AuthContext] Session check completed! User ID = ${session?.user?.id}`);

          if (session?.user) {
            console.log("[AuthContext] Session exists after timeout, reloading page to complete sign-in");
            // Authentication succeeded but promise didn't resolve - reload to fix state
            window.location.reload();
          } else {
            console.log("[AuthContext] No session found after timeout, treating as failure");
            if (!resolved) {
              rejectOnce(new Error('Google sign-in timed out after 10 seconds'));
            }
          }
        }).catch((error) => {
          console.log("[AuthContext] Error checking session after timeout:", error);
          if (!resolved) {
            rejectOnce(new Error('Google sign-in timed out after 10 seconds'));
          }
        });

        // Add a backup timeout for the session check itself
        setTimeout(() => {
          console.log("[AuthContext] Session check is taking too long, forcing reload anyway...");
          window.location.reload();
        }, 2000);
      }, 10000);

      // Set up auth listener BEFORE starting the sign-in
      console.log("[AuthContext] Setting up auth listener for Google sign-in...");
      authListener = supabase.auth.onAuthStateChange((event, session) => {
        console.log(`[AuthContext] Google sign-in auth event: ${event}, User: ${session?.user?.id}`);

        if (event === 'SIGNED_IN' && session?.user) {
          resolveOnce(session.user.id);
        }
      });

      // Start the sign-in process
      console.log("[AuthContext] Starting signInWithIdToken...");
      supabase.auth.signInWithIdToken({
        provider: 'google',
        token: idToken,
      }).then((result) => {
        console.log("[AuthContext] signInWithIdToken completed:", {
          hasData: !!result.data,
          hasError: !!result.error
        });

        if (result.error) {
          rejectOnce(new Error(result.error.message || 'Failed to sign in with Google'));
        }
      }).catch((error) => {
        console.log("[AuthContext] signInWithIdToken error:", error.message);
        // Don't reject immediately - the auth event might still fire
      });
    });
  };

  const signUp = async (email: string, username: string, password: string) => {
    try {
        console.log("Starting sign up request (via custom API)");
        const response = await fetch('/api/auth/signup', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ email, username, password }),
        });
        const data = await response.json();
        if (!response.ok) {
            throw new Error(data.message || 'Failed to sign up via API');
        }
        console.log("Sign up API call successful, potentially waiting for auth state change...");
        return data;
      } catch (error: any) {
        console.error('Custom Sign up Error:', error);
        throw error;
      }
  };

  const signOut = async () => {
    try {
        console.log("[AuthContext SignOut] Calling API route /api/auth/signout");

        // Immediately clear client-side state and tokens to prevent UI flicker
        // and ensure modals don't show during sign out
        setIsUsernameSetupRequired(false);
        setPendingUserForSetup(null);
        clearInvalidTokens(); // Clear tokens immediately
        clearSupabaseClient(); // Clear the singleton client
        ProfileCache.clearAllCache(); // Clear profile cache

        // Clear all state immediately to prevent issues with immediate re-sign-in
        setSession(null);
        setProfile(null);
        setUser(null);
        setIsAuthenticated(false);

        // Reset the initial profile check flag to allow fresh checks on next sign-in
        hasAttemptedInitialProfileCheck.current = false;

        const response = await fetch('/api/auth/signout', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' }
        });

        if (!response.ok) {
            const errorData = await response.json();
            console.error("[AuthContext SignOut] API SignOut failed:", errorData);
            throw new Error(errorData.error || 'Sign out failed via API');
        }

        console.log("[AuthContext SignOut] API call successful.");
        console.log("[AuthContext SignOut] All client state cleared.");

    } catch (e) {
        console.error("[AuthContext SignOut] Error calling signout API or during signout:", e);
        // Even on error, try to clear local state to ensure modal can close
        clearInvalidTokens();
        clearSupabaseClient();
        ProfileCache.clearAllCache();
        setSession(null);
        setProfile(null);
        setUser(null);
        setIsAuthenticated(false);
        setIsUsernameSetupRequired(false);
        setPendingUserForSetup(null);
        hasAttemptedInitialProfileCheck.current = false;
    }
    console.log("[AuthContext SignOut] signOut function finished.");
  };

  const verifyEmail = async (email: string, code: string, providedUserId?: string) => {
    try {
        let userId = providedUserId || user?.id;

        if (!userId && email) {
            console.log("No userId provided or in state for verifyEmail, fetching from API");
            const userCheckResponse = await fetch('/api/auth/check-exists', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ email })
            });

            const userCheckText = await userCheckResponse.text();
            if (!userCheckText) {
                throw new Error('Server returned an empty response for user check');
            }
            let userData;
            try {
                userData = JSON.parse(userCheckText);
            } catch (error) {
                console.error('JSON parse error during user check:', error);
                throw new Error(`Failed to parse user check response: ${userCheckText.substring(0, 100)}`);
            }
            if (!userCheckResponse.ok) {
                throw new Error(userData.message || 'Failed to verify user existence.');
            }
            if (!userData.userId) {
                throw new Error('User not found via check-exists API.');
            }
            userId = userData.userId;
        }

        if (!userId) throw new Error("User ID could not be determined for verification");

        console.log("Calling verify API with userId:", userId);
        const response = await fetch('/api/auth/verify', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ userId, code }),
        });
        const data = await response.json();
        if (!response.ok) {
            throw new Error(data.error || 'Failed to verify email via API.');
        }
        console.log("Verify API call successful, waiting for auth state change...");
        return data;
      } catch (error: any) {
        console.error('Custom Email verification error:', error);
        throw error;
      }
  };

  const resendVerificationCode = async (email: string, providedUserId?: string) => {
    try {
      let userId = providedUserId || user?.id;

      if (!userId && email) {
        console.log("No userId provided or in state for resendCode, fetching from API");
        const userCheckResponse = await fetch('/api/auth/check-exists', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ email })
        });
        const userData = await userCheckResponse.json();
        if (!userData.exists) {
          throw new Error('User not found.');
        }
        userId = userData.userId;
      }

      if (!userId) {
          throw new Error('Could not determine User ID to resend code.');
      }

      console.log("Resending verification code for userId:", userId);
      
      const response = await fetch('/api/auth/resend-code', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          email, 
          userId
        })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to resend verification code.');
      }

      return data;
    } catch (error: any) {
      console.error('Resend verification code error:', error);
      throw error;
    }
  };

  const checkEmailExists = async (email: string): Promise<boolean> => {
    try {
      const response = await fetch('/api/auth/check-exists', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
        }),
      });

      const data = await response.json();
      return data.exists;
    } catch (error) {
      console.error('Error checking email:', error);
      return false;
    }
  };

  const checkUsernameExists = async (username: string): Promise<boolean> => {
    try {
      const response = await fetch('/api/auth/check-exists', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username,
        }),
      });

      const data = await response.json();
      return data.exists;
    } catch (error) {
      console.error('Error checking username:', error);
      return false;
    }
  };

  const completeUserProfile = async (username: string) => {
    // No longer needs pendingUserForSetup directly as user ID/email will be derived server-side from session.
    // However, pendingUserForSetup is still useful for controlling modal visibility and UI cues.
    if (!pendingUserForSetup) { // Basic check, though API call will rely on server session
        console.warn("[completeUserProfile] Called without pendingUserForSetup; API will rely on session.");
        // Allow to proceed, server will validate auth.
    }

    console.log(`[AuthContext] Attempting to complete profile via API for username: ${username}`);

    try {
      const response = await fetch('/api/auth/complete-profile', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username }),
      });

      const result = await response.json();

      if (!response.ok) {
        console.error("[AuthContext] API call to complete profile failed:", result);
        throw new Error(result.error || `Failed to complete profile. Status: ${response.status}`);
      }

      console.log("[AuthContext] Profile completed successfully via API:", result);

      // Clear profile cache since it's now outdated
      if (session?.user?.id) {
        ProfileCache.clearUserCache(session.user.id);
      }

      // Clear pending state and trigger auth state refresh by reloading
      setIsUsernameSetupRequired(false);
      setPendingUserForSetup(null);

      console.log("[AuthContext] Forcing page reload to reflect profile changes after API success.");
      window.location.reload(); // Simplest way to ensure all contexts re-evaluate

    } catch (error: any) {
      console.error("[AuthContext] Error calling completeUserProfile API:", error);
      // Re-throw the error so the modal can display it
      // The error from the API (result.error) should be a user-friendly message.
      throw error; 
    }
  };

  const value = {
    supabase,
    isAuthenticated,
    user,
    session,
    profile,
    isUsernameSetupRequired,
    pendingUserForSetup,
    signIn,
    signUp,
    signInWithGoogle,
    signOut,
    verifyEmail,
    resendVerificationCode,
    checkEmailExists,
    checkUsernameExists,
    completeUserProfile,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
} 