import React from 'react';
import { 
  motion, 
  useMotionValue, 
  PanInfo,
  useAnimate,
  AnimatePresence 
} from 'framer-motion';

// Add type declaration for the framer features
declare global {
  interface Window {
    __FRAMER_FEATURES__?: {
      mismatched_motion_version_warnings?: boolean;
      [key: string]: any;
    };
  }
}

// Disable version mismatch warnings 
// This helps when libraries with different versions of framer-motion are used
if (typeof window !== 'undefined') {
  window.__FRAMER_FEATURES__ = window.__FRAMER_FEATURES__ || {};
  window.__FRAMER_FEATURES__.mismatched_motion_version_warnings = false;
}

/**
 * This wrapper ensures we're using a consistent version of framer-motion throughout the app
 * Use the exports from here instead of directly importing from framer-motion
 */

// Re-export motion components
export { motion };

// Re-export hooks
export { useMotionValue, useAnimate };

// Re-export types
export type { PanInfo };

// Re-export helpers
export { AnimatePresence };

// Version display for debugging
export const FRAMER_MOTION_VERSION = '12.6.2';

/**
 * Log the current framer-motion version when the app loads
 */
(function logVersion() {
  console.log(`Using framer-motion version: ${FRAMER_MOTION_VERSION}`);
})(); 