/**
 * Types for custom hooks
 */

import { Board, BoardViewState, Position, PenStroke } from './board';
import { BoardItem } from './item';
import { Connection } from './connection';
import { ArticleFormState, ModalState } from './modals';

/**
 * Return type for useBoardState hook
 */
export interface UseBoardStateReturn {
  board: Board;
  viewState: BoardViewState;
  setViewState: (viewState: Partial<BoardViewState>) => void;
  addItem: (type: string, position?: Position, props?: Record<string, any>, options?: { skipSync?: boolean }) => Promise<BoardItem>;
  addBatchItems: (items: Array<{type: string, position?: Position, props?: Record<string, any>}>, options?: { skipSync?: boolean }) => Promise<BoardItem[]>;
  updateItem: (id: string, updates: Partial<BoardItem>, options?: { skipSync?: boolean }) => void;
  deleteItem: (id: string, options?: { skipSync?: boolean }) => void;
  updateItemPosition: (id: string, position: Position, options?: { skipSync?: boolean }) => void;
  updateItemContent: (id: string, content: string, options?: { skipSync?: boolean }) => void;
  selectItem: (id: string | null) => void;
  addConnection: (fromId: string, toId: string, options?: { skipSync?: boolean }) => Connection;
  addBatchConnections: (connections: Array<{fromId: string, toId: string, label?: string}>, options?: { skipSync?: boolean }) => Connection[];
  addStroke: (stroke: PenStroke, options?: { skipSync?: boolean }) => void;
  removeStrokesNearPoint: (point: Position, radius: number, options?: { skipSync?: boolean }) => void;
  setBoard: (board: Board | ((prevBoard: Board) => Board)) => void;
  saveItemToServer: (itemId: string, action: 'add' | 'update' | 'delete', itemToSaveOverride?: BoardItem) => Promise<any>;
  updateItemPositionByDelta: (id: string, delta: { dx: number, dy: number }, options?: { skipSync?: boolean }) => void;
}

/**
 * Return type for useZoomAndPan hook
 */
export interface UseZoomAndPanReturn {
  scale: number;
  position: Position;
  isGrabbing: boolean;
  setScale: (scale: number, pinchMidpointClient?: Position) => void;
  setPosition: (position: Position) => void;
  setScaleAndPosition: (scale: number, position: Position) => void;
  handleWheel: (e: React.WheelEvent) => void;
  handleCanvasPointerDown: (event: React.PointerEvent<Element>) => void;
  resetView: () => void;
  boardDimensions: { width: number; height: number };
}

/**
 * Return type for useConnections hook
 */
export interface UseConnectionsReturn {
  connections: Connection[];
  connectMode: boolean;
  connectStart: string | null;
  toggleConnectMode: () => void;
  startConnection: (itemId: string) => void;
  completeConnection: (itemId: string) => void;
  cancelConnection: () => void;
  deleteConnection: (id: string, options?: { skipSync?: boolean }) => void;
  setConnectionsFromBoard: (connections: Connection[]) => void;
}

/**
 * Return type for useModalState hook
 */
export interface UseModalStateReturn {
  modalState: ModalState;
  openArticleForm: (initialData?: ArticleFormState) => void;
  closeArticleForm: () => void;
  openSaveModal: () => void;
  closeSaveModal: () => void;
  openExitConfirmation: (destination: string) => void;
  closeExitConfirmation: () => void;
  openImageUploadModal: () => void;
  closeImageUploadModal: () => void;
  setArticleFormData: (data: Partial<ArticleFormState>) => void;
  handleArticleFormSubmit: (data: ArticleFormState) => ArticleFormState;
  handleSaveFormSubmit: (boardName: string) => Promise<void>;
}

// Add and export FullBoardState here
export interface FullBoardState {
  elements?: BoardItem[];
  connections?: Connection[];
  strokes: PenStroke[];
}

/**
 * Return type for usePenTool hook
 */
export interface UsePenToolReturn {
  isDrawing: boolean;
  isErasing: boolean;
  toggleEraserMode: () => void;
  currentStroke: PenStroke | null;
  penColor: string;
  penStrokeWidth: number;
  setPenColor: (color: string) => void;
  setPenStrokeWidth: (width: number) => void;
  hoveredStrokeId: string | null;
  handlePointerDown: (event: React.PointerEvent) => void;
  handlePointerMove: (event: React.PointerEvent) => void;
  handlePointerUp: (event: React.PointerEvent) => void;
}

/**
 * Return type for useAutoSave hook
 */
export interface UseAutoSaveReturn {
  saveBoard: () => Promise<string | null>;
  saveElementChange: (element: BoardItem, action: 'add' | 'update' | 'delete') => Promise<void>;
  saveConnectionChange: (connection: Connection, action: 'add' | 'update' | 'delete') => Promise<void>;
  debouncedSaveBoardState: (boardState: FullBoardState) => void;
  isSaving: boolean;
  lastSaved: Date | null;
} 