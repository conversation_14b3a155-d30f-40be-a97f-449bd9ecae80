/**
 * Core types for the Detective Board application
 */

import { BoardItem } from './item';
import { Connection } from './connection';

/**
 * Represents a position on the board
 */
export interface Position {
  x: number;
  y: number;
}

/**
 * Represents a single freehand drawing stroke on the board
 */
export interface PenStroke {
  id: string; // Unique identifier for the stroke
  points: Position[]; // Array of points defining the stroke path
  color: string; // Color of the stroke (e.g., '#000000')
  strokeWidth: number; // Width of the stroke
}

/**
 * Represents a board sharing record
 */
export interface BoardSharingRecord {
  id: string;
  board_id: string;
  public_board?: boolean; // Optional in case it's not always present
  likes?: number | null;    // Optional, can be null
  total_views?: number | null;
  created_at?: string;
  updated_at?: string;
}

/**
 * Represents a detective board with all its elements
 */
export interface Board {
  id: string;
  name: string;
  elements: BoardItem[];
  connections: Connection[];
  strokes: PenStroke[]; // Array of freehand strokes
  sharing?: BoardSharingRecord[]; // Make sharing an array of records, optional
  liked?: boolean; // Add optional liked status
  createdAt?: Date;
  updatedAt?: Date;
  userId?: string;
}

/**
 * Represents the view state of the board
 */
export interface BoardViewState {
  scale: number;
  position: Position;
  selectedItemId: string | null;
  connectMode: boolean;
  connectStart: string | null;
  isGrabbing: boolean;
  presentationMode: boolean; // When true, hide editing UI elements
}

/**
 * Different types of actions that can be performed on board elements
 */
export type ElementAction = 'add' | 'update' | 'delete';

/**
 * Response from a board save operation
 */
export interface BoardSaveResponse {
  boardId: string;
  message?: string;
  success: boolean;
}

/**
 * Payload for saving a board
 */
export interface BoardSavePayload {
  boardId?: string;
  boardName: string;
  elements?: BoardItem[];
  connections?: Connection[];
  strokes?: PenStroke[];
  sharing?: BoardSharingRecord[];
}

/**
 * Payload for updating a board element
 */
export interface ElementUpdatePayload {
  boardId: string;
  element: BoardItem;
  action: ElementAction;
}

/**
 * Payload for updating a connection
 */
export interface ConnectionUpdatePayload {
  boardId: string;
  connection: Connection;
  action: ElementAction;
} 