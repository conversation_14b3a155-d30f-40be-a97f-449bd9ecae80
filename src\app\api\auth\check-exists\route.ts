import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js'; // Use standard client
import type { Database } from '@/lib/database.types'; // Ensure this path is correct

// Initialize Supabase client - Service role might be needed if <PERSON><PERSON> restricts reads,
// but try without it first if your RLS allows checking existence.
const supabase = createClient<Database>(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY! // Use anon key for basic checks if <PERSON><PERSON> allows
);

// Define the type for the user data returned (matching public.users structure)
// Note: Excludes password
type UserResponse = {
  id: string;
  email: string;
  username: string;
  is_verified: boolean; // Column name from schema
  created_at: string;
  updated_at: string;
};

export async function POST(request: NextRequest) {
  try {
    const { email, username } = await request.json();

    // Check if either email or username is provided
    if (!email && !username) {
      return NextResponse.json({ error: 'Email or username is required' }, { status: 400 });
    }

    // --- Build the query based on provided parameters ---
    // We want to find if *any* user exists with that email OR that username.
    let query = supabase.from('users').select(
        'id, email, username, is_verified, created_at, updated_at' // Select desired columns
    );

    if (email && username) {
        // Check if EITHER the email OR the username exists
        query = query.or(`email.eq.${email},username.eq.${username}`);
    } else if (email) {
        query = query.eq('email', email);
    } else if (username) {
        query = query.eq('username', username);
    }

    // Limit to 1 as we only need to know if at least one exists
    query = query.limit(1);

    // --- Execute the query ---
    const { data, error } = await query;

    if (error) {
        console.error('Check exists error - Supabase query failed:', error);
        // Don't expose detailed error to client
        return NextResponse.json({ error: 'Failed to query user data' }, { status: 500 });
    }

    // Check if any user was found
    const userExists = data && data.length > 0;
    const foundUser = userExists ? data[0] : null;

    // --- Return the result ---
    return NextResponse.json({
      exists: userExists,
      // Return the found user data (or null) matching UserResponse type
      user: foundUser ? {
          id: foundUser.id,
          email: foundUser.email,
          username: foundUser.username,
          is_verified: foundUser.is_verified,
          created_at: foundUser.created_at,
          updated_at: foundUser.updated_at,
      } : null
    }, { status: 200 });

  } catch (error) {
    // Catch JSON parsing errors or unexpected issues
    console.error('Check exists handler error:', error);
    return NextResponse.json({ error: 'An error occurred while checking user existence' }, { status: 500 });
  }
  // No finally block needed
} 