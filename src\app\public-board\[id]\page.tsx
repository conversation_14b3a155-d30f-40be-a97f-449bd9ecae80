'use client';

import React, { useEffect } from 'react';
import { useParams } from 'next/navigation';
import MainLayout from '@/components/layout/MainLayout';
import PublicBoardView from '@/components/features/public-board/PublicBoardView';
import { BoardProvider } from '@/context/BoardContext';
import { DragProvider } from '@/context/DragContext';

export default function PublicBoardPage() {
  const params = useParams();
  const id = params.id as string;
  
  useEffect(() => {
    // Just logging the board ID for debug purposes
    if (id) {
      console.log(`Loading public board: ${id}`);
    }
  }, [id]);

  return (
    <MainLayout className="p-0">
      <DragProvider>
        <BoardProvider initialBoardName="Public Board">
          <PublicBoardView />
        </BoardProvider>
      </DragProvider>
    </MainLayout>
  );
} 