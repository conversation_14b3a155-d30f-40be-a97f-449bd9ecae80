import React, { useState, useEffect } from 'react';
import { X, Loader2 } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { SaveBoardModalProps } from '../../../../types';
import { useAuth } from '@/context/AuthContext';
import { SignInModal } from '../../auth/SignInModal';
import { SignUpModal } from '../../auth/SignUpModal';
import { VerificationModal } from '../../auth/VerificationModal';

/**
 * Modal for saving a new board or editing an existing board's name.
 */
const SaveModal: React.FC<SaveBoardModalProps> = ({
  isOpen,
  onClose,
  onSave,
  initialBoardName,
  isNewBoard // true for creating, false for editing name
}) => {
  const router = useRouter();
  const { isAuthenticated } = useAuth();
  const [boardName, setBoardName] = useState(initialBoardName);
  const [isSignInOpen, setIsSignInOpen] = useState(false);
  const [isSignUpOpen, setIsSignUpOpen] = useState(false);
  const [isVerificationOpen, setIsVerificationOpen] = useState(false);
  const [verificationEmail, setVerificationEmail] = useState('');
  const [verificationUserId, setVerificationUserId] = useState<string | undefined>(undefined);
  const [verificationSentAt, setVerificationSentAt] = useState<string | undefined>();
  const [isSaving, setIsSaving] = useState(false);
  const [wasSignInAttempted, setWasSignInAttempted] = useState(false);

  useEffect(() => {
    if (isOpen) {
      setBoardName(initialBoardName);
      setWasSignInAttempted(false); // Reset sign-in attempt flag when modal opens/reopens
    }
  }, [isOpen, initialBoardName]);

  useEffect(() => {
    console.log("SaveModal: Auth state changed - isAuthenticated:", isAuthenticated, "wasSignInAttempted:", wasSignInAttempted);
    
    if (isAuthenticated && wasSignInAttempted && isSaving) { // Ensure we proceed only if a save was pending
      console.log("SaveModal: User is now authenticated and previously attempted save, proceeding with save");
      // Directly call onSave here, as handleSubmit would re-check auth and show sign-in again
      // setIsSaving is already true
      onSave(boardName)
        .catch(error => console.error('Error saving board after auth:', error))
        .finally(() => {
          setIsSaving(false); // Ensure isSaving is reset
          // onClose(); // Optionally close modal after save, or let parent handle it
        });
      setWasSignInAttempted(false);
    }
  }, [isAuthenticated, wasSignInAttempted, onSave, boardName, isSaving]);

  const performSave = async () => {
    if (!boardName.trim()) {
      // Optionally, add a toast here for empty name
      return;
    }
    setIsSaving(true);
    try {
      await onSave(boardName);
      // onClose(); // Parent component (RecentBoards) handles closing and toast messages for updates.
                 // For new boards, the original onSave might handle routing/closing.
    } catch (error) {
      console.error('Error saving board:', error);
      // Optionally, add a toast here for save failure
    } finally {
      setIsSaving(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!isAuthenticated && isNewBoard) { // Only force sign-in flow for new boards
      setWasSignInAttempted(true);
      setIsSignInOpen(true);
      // Set isSaving to true here to indicate that a save operation is pending post-authentication
      // This helps the useEffect hook to proceed with the save once authenticated.
      setIsSaving(true); 
      return;
    }
    // For editing, or if already authenticated for new board
    performSave();
  };

  const handleVerificationNeeded = (email: string, userId?: string) => {
    setVerificationEmail(email);
    setVerificationUserId(userId);
    setVerificationSentAt(new Date().toISOString());
    setIsVerificationOpen(true);
  };

  const handleVerificationSuccess = () => {
    setIsVerificationOpen(false);
    setIsSignInOpen(false);
    setIsSignUpOpen(false);
    // After successful verification, proceed with saving if a save was pending
    if (wasSignInAttempted && isSaving) {
        performSave();
    }
    setWasSignInAttempted(false); // Reset flag
  };

  const handleSignInSuccess = () => {
    console.log("SaveModal: Sign-in success detected, wasSignInAttempted was true");
    setIsSignInOpen(false);
    // The useEffect for isAuthenticated change will handle the save if `isSaving` is true
    // setWasSignInAttempted(true); // This is already set before opening sign-in
  };

  const handleBoardNameFocus = (e: React.FocusEvent<HTMLInputElement>) => {
    e.target.select();
  };

  const handleModalClose = () => {
    if (!isSaving) { // Prevent closing if actively saving
        onClose();
    }
  };

  const handleCancelClick = () => {
    if (isNewBoard) {
        router.push('/');
    } else {
        handleModalClose(); // For editing, just close the modal
    }
  };

  if (!isOpen) return null;

  const modalTitle = isNewBoard ? 'Name Your Board' : 'Edit Board Name';
  const saveButtonText = isNewBoard ? 'Create Board' : 'Save Changes';

  return (
    <>
      <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50">
        <div className="bg-noir-100 rounded-lg w-full max-w-md p-4 shadow-xl">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-white text-lg font-medium">
              {modalTitle}
            </h2>
            {/* Show close button if not a new board OR if it's editable (which is always if it's not new) */}
            {/* The original logic was: !isNewBoard. Now, it should always be shown as onClose is always provided */}
            {/* <button 
              onClick={handleModalClose}
              className="text-white/70 hover:text-white"
              aria-label="Close"
              disabled={isSaving}
            >
              <X size={20} />
            </button> */}
          </div>
          
          <form onSubmit={handleSubmit}>
            <div className="mb-4">
              <label htmlFor="boardName" className="block text-white/70 mb-1 text-sm">
                Board Name
              </label>
              <input
                type="text"
                id="boardName"
                value={boardName}
                onChange={(e) => setBoardName(e.target.value)}
                onFocus={handleBoardNameFocus}
                className="w-full bg-noir-200 border border-noir-50 rounded p-2 text-white"
                required
                autoFocus
                disabled={isSaving}
              />
            </div>
            
            <div className="flex justify-end gap-2">
              <button
                type="button"
                onClick={handleCancelClick} // Updated cancel handler
                className="px-4 py-2 text-white/70 hover:text-white"
                disabled={isSaving}
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-noir-accent text-white rounded hover:bg-opacity-90 flex items-center justify-center"
                disabled={isSaving || !boardName.trim() || (!isNewBoard && boardName.trim() === initialBoardName)}
              >
                {isSaving ? (
                  <Loader2 className="h-5 w-5 animate-spin mr-2" />
                ) : null}
                {saveButtonText}
              </button>
            </div>
          </form>
        </div>
      </div>

      {/* Auth Modals - only relevant if isNewBoard is true and user is not authenticated */}
      {isNewBoard && (
        <>
          <SignInModal
            isOpen={isSignInOpen}
            onClose={() => setIsSignInOpen(false)}
            onSuccess={handleSignInSuccess}
            onVerificationNeeded={handleVerificationNeeded}
            onSignUpClick={() => {
              setIsSignInOpen(false);
              setIsSignUpOpen(true);
            }}
          />

          <SignUpModal
            isOpen={isSignUpOpen}
            onClose={() => setIsSignUpOpen(false)}
            onSuccess={handleSignInSuccess} // Should trigger the save via useEffect
            onSignInClick={() => {
              setIsSignUpOpen(false);
              setIsSignInOpen(true);
            }}
            onVerificationNeeded={handleVerificationNeeded}
          />

          <VerificationModal
            isOpen={isVerificationOpen}
            onClose={() => setIsVerificationOpen(false)}
            onSuccess={handleVerificationSuccess}
            email={verificationEmail}
            userId={verificationUserId}
            verificationSentAt={verificationSentAt}
          />
        </>
      )}
    </>
  );
};

export default SaveModal; 