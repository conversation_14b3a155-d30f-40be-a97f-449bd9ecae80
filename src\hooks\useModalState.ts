import { useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { 
  ArticleFormState, 
  ModalState,
  UseModalStateReturn 
} from '../types';
import { boardService } from '../services';

const defaultArticleForm: ArticleFormState = {
  title: '',
  url: '',
  content: ''
};

/**
 * Custom hook for managing modal states
 */
export const useModalState = (
  boardId?: string,
  initialBoardName: string = 'Untitled board',
  setIsCreatingNewBoard?: React.Dispatch<React.SetStateAction<boolean>>
): UseModalStateReturn => {
  const router = useRouter();
  
  // Initialize modal state
  const [modalState, setModalState] = useState<ModalState>({
    showArticleForm: false,
    showSaveModal: false,
    showExitConfirmation: false,
    showImageUploadModal: false,
    exitDestination: '/',
    articleForm: { ...defaultArticleForm }
  });
  
  // Store board name
  const [boardName, setBoardName] = useState<string>(initialBoardName);

  /**
   * Open article form modal
   */
  const openArticleForm = useCallback((initialData?: ArticleFormState) => {
    setModalState(prev => ({
      ...prev,
      showArticleForm: true,
      articleForm: initialData || { ...defaultArticleForm }
    }));
  }, []);

  /**
   * Close article form modal
   */
  const closeArticleForm = useCallback(() => {
    setModalState(prev => ({
      ...prev,
      showArticleForm: false
    }));
  }, []);

  /**
   * Open image upload modal
   */
  const openImageUploadModal = useCallback(() => {
    setModalState(prev => ({
      ...prev,
      showImageUploadModal: true
    }));
  }, []);

  /**
   * Close image upload modal
   */
  const closeImageUploadModal = useCallback(() => {
    setModalState(prev => ({
      ...prev,
      showImageUploadModal: false
    }));
  }, []);

  /**
   * Open save modal
   */
  const openSaveModal = useCallback(() => {
    setModalState(prev => ({
      ...prev,
      showSaveModal: true
    }));
  }, []);

  /**
   * Close save modal
   */
  const closeSaveModal = useCallback(() => {
    setModalState(prev => ({
      ...prev,
      showSaveModal: false
    }));
  }, []);

  /**
   * Open exit confirmation modal
   */
  const openExitConfirmation = useCallback((destination: string) => {
    setModalState(prev => ({
      ...prev,
      showExitConfirmation: true,
      exitDestination: destination
    }));
  }, []);

  /**
   * Close exit confirmation modal
   */
  const closeExitConfirmation = useCallback(() => {
    setModalState(prev => ({
      ...prev,
      showExitConfirmation: false
    }));
  }, []);

  /**
   * Update article form data
   */
  const setArticleFormData = useCallback((data: Partial<ArticleFormState>) => {
    setModalState(prev => ({
      ...prev,
      articleForm: {
        ...prev.articleForm,
        ...data
      }
    }));
  }, []);

  /**
   * Handle article form submission
   */
  const handleArticleFormSubmit = useCallback((data: ArticleFormState) => {
    // The actual handling of adding an article is expected to be
    // implemented in the component that uses this hook
    
    // Just close the form after submission
    closeArticleForm();
    
    return data;
  }, [closeArticleForm]);

  /**
   * Handle save form submission
   */
  const handleSaveFormSubmit = useCallback(async (newBoardName: string): Promise<void> => {
    if (!newBoardName.trim()) {
      toast.error('Board name cannot be empty.');
      return;
    }
    
    setBoardName(newBoardName);
    
    if (!boardId || boardId === 'new') {
      if (setIsCreatingNewBoard) {
        setIsCreatingNewBoard(true);
      }
      try {
        const result = await boardService.saveBoard({
          boardName: newBoardName
        });
        
        if (result.success && result.boardId) {
          router.push(`/board/${result.boardId}`);
        } else {
          const errorMessage = result.message || 'Failed to create board.';
          console.error('Failed to create board (API reported !success):', errorMessage);
          toast.error(errorMessage);
          if (setIsCreatingNewBoard) {
            setIsCreatingNewBoard(false);
          }
        }
      } catch (error: any) {
        const errorMessage = error.message || 'An unexpected error occurred while creating the board.';
        console.error('Error creating board (exception):', error);
        toast.error(errorMessage);
        if (setIsCreatingNewBoard) {
          setIsCreatingNewBoard(false);
        }
      } finally {
        const currentPath = window.location.pathname; 
        if (currentPath === '/board/new') {
            closeSaveModal();
            if (setIsCreatingNewBoard && (currentPath === '/board/new')) {
            }
        }
      }
    } else {
      try {
        toast.promise(
          boardService.saveBoard({
            boardId,
            boardName: newBoardName,
          }),
          {
            loading: 'Saving board name...',
            success: `Board name updated to '${newBoardName}'!`,
            error: 'Failed to update board name.',
          }
        );
      } catch (error: any) {
        console.error('Error updating board name (exception):', error);
        toast.error(error.message || 'Error updating board name.');
      } finally {
         closeSaveModal();
      }
    }
  }, [boardId, closeSaveModal, router, setIsCreatingNewBoard]);

  return {
    modalState,
    openArticleForm,
    closeArticleForm,
    openSaveModal,
    closeSaveModal,
    openExitConfirmation,
    closeExitConfirmation,
    openImageUploadModal,
    closeImageUploadModal,
    setArticleFormData,
    handleArticleFormSubmit,
    handleSaveFormSubmit
  };
}; 