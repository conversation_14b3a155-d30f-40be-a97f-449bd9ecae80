import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
// import { createClient } from '@supabase/supabase-js'; // No longer needed for admin client
import type { Database } from '@/lib/database.types';
import { randomUUID } from 'crypto';

// const supabaseAdmin = createClient<Database>(
// process.env.NEXT_PUBLIC_SUPABASE_URL!,
// process.env.SUPABASE_SERVICE_ROLE_KEY!
// );

function generateVerificationCode(): string {
    const characters = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789';
    let code = '';
    for (let i = 0; i < 6; i++) {
      code += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    return code;
}

export async function POST(request: NextRequest) {
  try {
    const { login, password } = await request.json();

    if (!login || !password) {
      return NextResponse.json({ error: 'Login and password are required' }, { status: 400 });
    }

    const cookieStore = cookies();
    // Use this client for all database operations
    const supabase = createRouteHandlerClient<Database>({ cookies: () => cookieStore });

    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: login,
      password: password,
    });

    if (authError) {
      const errorMessage = authError.message || 'Invalid credentials';
      return NextResponse.json({ error: errorMessage }, { status: 401 });
    }

    if (!authData.user) {
        console.error("Sign In Error: User data missing despite successful auth.");
        return NextResponse.json({ error: 'Authentication failed' }, { status: 500 });
    }

    const userId = authData.user.id;
    const userEmail = authData.user.email;

    const { data: userProfile, error: profileError } = await supabase // Use the route handler client
        .from('users')
        .select('is_verified')
        .eq('id', userId)
        .single();

    if (profileError || !userProfile) {
        console.error(`Sign In Error: Failed to fetch profile for user ${userId}`, profileError);
        return NextResponse.json({ error: 'Failed to retrieve user profile' }, { status: 500 });
    }

    if (userProfile.is_verified) {
        return NextResponse.json({
            message: 'Login successful',
            userId: userId,
            email: userEmail,
            session: authData.session
        }, { status: 200 });
    }

    else {
        const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000);
        let newCodeSent = false;
        let verificationCode: string;
        let sentAt = new Date();

        const { data: existingCode, error: codeCheckError } = await supabase // Use the route handler client
            .from('verification_codes')
            .select('code, sent_at')
            .eq('user_id', userId)
            .gt('sent_at', thirtyMinutesAgo.toISOString())
            .order('sent_at', { ascending: false })
            .limit(1)
            .maybeSingle();

        if (codeCheckError) {
            console.error(`Sign In Error: Failed to check verification codes for user ${userId}`, codeCheckError);
            return NextResponse.json({ error: 'Failed to check verification status' }, { status: 500 });
        }

        if (existingCode && existingCode.sent_at) {
            verificationCode = existingCode.code;
            sentAt = new Date(existingCode.sent_at);
        } else {
            verificationCode = generateVerificationCode();
            sentAt = new Date();
            const expiresAt = new Date(Date.now() + 30 * 60 * 1000);
            const verificationCodeId = randomUUID();

            await supabase.from('verification_codes').delete().eq('user_id', userId); // Use the route handler client

            const { error: insertError } = await supabase // Use the route handler client
                .from('verification_codes')
                .insert({
                    id: verificationCodeId,
                    user_id: userId,
                    code: verificationCode,
                    sent_at: sentAt.toISOString(),
                    expires_at: expiresAt.toISOString()
                });

            if (insertError) {
                console.error(`Sign In Error: Failed to insert new verification code for user ${userId}`, insertError);
                return NextResponse.json({ error: 'Failed to generate verification code' }, { status: 500 });
            }

            const origin = request.headers.get('origin') || process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
            try {
                const emailResponse = await fetch(`${origin}/api/send-verification-email`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ email: userEmail, code: verificationCode })
                });
                if (!emailResponse.ok) {
                    console.error(`Sign In Error: Failed to send verification email for user ${userId}. Status: ${emailResponse.status}`);
                } else {
                     newCodeSent = true;
                }
            } catch (emailApiError) {
                console.error(`Sign In Error: Error calling email API for user ${userId}`, emailApiError);
            }
        }

        return NextResponse.json({
          message: newCodeSent ? 'User not verified. New verification code sent.' : 'User not verified. Please enter your verification code.',
          needsVerification: true,
          newCodeSent: newCodeSent,
          userId: userId,
          verificationSentAt: sentAt.toISOString()
        }, { status: 200 });
    }

  } catch (error) {
    console.error('Signin Handler Error:', error);
    return NextResponse.json({ error: 'An error occurred during login' }, { status: 500 });
  }
} 