import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import type { Database } from '@/lib/database.types';
import { getAuthenticatedUser } from '@/utils/authUtils';

/**
 * Create a new comment
 * POST /api/board/comment
 */
export async function POST(request: NextRequest) {
  const cookieStore = cookies();
  const supabase = createRouteHandlerClient<Database>({ cookies: () => cookieStore });

  try {
    const { user, error: sessionError, supabase } = await getAuthenticatedUser();
    
    // Parse request body
    const { boardId, comment, parentCommentId } = await request.json();

    if (!boardId || !comment) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // Authentication is required for creating comments
    if (!user) {
      console.log("Authentication required: No user found in session");
      return NextResponse.json({ error: 'Authentication required to post comments' }, { status: 401 });
    }

    console.log(`Authenticated user ID: ${user.id}`);

    // Verify the board exists
    const { data: board, error: boardError } = await supabase
      .from('boards')
      .select('id')
      .eq('id', boardId)
      .single();

    if (boardError) {
      console.error('Error fetching board:', boardError);
      return NextResponse.json({ error: 'Board not found' }, { status: 404 });
    }

    // Try very specific workaround - use the create_comment RPC function first if it exists
    try {
      console.log("Attempting to use create_comment RPC function");
      const { data: rpcResult, error: rpcError } = await supabase.rpc('create_comment', {
        p_content: comment,
        p_board_id: boardId,
        p_parent_comment_id: parentCommentId
      });
      
      if (!rpcError && rpcResult) {
        // If this is an array, take the first element
        const commentData = Array.isArray(rpcResult) ? rpcResult[0] : rpcResult;
        return NextResponse.json({ success: true, comment: commentData });
      } else {
        console.log("RPC method failed:", rpcError);
      }
    } catch (rpcError) {
      console.log("RPC method threw exception:", rpcError);
      // Continue to other methods
    }

    // Try a different approach - disable RLS temporarily if you have admin access
    try {
      // Create client with anon key (publicly available in the browser)
      // This shouldn't work but might give different error info
      const anonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
      if (anonKey) {
        console.log("Trying with anon key client...");
        const anonClient = createRouteHandlerClient({ cookies: () => cookieStore }, {
          supabaseKey: anonKey,
          options: {
            global: {
              headers: {
                Authorization: `Bearer ${anonKey}`
              }
            }
          }
        });

        // Explicitly set auth using session token
        const { data: session } = await supabase.auth.getSession();
        if (session?.session?.access_token) {
          console.log("Setting auth explicitly with access token");
          anonClient.auth.setSession({
            access_token: session.session.access_token,
            refresh_token: session.session.refresh_token
          });
        }

        const newComment = {
          board_id: boardId,
          content: comment,
          user_id: user.id,
          parent_comment_id: parentCommentId || null,
          is_edited: false,
          likes: 0
        };
        
        const { data: commentData, error: commentError } = await anonClient
          .from('comments')
          .insert(newComment)
          .select()
          .single();
          
        if (!commentError) {
          console.log("Success using anon client!");
          return NextResponse.json({ success: true, comment: commentData });
        } else {
          console.log("Anon client error:", commentError);
        }
      }
    } catch (anonError) {
      console.log("Anon client approach failed:", anonError);
    }

    // If all methods failed, report detailed error info
    console.error("All approaches to create comment failed. Here's the standard approach result:");
    const newComment = {
      board_id: boardId,
      content: comment,
      user_id: user.id,
      parent_comment_id: parentCommentId || null,
      is_edited: false,
      likes: 0
    };

    const { data: commentData, error: commentError } = await supabase
      .from('comments')
      .insert(newComment)
      .select()
      .single();

    if (commentError) {
      console.error('Error creating comment:', commentError);
      
      return NextResponse.json({ 
        error: `Failed to create comment: ${commentError.message}`,
        code: commentError.code,
        details: commentError.details,
        hint: `Try checking if your RLS policies are correctly configured. Error code ${commentError.code} suggests a permissions issue.`
      }, { status: 500 });
    }

    return NextResponse.json({ success: true, comment: commentData });
  } catch (error) {
    console.error('Error creating comment:', error);
    return NextResponse.json({ 
      error: 'Failed to create comment due to server error',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}

/**
 * Update a comment
 * PATCH /api/board/comment
 */
export async function PATCH(request: NextRequest) {
  const cookieStore = cookies();
  const supabase = createRouteHandlerClient<Database>({ cookies: () => cookieStore });

  try {
    // Get authenticated user
    const { user, error: sessionError, supabase } = await getAuthenticatedUser();

    if (sessionError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { commentId, content } = await request.json();

    if (!commentId || !content) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // Verify comment exists and belongs to the user
    const { data: comment, error: commentError } = await supabase
      .from('comments')
      .select('*')
      .eq('id', commentId)
      .single();

    if (commentError) {
      console.error('Error fetching comment:', commentError);
      return NextResponse.json({ error: 'Comment not found' }, { status: 404 });
    }

    if (comment.user_id !== user.id) {
      return NextResponse.json({ error: 'You can only edit your own comments' }, { status: 403 });
    }

    // Update the comment
    const { data: updatedComment, error: updateError } = await supabase
      .from('comments')
      .update({
        content,
        is_edited: true,
        updated_at: new Date().toISOString()
      })
      .eq('id', commentId)
      .select()
      .single();

    if (updateError) {
      console.error('Error updating comment:', updateError);
      return NextResponse.json({ error: 'Failed to update comment' }, { status: 500 });
    }

    return NextResponse.json({ success: true, comment: updatedComment });
  } catch (error) {
    console.error('Error updating comment:', error);
    return NextResponse.json({ error: 'Failed to update comment' }, { status: 500 });
  }
}

/**
 * Delete a comment
 * DELETE /api/board/comment
 */
export async function DELETE(request: NextRequest) {
  const cookieStore = cookies();
  const supabase = createRouteHandlerClient<Database>({ cookies: () => cookieStore });

  try {
    // Get authenticated user
    const { user, error: sessionError, supabase } = await getAuthenticatedUser();

    if (sessionError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const commentId = searchParams.get('commentId');

    if (!commentId) {
      return NextResponse.json({ error: 'Comment ID is required' }, { status: 400 });
    }

    // Verify comment exists and belongs to the user
    const { data: comment, error: commentError } = await supabase
      .from('comments')
      .select('*')
      .eq('id', commentId)
      .single();

    if (commentError) {
      console.error('Error fetching comment:', commentError);
      return NextResponse.json({ error: 'Comment not found' }, { status: 404 });
    }

    if (comment.user_id !== user.id) {
      return NextResponse.json({ error: 'You can only delete your own comments' }, { status: 403 });
    }

    // Delete the comment
    const { error: deleteError } = await supabase
      .from('comments')
      .delete()
      .eq('id', commentId);

    if (deleteError) {
      console.error('Error deleting comment:', deleteError);
      return NextResponse.json({ error: 'Failed to delete comment' }, { status: 500 });
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting comment:', error);
    return NextResponse.json({ error: 'Failed to delete comment' }, { status: 500 });
  }
}

/**
 * Get comments for a board
 * GET /api/board/comment?boardId=123
 */
export async function GET(request: NextRequest) {
  const cookieStore = cookies();
  const supabase = createRouteHandlerClient<Database>({ cookies: () => cookieStore });

  try {
    const { searchParams } = new URL(request.url);
    const boardId = searchParams.get('boardId');

    if (!boardId) {
      return NextResponse.json({ error: 'Board ID is required' }, { status: 400 });
    }

    // Verify board exists
    const { data: board, error: boardError } = await supabase
      .from('boards')
      .select('id')
      .eq('id', boardId)
      .single();

    if (boardError) {
      console.error('Error fetching board:', boardError);
      return NextResponse.json({ error: 'Board not found' }, { status: 404 });
    }

    // Get authenticated user (optional) - Handle potential error
    const { data: authData, error: authError } = await supabase.auth.getUser();
    const user = authError ? null : authData.user; // Get user or null if error/not logged in
    const userId = user?.id; // Store user ID if available

    // If not authenticated, check if the board is public
    if (!userId) {
      const { data: sharingData, error: sharingError } = await supabase
        .from('board_sharing')
        .select('public_board')
        .eq('board_id', boardId)
        .eq('public_board', true)
        .maybeSingle(); // Use maybeSingle to handle no sharing record gracefully

      // If there's an error OR no sharing record is found OR it's not public
      if (sharingError || !sharingData?.public_board) {
         console.log("Board access denied for anonymous user.", { sharingError, sharingData });
         return NextResponse.json({ error: 'Authentication required or board is not public' }, { status: 401 });
      }
    }

    // Base select string
    let selectString = `
      id,
      content,
      created_at,
      likes,
      dislikes,
      user_id,
      board_id,
      is_edited,
      parent_comment_id,
      updated_at,
      user:users ( username ) 
    `;

    // Define the type for a comment potentially including user reaction
    // Adjust ReactionType based on your actual enum/type in database.types.ts if available
    type ReactionType = 'like' | 'dislike' | null; 
    type CommentWithDetails = Database['public']['Tables']['comments']['Row'] & {
      user: { username: string | null } | null;
      comment_reactions?: { reaction_type: ReactionType }[];
    };


    // If user is authenticated, add the join to get their reaction
    if (userId) {
      // Left join with comment_reactions, filtered by the current user's ID
      // Selects the reaction_type if a matching reaction exists for this user
      selectString += `, comment_reactions!left ( reaction_type )`;
    }

    // Build the query
    let query = supabase
      .from('comments')
      .select(selectString)
      .eq('board_id', boardId);

    // Add the user filter *to the join* if the user is authenticated
    if (userId) {
       // Filter the 'comment_reactions' part of the select
       query = query.eq('comment_reactions.user_id', userId);
    }
    
    // Add ordering
    query = query.order('created_at', { ascending: false }); // Order descending by creation date

    // Execute the query
    // Explicitly type the expected result based on the select string
    const { data: comments, error: commentsError } = await query as { data: CommentWithDetails[] | null, error: any };


    if (commentsError) {
      console.error('Error fetching comments:', commentsError);
      return NextResponse.json({ error: 'Failed to fetch comments' }, { status: 500 });
    }

    // Format comments: extract username and userVote
    const formattedComments = comments?.map(c => {
        // Assert type 'c' here to ensure it's treated as an object for spread
        const commentData = c as CommentWithDetails; 
        const reactions = commentData.comment_reactions; 
        const userVote = (userId && Array.isArray(reactions) && reactions.length > 0) 
                         ? reactions[0].reaction_type 
                         : null;

        // Now spread the asserted commentData
        return {
          ...commentData, 
          username: commentData.user?.username || 'Anonymous', // Extract username
          userVote: userVote, // Add the user's vote (like/dislike/null)
          user: undefined, // Remove the nested user object
          comment_reactions: undefined // Remove the nested reactions array
        };
    }) || [];


    return NextResponse.json({ success: true, comments: formattedComments }); // Return the enhanced comments
  } catch (error) {
    console.error('Error fetching comments:', error);
    return NextResponse.json({ error: 'Failed to fetch comments' }, { status: 500 });
  }
} 