import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import type { Database } from '@/lib/database.types';

import { getAuthenticatedUser } from '@/utils/authUtils';
/**
 * Like a board
 * POST /api/board/like
 */
export async function POST(request: NextRequest) {
  try {
  const { user, error: authError, supabase } = await getAuthenticatedUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { boardId } = await request.json();

    if (!boardId) {
      return NextResponse.json({ error: 'Missing boardId' }, { status: 400 });
    }

    // --- Insert the like action into the board_likes table ---
    const { error: insertError } = await supabase
      .from('board_likes')
      .insert({ board_id: boardId, user_id: user.id }); 

    // If insert fails, return an error
    if (insertError) {
        console.error("Failed to insert like:", insertError);
        // Provide more specific error based on insertError.code if needed (e.g., unique constraint violation)
        let errorMessage = 'Failed to record like action';
        let statusCode = 500;
        if (insertError.code === '23505') { // Unique violation
            errorMessage = 'You have already liked this board.';
            statusCode = 409; // Conflict
        } else if (insertError.message.includes('violates row-level security policy')) {
            errorMessage = 'Permission denied to like this board.';
            statusCode = 403; // Forbidden
        }
        return NextResponse.json({ error: errorMessage, details: insertError.message }, { status: statusCode }); 
    }
    // --- End Insert ---

    return NextResponse.json({ success: true, message: 'Board liked' }, { status: 200 });

  } catch (error) {
    console.error('Error in POST /api/board/like:', error);
    return NextResponse.json({ error: 'An unexpected error occurred' }, { status: 500 });
  }
}

/**
 * Unlike a board
 * DELETE /api/board/like?boardId=...
 */
export async function DELETE(request: NextRequest) {
  try {
  const { user, error: authError, supabase } = await getAuthenticatedUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const boardId = searchParams.get('boardId');

    if (!boardId) {
      return NextResponse.json({ error: 'Missing boardId query parameter' }, { status: 400 });
    }

    // --- Remove like from log table ---
    const { error: deleteError } = await supabase
      .from('board_likes')
      .delete()
      .match({ board_id: boardId, user_id: user.id }); // RLS ensures users can only delete their own likes

    // If delete fails, return an error
    if (deleteError) {
        console.error("Failed to remove like log:", deleteError);
        let errorMessage = 'Failed to remove like record';
        let statusCode = 500;
         if (deleteError.message.includes('violates row-level security policy')) {
            errorMessage = 'Permission denied to unlike this board.';
            statusCode = 403; // Forbidden
        }
        // Note: A non-existent like won't cause an error here, just 0 rows affected.
        return NextResponse.json({ error: errorMessage, details: deleteError.message }, { status: statusCode });
    }
    // --- End Delete ---

    return NextResponse.json({ success: true, message: 'Board unliked' }, { status: 200 });

  } catch (error) {
    console.error('Error in DELETE /api/board/like:', error);
    return NextResponse.json({ error: 'An unexpected error occurred' }, { status: 500 });
  }
} 