'use client';

import { useState, useCallback, useEffect, useRef } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { 
  Board, 
  BoardItem, 
  BoardViewState, 
  Position, 
  UseBoardStateReturn, 
  Connection, 
  PenStroke,
  FullBoardState
} from '../types';
import { 
  boardService, 
  itemRegistry, 
  operationHistory, 
  AddItemOperation, 
  UpdateItemOperation, 
  DeleteItemOperation,
  AddConnectionOperation,
  BatchConnectionOperation
} from '../services';
import type { Database } from '@/lib/database.types';

// True debounce implementation with cancel capability
const debounce = <T extends (...args: any[]) => any>(fn: T, ms = 300) => {
  let timeoutId: ReturnType<typeof setTimeout> | null = null;
  
  // The debounced function
  const debouncedFn = function(this: any, ...args: Parameters<T>) {
    if (timeoutId !== null) {
      clearTimeout(timeoutId);
    }
    timeoutId = setTimeout(() => {
      timeoutId = null;
      fn.apply(this, args);
    }, ms);
  };
  
  // Add a cancel method
  debouncedFn.cancel = () => {
    if (timeoutId !== null) {
      clearTimeout(timeoutId);
      timeoutId = null;
    }
  };
  
  return debouncedFn;
};

// Helper function for simple deep comparison (can be expanded if needed)
const simpleDeepCompare = (obj1: any, obj2: any): boolean => {
  if (obj1 === null || obj2 === null || typeof obj1 !== 'object' || typeof obj2 !== 'object') {
    return obj1 === obj2;
  }
  // Check if both are null or undefined after the object check
  if (obj1 == null && obj2 == null) return true;
  if (obj1 == null || obj2 == null) return false;

  const keys1 = Object.keys(obj1);
  const keys2 = Object.keys(obj2);
  if (keys1.length !== keys2.length) return false;
  for (const key of keys1) {
    // Ensure the key exists in obj2 before comparing
    if (!obj2.hasOwnProperty(key) || !simpleDeepCompare(obj1[key], obj2[key])) {
      return false;
    }
  }
  return true;
};

// Helper function for distance
const distSq = (p1: Position, p2: Position): number => {
  return Math.pow(p1.x - p2.x, 2) + Math.pow(p1.y - p2.y, 2);
};

/**
 * Custom hook for managing board state
 */
export const useBoardState = (
  boardId?: string,
  debouncedSave?: (boardState: FullBoardState) => void
): UseBoardStateReturn => {
  // Initialize board with default values
  const [board, setBoard] = useState<Board>({
    id: boardId || 'new',
    name: 'Untitled board',
    elements: [],
    connections: [],
    strokes: []
  });

  // Initialize view state
  const [viewState, setViewStateInternal] = useState<BoardViewState>({
    scale: 1,
    position: { x: 0, y: 0 },
    selectedItemId: null,
    connectMode: false,
    connectStart: null,
    isGrabbing: false,
    presentationMode: false
  });

  // Create a ref to store debounced functions by item ID
  const debouncedUpdates = useRef<Record<string, Function>>({});
  
  // Add a ref to track items being deleted to prevent duplicate deletion
  const itemsBeingDeleted = useRef<Set<string>>(new Set());
  
  // Add a ref to track which server requests are already in flight
  const pendingServerRequests = useRef<Map<string, Promise<any>>>(new Map());

  // Fetch board data when boardId changes
  useEffect(() => {
    if (boardId && boardId !== 'new') {
      const fetchBoard = async () => {
        try {
          const boardData = await boardService.getBoard(boardId);
          
          // Ensure board data is properly structured
          const normalizedData = {
            ...boardData,
            elements: Array.isArray(boardData.elements) ? boardData.elements : [],
            connections: Array.isArray(boardData.connections) ? boardData.connections : [],
            strokes: Array.isArray(boardData.strokes) ? boardData.strokes : [],
            sharing: Array.isArray(boardData.sharing) ? boardData.sharing : []
          };
          
          setBoard(normalizedData);
        } catch (error) {
          console.error('Error fetching board:', error);
          setBoard({
            id: boardId,
            name: 'Error Loading Board',
            elements: [],
            connections: [],
            strokes: [],
            sharing: []
          });
        }
      };
      
      fetchBoard();
    } else {
      setBoard({
        id: 'new',
        name: 'Untitled board',
        elements: [],
        connections: [],
        strokes: [],
        sharing: []
      });
    }
  }, [boardId]);

  // Handle view state updates
  const setViewState = useCallback((newViewState: Partial<BoardViewState>) => {
    setViewStateInternal(prev => ({ ...prev, ...newViewState }));
  }, []);

  // Add a new item to the board
  const addItem = useCallback(async (type: string, position?: Position, props?: Record<string, any>, options?: { skipSync?: boolean }): Promise<BoardItem> => {
    // If position is not provided, calculate it based on the current viewport center
    let defaultPosition;
    if (!position) {
      // Get current viewport dimensions
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;
      
      // Calculate the center position in board coordinates by considering
      // the current board position and scale
      const centerX = (viewportWidth / 2 - viewState.position.x) / viewState.scale;
      const centerY = (viewportHeight / 2 - viewState.position.y) / viewState.scale;
      
      defaultPosition = { x: centerX, y: centerY };
    } else {
      defaultPosition = position;
    }
    
    // If this is a sticky note with a color, modify the type to include the color
    let finalType = type;
    if (type === 'sticky' && props?.color) {
      finalType = `sticky-${props.color}`;
    }
    
    // Create a new item using the item registry
    const newItem = itemRegistry.createItem(finalType, defaultPosition);
    
    if (!newItem) {
      throw new Error(`Could not create item of type: ${finalType}`);
    }
    
    // Apply any additional properties from props
    if (props) {
      Object.assign(newItem, props);
    }
    // Assign default size from registry plugin
    const plugin = itemRegistry.getItemPlugin(finalType);
    if (plugin) {
      newItem.width = plugin.width;
      newItem.height = plugin.height;
    }
    
    // --- Immediate Signed URL Generation (if needed) ---
    // If it's an article with a file_url (from manual upload) but no imageUrl yet
    if (newItem.type === 'article' && 'file_url' in newItem && newItem.file_url && !newItem.imageUrl) {
      try {
        const supabase = createClientComponentClient<Database>();
        const fileUrl = newItem.file_url as string;
        const cleanFilePath = fileUrl.includes('/images/') 
          ? fileUrl.split('/images/').pop() || fileUrl
          : fileUrl;
        const { data: signedUrlData, error: signedUrlError } = await supabase
          .storage
          .from('images')
          .createSignedUrl(cleanFilePath, 3600); 

        if (signedUrlError) {
          console.error("[addItem] Failed to generate initial signed URL for article image:", signedUrlError);
        } else if (signedUrlData) {
          newItem.imageUrl = signedUrlData.signedUrl; // Add URL directly to the item being added
        }
      } catch (error) {
        console.error("[addItem] Error generating initial signed URL for article image:", error);
      }
    } else if (newItem.type === 'image' && 'file_url' in newItem && newItem.file_url && !newItem.imageUrl) {
      // Also handle immediate generation for image types if needed (though less common scenario on initial add)
      try {
        const supabase = createClientComponentClient<Database>();
        const fileUrl = newItem.file_url as string;
        const cleanFilePath = fileUrl.includes('/images/') 
          ? fileUrl.split('/images/').pop() || fileUrl
          : fileUrl;
        const { data: signedUrlData, error: signedUrlError } = await supabase
          .storage
          .from('images')
          .createSignedUrl(cleanFilePath, 3600); 

        if (signedUrlError) {
          console.error("[addItem] Failed to generate initial signed URL for image:", signedUrlError);
        } else if (signedUrlData) {
          newItem.imageUrl = signedUrlData.signedUrl; 
        }
      } catch (error) {
        console.error("[addItem] Error generating initial signed URL for image:", error);
      }
    }
    
    // Add operation to history for undo/redo
    const operation = new AddItemOperation({ item: newItem });
    const updatedBoard = operation.execute(board);
    
    operationHistory.addOperation(operation);
    setBoard(updatedBoard);
    
    // Auto-save the change if we have a boardId and sync is NOT skipped
    if (!options?.skipSync && boardId && boardId !== 'new') {
      boardService.updateElement(boardId, newItem, 'add')
        .then(async (response) => {
          console.log('[addItem] Server response for ADD:', response); // Log raw response
          // If server returned a new ID, update the item in the board
          if (response.element && response.element.id !== newItem.id) {
            const serverItem = response.element;
            console.log(`[addItem] Server ID (${serverItem.id}) differs from client ID (${newItem.id}). Updating local state.`);
            console.log(`[addItem] Server item type: ${serverItem.type}`);

            // For image elements, generate a signed URL immediately if it has a file_url
            // (This might now be redundant due to immediate generation above, but keep as fallback)
            if (serverItem.type === 'image' && 'file_url' in serverItem && serverItem.file_url && !serverItem.imageUrl) {
              console.log('[addItem] Processing image-specific URL generation');
              try {
                // Create a Supabase client for the browser
                const supabase = createClientComponentClient<Database>();
                
                // Generate a signed URL for the image
                // Clean the file path in case it includes a full URL
                const fileUrl = serverItem.file_url as string;
                const cleanFilePath = fileUrl.includes('/images/') 
                  ? fileUrl.split('/images/').pop() || fileUrl
                  : fileUrl;
                
                const { data: signedUrlData, error: signedUrlError } = await supabase
                  .storage
                  .from('images') // Use your bucket name - 'images' based on previous code
                  .createSignedUrl(cleanFilePath, 3600); // 1 hour expiry, use type assertion
                
                if (signedUrlError) {
                  console.error("Failed to generate signed URL for image:", signedUrlError);
                } else if (signedUrlData) {
                  // Add the signed URL to the server item
                  serverItem.imageUrl = signedUrlData.signedUrl;
                }
              } catch (error) {
                console.error("Error generating signed URL for image:", error);
              }
            } else if (serverItem.type === 'article' && 'file_url' in serverItem && serverItem.file_url && !serverItem.imageUrl) {
              // Skip generating signed URL for articles since it's not working correctly
              console.log('[addItem] Skipping article URL generation - using direct file URL instead');
              
              // Use the file_url directly if it exists
              if (serverItem.file_url) {
                const fileUrl = serverItem.file_url as string;
                // Set the imageUrl to be the same as file_url to maintain functionality
                serverItem.imageUrl = fileUrl;
                console.log('[addItem] Using direct file_url for article image:', fileUrl);
              }
              
              // Log completion for tracking
              console.log('[addItem] Completed article URL handling (skipped signed URL generation)');
            } else {
              console.log('[addItem] No URL generation needed or conditions not met', {
                type: serverItem.type,
                hasFileUrl: 'file_url' in serverItem,
                fileUrlValue: 'file_url' in serverItem ? (serverItem as any).file_url : undefined,
                hasImageUrl: 'imageUrl' in serverItem,
                imageUrlValue: 'imageUrl' in serverItem ? (serverItem as any).imageUrl : undefined
              });
            }
            
            console.log('[addItem] About to update board state with server ID');
            
            // Update the board state with the server-generated ID and potentially the imageUrl
            setBoard(currentBoard => {
              console.log(`[addItem] Inside setBoard callback. Searching for item with client ID: ${newItem.id}`);
              // Find the existing item in the current board state
              const currentItem = currentBoard.elements.find(item => item.id === newItem.id);
              
              if (!currentItem) {
                console.warn(`[addItem] Item with client ID ${newItem.id} not found in current board state. Cannot update ID.`);
                return currentBoard;
              }
              
              const updatedElements = currentBoard.elements.map(item => {
                if (item.id === newItem.id) {
                  console.log(`[addItem] Found item with client ID ${item.id}. Updating ID to ${serverItem.id}.`);
                  return { 
                    ...item,  // Keep all existing properties 
                    ...serverItem, // Apply any server-updated properties
                    id: serverItem.id, // Ensure ID is updated
                    // Preserve client-side properties that shouldn't be overwritten
                    imageUrl: currentItem?.imageUrl || (serverItem.imageUrl ? serverItem.imageUrl : item.imageUrl)
                  };
                }
                return item;
              });

              // Log after ID update for debugging
              console.log('[addItem] Updated element IDs after server response:', 
                updatedElements.map(el => ({ id: el.id, type: el.type })));

              return {
                ...currentBoard,
                elements: updatedElements
              };
            });
            
            // If this item is selected, update the selection ID
            if (viewState.selectedItemId === newItem.id) {
              console.log(`[addItem] Updating selected item ID from ${newItem.id} to ${serverItem.id}`);
              setViewState({ selectedItemId: serverItem.id });
            }
          } else {
             console.log('[addItem] Server ID matched client ID or no element in response. No local ID update needed.');
          }
        })
        .catch(error => console.error('[addItem] Error saving new item:', error));
    }
    
    return newItem;
  }, [board, boardId, viewState.selectedItemId, viewState.position, viewState.scale, setViewState]);

  // Add multiple items to the board in a single batch
  const addBatchItems = useCallback(async (
    items: Array<{type: string, position?: Position, props?: Record<string, any>}>,
    options?: { skipSync?: boolean }
  ): Promise<BoardItem[]> => {
    console.log(`[addBatchItems] ===== STARTING BATCH ITEM CREATION =====`);
    console.log(`[addBatchItems] Creating ${items.length} items in batch`);
    console.log(`[addBatchItems] Current board has ${board.elements.length} elements`);
    items.forEach((item, index) => {
      console.log(`[addBatchItems] Input item ${index}: type=${item.type}, props.id=${item.props?.id}, position=${JSON.stringify(item.position)}`);
    });

    // Create all items locally first
    const newItems: BoardItem[] = [];
    const defaultPositions: Record<string, Position> = {};
    
    // Calculate default positions only once if needed
    if (items.some(item => !item.position)) {
      // Get current viewport dimensions
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;
      
      // Calculate the center position in board coordinates
      const centerX = (viewportWidth / 2 - viewState.position.x) / viewState.scale;
      const centerY = (viewportHeight / 2 - viewState.position.y) / viewState.scale;
      
      // Use grid layout for multiple items without positions
      const itemsWithoutPositions = items.filter(item => !item.position).length;
      const gridCols = Math.ceil(Math.sqrt(itemsWithoutPositions));
      const spacing = 250; // Pixels between items
      
      let gridRow = 0;
      let gridCol = 0;
      
      items.forEach((item, index) => {
        if (!item.position) {
          // Calculate grid position
          const posX = centerX + (gridCol - Math.floor(gridCols/2)) * spacing;
          const posY = centerY + (gridRow - Math.floor(itemsWithoutPositions/gridCols/2)) * spacing;
          
          defaultPositions[index] = { x: posX, y: posY };
          
          // Move to next grid position
          gridCol++;
          if (gridCol >= gridCols) {
            gridCol = 0;
            gridRow++;
          }
        }
      });
    }
    
    // Create all items
    items.forEach((item, index) => {
      // Get position (either provided or from default grid)
      const itemPosition = item.position || defaultPositions[index] || { x: 0, y: 0 };
      
      // Adjust type for sticky notes with color
      let finalType = item.type;
      if (item.type === 'sticky' && item.props?.color) {
        finalType = `sticky-${item.props.color}`;
      }
      
      // Create item using registry
      console.log(`[addBatchItems] Creating item ${index} with registry.createItem(${finalType}, ${JSON.stringify(itemPosition)})`);
      const newItem = itemRegistry.createItem(finalType, itemPosition);

      if (!newItem) {
        console.error(`[addBatchItems] Could not create item of type: ${finalType}`);
        return;
      }

      console.log(`[addBatchItems] Registry created item ${index} with ID: ${newItem.id}`);

      // Apply additional properties
      if (item.props) {
        console.log(`[addBatchItems] Applying props to item ${index}:`, item.props);
        Object.assign(newItem, item.props);

        // If an ID was provided in props (e.g., from AI tool), preserve it
        if (item.props.id) {
          console.log(`[addBatchItems] Preserving existing ID: ${item.props.id} (was ${newItem.id})`);
          newItem.id = item.props.id;
        }
      }

      console.log(`[addBatchItems] Final item ${index} created with ID: ${newItem.id}, type: ${newItem.type}`);
      // Assign default size from registry plugin
      const plugin = itemRegistry.getItemPlugin(finalType);
      if (plugin) {
        newItem.width = plugin.width;
        newItem.height = plugin.height;
      }
      
      // Handle immediate signed URL generation for images/articles
      if (newItem.type === 'article' && 'file_url' in newItem && newItem.file_url && !newItem.imageUrl) {
        // For articles, we'll handle URL generation after server response to avoid redundant work
        // But we track that it needs URL generation
        (newItem as any)._needsUrlGeneration = true;
      } else if (newItem.type === 'image' && 'file_url' in newItem && newItem.file_url && !newItem.imageUrl) {
        // For images, we'll handle URL generation after server response too
        (newItem as any)._needsUrlGeneration = true;
      }
      
      newItems.push(newItem);
    });
    
    // Create batch operation for history
    const batchOperation = {
      execute: (boardState: Board): Board => ({
        ...boardState,
        elements: [...boardState.elements, ...newItems]
      }),
      undo: (boardState: Board): Board => ({
        ...boardState,
        elements: boardState.elements.filter(item => 
          !newItems.some(newItem => newItem.id === item.id)
        )
      })
    };
    
    // Apply the batch update to the board using functional update to avoid stale state
    setBoard(currentBoard => {
      const updatedBoard = batchOperation.execute(currentBoard);
      return updatedBoard;
    });
    
    // Auto-save all items if we have a boardId and sync is NOT skipped
    if (!options?.skipSync && boardId && boardId !== 'new' && newItems.length > 0) {
      try {
        console.log(`[addBatchItems] Saving ${newItems.length} items to server`);
        const response = await boardService.updateBatchElements(boardId, newItems, 'add');
        
        if (response.elements && response.elements.length > 0) {
          console.log(`[addBatchItems] Received ${response.elements.length} items from server`);
          
          // Create ID mapping for client to server IDs
          const idMap: Record<string, string> = {};
          const serverItemsMap: Record<string, BoardItem> = {};
          
          // Process server items and create mapping
          for (let i = 0; i < newItems.length; i++) {
            const clientItem = newItems[i];
            const serverItem = response.elements[i];
            
            if (serverItem && serverItem.id !== clientItem.id) {
              idMap[clientItem.id] = serverItem.id;
              serverItemsMap[clientItem.id] = serverItem;
            }
          }
          
          // If we have ID mappings, update the board state
          if (Object.keys(idMap).length > 0) {
            console.log('[addBatchItems] Preparing to call setBoard to update IDs. idMap keys:', Object.keys(idMap), 'serverItemsMap keys:', Object.keys(serverItemsMap));

            // Generate signed URLs for items that need them
            for (const clientId of Object.keys(serverItemsMap)) {
              const serverItem = serverItemsMap[clientId];
              const clientItem = newItems.find(item => item.id === clientId);
              
              if (clientItem && (clientItem as any)._needsUrlGeneration) {
                if ((serverItem.type === 'image' || serverItem.type === 'article') && 
                    'file_url' in serverItem && serverItem.file_url) {
                  try {
                    const supabase = createClientComponentClient<Database>();
                    const fileUrl = serverItem.file_url as string;
                    const cleanFilePath = fileUrl.includes('/images/') 
                      ? fileUrl.split('/images/').pop() || fileUrl
                      : fileUrl;
                      
                    const { data: signedUrlData, error: signedUrlError } = await supabase
                      .storage
                      .from('images')
                      .createSignedUrl(cleanFilePath, 3600);
                    
                    if (!signedUrlError && signedUrlData) {
                      serverItem.imageUrl = signedUrlData.signedUrl;
                    }
                  } catch (error) {
                    console.error(`[addBatchItems] Error generating signed URL for ${serverItem.type}:`, error);
                  }
                }
              }
            }
            
            // Update board with server IDs and signed URLs
            setBoard(currentBoard => {
              const updatedElements = currentBoard.elements.map(item => {
                const serverId = idMap[item.id];
                if (serverId) {
                  const serverItem = serverItemsMap[item.id];
                  if (!serverItem) {
                     console.error(`[addBatchItems] CRITICAL ERROR: serverItem not found in serverItemsMap for client ID ${item.id}!`);
                     return item;
                  }
                  return {
                    ...item,
                    ...serverItem,
                    id: serverId
                  };
                }
                return item;
              });

              if (currentBoard.elements.length > 0 && updatedElements.length === 0) {
                console.error("[addBatchItems] CRITICAL ERROR: updatedElements became empty during ID update!");
              }

              return {
                ...currentBoard,
                elements: updatedElements
              };
            });
            
            // Update selection if needed
            if (viewState.selectedItemId && idMap[viewState.selectedItemId]) {
              console.log(`[addBatchItems] Updating selected item ID from ${viewState.selectedItemId} to ${idMap[viewState.selectedItemId]}`);
              setViewState({ selectedItemId: idMap[viewState.selectedItemId] });
            }

            // Update the newItems array with server IDs for return value
            const finalItems = newItems.map(item => {
              const serverId = idMap[item.id];
              if (serverId) {
                const serverItem = serverItemsMap[item.id];
                console.log(`[addBatchItems] Updating return item ID from ${item.id} to ${serverId}`);
                return {
                  ...item,
                  ...serverItem,
                  id: serverId
                };
              }
              return item;
            });

            console.log(`[addBatchItems] Returning ${finalItems.length} items with final IDs:`, finalItems.map(item => item.id));
            return finalItems;
          } else {
             console.log('[addBatchItems] Skipping setBoard for ID update because idMap is empty.');
          }
        }
      } catch (error) {
        console.error('[addBatchItems] Error saving batch items to server:', error);
      }
    }

    return newItems;
  }, [board, boardId, viewState.position, viewState.scale, viewState.selectedItemId, setViewState]);

  // Function to save item changes to the server
  const saveItemToServer = useCallback((itemId: string, action: 'add' | 'update' | 'delete', itemToSaveOverride?: BoardItem) => {
    if (!boardId || boardId === 'new') return Promise.resolve(null);
    
    // Check if there's already a pending request for this item and action
    const requestKey = `${action}-${itemId}`;
    if (pendingServerRequests.current.has(requestKey)) {
      console.log(`[saveItemToServer] Request already in flight for ${action} on item ${itemId}. Reusing promise.`);
      return pendingServerRequests.current.get(requestKey)!;
    }

    // Use the provided item override if available, otherwise find it in the current board state
    let item: BoardItem | undefined;
    if (itemToSaveOverride) {
      item = itemToSaveOverride;
    } else {
      // Find the *current* item state using the itemId just before saving
      item = board.elements.find(elem => elem.id === itemId);
    }

    // If item is not found in the current state (e.g., deleted before save), abort
    if (!item) {
        console.warn(`[saveItemToServer] Item with ID ${itemId} not found in current state. This may be because the ID changed during a batch operation. Aborting ${action}.`);
        // If it was a delete action that's now aborted, ensure it's removed from the tracking set
        if (action === 'delete') {
             itemsBeingDeleted.current.delete(itemId);
        }
        return Promise.resolve(null); 
    }
    
    // Create a copy of the item found in the current state to send to the server
    const itemToSend = { ...item }; 
    
    // For update actions, ensure image/article elements preserve their imageUrl and file_url, and avoid sending transient signed URLs
    if (action === 'update' && (item.type === 'image' || item.type === 'image-invisible' || item.type === 'article')) {
      if ('imageUrl' in item && item.imageUrl) {
        (itemToSend as any).imageUrl = item.imageUrl;
      }
      
      // Make sure the file_url is preserved and cleaned
      if ('file_url' in item && (item as any).file_url) {
        let file_url = (item as any).file_url;
        if (typeof file_url === 'string' && file_url.includes('/images/')) {
          const cleanPath = file_url.split('/images/').pop();
          if (cleanPath) {
            file_url = cleanPath;
          }
        }
        (itemToSend as any).file_url = file_url;
      }

      // Remove the transient `url` field which may contain a short‑lived signed URL.
      // We never want to persist this value to the database.
      if ('url' in itemToSend) {
        delete (itemToSend as any).url;
      }
    }
    
    console.log(`[saveItemToServer] Performing ${action} for item ID: ${itemToSend.id}`);

    // Create the promise for this request
    const requestPromise = boardService.updateElement(boardId, itemToSend, action)
      .then(response => {
        // Check if the server sent back a different ID (Should NOT happen for 'update'/'delete')
        if (response.element && response.element.id !== item!.id && action === 'add') {
           // This block handles the ID update after an 'add' operation now moved here.
           // It was previously (and redundantly) in addItem's .then()
          console.log(`[saveItemToServer] Server ID (${response.element.id}) differs from client ID (${item.id}) after ADD. Updating local state.`);
          setBoard(currentBoard => ({
            ...currentBoard,
            elements: currentBoard.elements.map(boardItem => 
              boardItem.id === item!.id // Find by original client ID
                ? { ...boardItem, ...response.element } // Replace with server data, ensuring ID is updated
                : boardItem
            )
          }));
          // Also update selection if needed
          if (viewState.selectedItemId === item!.id) {
            setViewState({ selectedItemId: response.element.id });
          }
        } else if (response.element && response.element.id !== item!.id && action !== 'add') {
             // Log a warning if the ID changes unexpectedly during an update/delete
             console.warn(`[saveItemToServer] Server returned unexpected ID change during ${action} for item ${item!.id}. Server ID: ${response.element.id}`);
        }
        return response;
      })
      .catch(error => {
        console.error(`[saveItemToServer] Error ${action}ing item ${item!.id}:`, error);
        // Optionally: Check if the error is a 404 Not Found for an 'update' action
        // This might indicate a race condition where the item was deleted before the update could save.
        if (action === 'update' && error.message?.includes('not found')) {
            console.warn(`[saveItemToServer] Update failed for item ${item!.id} (Not Found). Item might have been deleted.`);
        }
        return null; 
      })
      .finally(() => {
        // If this was a delete operation, remove the item from the "being deleted" set
        if (action === 'delete') {
          itemsBeingDeleted.current.delete(itemId);
        }
        // Remove this request from the pending map
        pendingServerRequests.current.delete(requestKey);
      });
    
    // Store the promise in our map of pending requests
    pendingServerRequests.current.set(requestKey, requestPromise);
    
    return requestPromise;
  }, [boardId, board.elements, viewState.selectedItemId, setViewState]);

  // Update an item
  const updateItem = useCallback((id: string, updates: Partial<BoardItem>, options?: { skipSync?: boolean }) => {
    
    // Special logging for content updates
    if ('content' in updates) {
      const targetItem = board.elements.find(item => item.id === id);
      if (targetItem) {
      } else {
      }
    }

    setBoard(prev => {
      const elementIndex = prev.elements.findIndex(item => item.id === id);
      if (elementIndex === -1) {
        console.warn(`[updateItem] Item with ID ${id} not found. Cannot apply updates.`);
        console.log(`[updateItem] Available IDs:`, prev.elements.map(e => e.id));
        return prev;
      }

      const existingItem = prev.elements[elementIndex];
      let itemChanged = false;

      // Perform comparison BEFORE creating the updatedItem object
      itemChanged = Object.keys(updates).some(key => {
        const updateKey = key as keyof BoardItem;
        // Ensure the key exists on the existing item for a valid comparison, 
        // although typically updates should only contain existing keys.
        if (!existingItem.hasOwnProperty(updateKey) && updates[updateKey] !== undefined) {
          console.log(`[updateItem] New property being added: ${updateKey}:`, updates[updateKey]);
          return true; // It's a new property being added essentially
        }
        if (updateKey === 'position') {
          // Deep compare for position
          return !simpleDeepCompare(updates.position, existingItem.position);
        }
        // Shallow compare for other properties
        if (updateKey === 'content') {
          console.log(`[updateItem] Content comparison: existing="${existingItem[updateKey]}" vs new="${updates[updateKey]}"`);
        }
        return updates[updateKey] !== existingItem[updateKey];
      });

      // If nothing changed, return the previous state without modifications
      if (!itemChanged) {
        return prev; // IMPORTANT: Skip state update and side effects
      }
      
      // Now create a new item with the updates merged in
      let updatedItem: BoardItem;
      
      // Special handling for image/image-invisible to preserve imageUrl and file_url
      if (existingItem.type === 'image' || existingItem.type === 'image-invisible' || 
          (updates as any).type === 'image' || (updates as any).type === 'image-invisible') {
        // For images, ensure we preserve both imageUrl and file_url
        let file_url = (updates as any).file_url !== undefined 
          ? (updates as any).file_url 
          : (existingItem as any).file_url;
        
        // Clean up file_url if it's a URL instead of just the file path
        if (typeof file_url === 'string' && file_url.includes('/images/')) {
          const cleanPath = file_url.split('/images/').pop();
          if (cleanPath) {
            file_url = cleanPath;
          }
        }
        
        updatedItem = { 
          ...existingItem, 
          ...updates,
          // Explicitly preserve imageUrl if it exists and isn't included in updates
          imageUrl: updates.imageUrl !== undefined ? updates.imageUrl : existingItem.imageUrl,
          // Also preserve file_url which is critical for database storage
          file_url: file_url
        };  
      } else {
        // For non-image elements, proceed normally
        updatedItem = { ...existingItem, ...updates };
      }

      // Create operation for history
      const operation = new UpdateItemOperation({
        itemId: id,
        updates,
        previousItem: existingItem
      });

      // Create the new elements array
      const newElements = [
        ...prev.elements.slice(0, elementIndex),
        updatedItem,
        ...prev.elements.slice(elementIndex + 1)
      ];

      const updatedBoard = {
        ...prev,
        elements: newElements
      };
      operationHistory.addOperation(operation);

      // --- Trigger DEBOUNCED save side effect --- 
      if (!options?.skipSync && boardId && boardId !== 'new') {
        // Clear any existing debounced update for this item to prevent multiple in-flight updates
        if (debouncedUpdates.current[id]) {
          (debouncedUpdates.current[id] as any).cancel();
        }

        // Create a new debounced function that passes the updated item directly
        debouncedUpdates.current[id] = debounce((itemIdToSave: string, updatedItemToSave: BoardItem) => { 
          // Pass the updated item directly to the save function
          saveItemToServer(itemIdToSave, 'update', updatedItemToSave);
        }, 500);
        
        // Call the debounced function with the ID
        debouncedUpdates.current[id](id, updatedItem); 
      }
      // --- End Save Trigger ---

      return updatedBoard; // Return the newly calculated state
    });
  }, [boardId, saveItemToServer]);

  // Delete an item
  const deleteItem = useCallback((id: string, options?: { skipSync?: boolean }) => {
    // More aggressive checking to prevent duplicate delete operations
    if (itemsBeingDeleted.current.has(id)) {
        console.warn(`[deleteItem] Delete already in progress for item ${id}. Ignoring duplicate request.`);
        return;
    }
    
    // Pre-emptively mark this item as being deleted to lock against React Strict Mode double invocations
    itemsBeingDeleted.current.add(id);
    
    // Find the item in the current board state
    const itemToDelete = board.elements.find(item => item.id === id); 

    if (!itemToDelete) {
        console.warn(`[deleteItem] Item with ID ${id} not found. Cannot delete.`);
        console.log(`[deleteItem] Current board has ${board.elements.length} elements. IDs:`, 
                   board.elements.map(e => e.id).join(', '));
        // Important: Remove the lock since we're not proceeding with deletion
        itemsBeingDeleted.current.delete(id);
        return; // Item doesn't exist, nothing to do
    }
    
    console.log(`[deleteItem] Found item to delete:`, { 
      id: itemToDelete.id, 
      type: itemToDelete.type,
      position: itemToDelete.position
    });
    
    // Using React 18 automatic batching to ensure state updates happen together
    // and to reduce the chance of double effects due to React Strict Mode
    setBoard(prev => {
      // Check if this is an image element, and if so, delete the file from storage
      if ((itemToDelete.type === 'image' || itemToDelete.type === 'image-invisible') && 
          'file_url' in itemToDelete && itemToDelete.file_url) {

        // Create a Promise to handle the image deletion
        const deleteImagePromise = new Promise<void>((resolve) => {
          try {
            // Create a Supabase client
            const supabase = createClientComponentClient<Database>();
            
            // Extract the file path from file_url
            let filePath = itemToDelete.file_url as string;
            
            // Enhanced file path extraction logic
            if (typeof filePath === 'string') {
              // Case 1: If it's a full URL with /storage/v1/object/
              if (filePath.includes('/storage/v1/object/')) {
                const pathMatch = filePath.match(/\/storage\/v1\/object\/[^/]+\/([^?]+)/);
                if (pathMatch && pathMatch[1]) {
                  filePath = decodeURIComponent(pathMatch[1]);
                }
              }
              // Case 2: If it includes /images/ path segment
              else if (filePath.includes('/images/')) {
                filePath = filePath.split('/images/').pop() || filePath;
              }
              // Case 3: If it's already a relative path, use as is
              
              // Remove any query parameters if present
              if (filePath.includes('?')) {
                filePath = filePath.split('?')[0];
              }
            }
            
            
            // Delete the file from the 'images' bucket with improved error handling
            supabase.storage.from('images').remove([filePath])
              .then(({ data, error }) => {
                if (error) {
                  console.error('Error deleting image file from storage:', error);
                  
                  // Check for specific error types
                  if (error.message?.includes('not found')) {
                    
                    // Try alternative path extraction - just use the filename
                    const originalPath = itemToDelete.file_url as string;
                    const filename = originalPath.split('/').pop()?.split('?')[0];
                    
                    if (filename) {
                      supabase.storage.from('images').remove([filename])
                        .then(result => {
                          if (result.error) {
                            console.error('Alternative deletion failed with error:', result.error);
                            // Try a third approach - using the bucket API directly
                            // Use environment variables instead of accessing protected properties
                            const apiUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
                            const apiKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

                            if (apiUrl && apiKey) {
                              fetch(`${apiUrl}/storage/v1/object/images/${encodeURIComponent(filename)}`, {
                                method: 'DELETE',
                                headers: {
                                  'Content-Type': 'application/json',
                                  'Authorization': `Bearer ${apiKey}`
                                }
                              })
                              .then(response => {
                                return response.text();
                              })
                              .then(text => {
                                resolve();
                              })
                              .catch(err => {
                                console.error('Direct API deletion failed:', err);
                                resolve();
                              });
                            }
                          } else {
                            resolve();
                          }
                        })
                        .catch(err => {
                          console.error('Alternative deletion attempt threw exception:', err);
                          resolve();
                        });
                    } else {
                      resolve();
                    }
                  } else if (error.message?.includes('timeout') || 
                            error.message?.includes('network') ||
                            error.name?.includes('ConnectTimeout')) {
                    console.error('Network timeout during deletion, request may have failed to complete');
                    resolve();
                  } else {
                    // For any other error type
                    console.error('Unknown error during deletion:', error);
                    resolve();
                  }
                } else {
                  // Success case
                  if (data && data.length > 0) {
                  } else {
                  }
                  resolve();
                }
              })
              .catch(error => {
                console.error('Unexpected error in storage deletion:', error);
                resolve(); // Resolve on unexpected errors
              });
          } catch (error) {
            console.error('Error setting up file deletion:', error);
            resolve(); // Resolve on setup errors
          }
        });
        
        // For debugging - wait for the deletion promise to complete
        deleteImagePromise.then(() => {
        });
      } else {
      }
      
      // Create operation for history
      const operation = new DeleteItemOperation({
        itemId: id,
        item: itemToDelete // Use the item we found earlier
      });
      
      const updatedBoard = operation.execute(prev);
      operationHistory.addOperation(operation);
      
      // Also delete any connections involving this item
      const updatedConnections = prev.connections.filter(
        conn => conn.fromId !== id && conn.toId !== id
      );
      
      const finalBoard = {
        ...updatedBoard,
        connections: updatedConnections
      };
      
      // Auto-save the change if we have a boardId and sync is NOT skipped
      if (!options?.skipSync && boardId && boardId !== 'new') {
        // Call saveItemToServer with the ID and 'delete' action
        saveItemToServer(id, 'delete', itemToDelete); 
      } else {
        // If no boardId, we can immediately remove from being deleted set
        itemsBeingDeleted.current.delete(id);
      }
      
      return finalBoard;
    });
    
    // If the deleted item was selected, clear selection
    if (viewState.selectedItemId === id) {
      setViewState({ selectedItemId: null });
    }
  }, [boardId, saveItemToServer, setViewState, viewState.selectedItemId, board.elements]);

  // Update item position - just call updateItem since we've implemented debouncing there
  const updateItemPosition = useCallback((id: string, position: Position, options?: { skipSync?: boolean }) => {
    updateItem(id, { position }, options);
  }, [updateItem]);

  // Update item content
  const updateItemContent = useCallback((id: string, content: string, options?: { skipSync?: boolean }) => {
    updateItem(id, { content }, options);
  }, [updateItem]);

  // Select an item
  const selectItem = useCallback((id: string | null) => {
    setViewState({ selectedItemId: id });
  }, [setViewState]);

  // Add a new function to update item position by a delta
  const updateItemPositionByDelta = useCallback((id: string, delta: { dx: number, dy: number }, options?: { skipSync?: boolean }) => {
    setBoard(prev => {
      const elementIndex = prev.elements.findIndex(item => item.id === id);
      if (elementIndex === -1) {
        console.warn(`[updateItemPositionByDelta] Item with ID ${id} not found.`);
        return prev;
      }

      const existingItem = prev.elements[elementIndex];
      const newPosition = {
        x: existingItem.position.x + delta.dx,
        y: existingItem.position.y + delta.dy,
      };

      // Check if position actually changed to avoid unnecessary updates
      // Assuming simpleDeepCompare is available in this scope
      if (simpleDeepCompare(newPosition, existingItem.position)) {
        console.log(`[updateItemPositionByDelta] Position for item ${id} did not change. Skipping update.`);
        return prev;
      }

      const updatedItem = { ...existingItem, position: newPosition };

      const operation = new UpdateItemOperation({
        itemId: id,
        updates: { position: newPosition },
        previousItem: { ...existingItem } 
      });

      const newElements = [
        ...prev.elements.slice(0, elementIndex),
        updatedItem,
        ...prev.elements.slice(elementIndex + 1)
      ];

      const updatedBoard = {
        ...prev,
        elements: newElements
      };
      operationHistory.addOperation(operation);

      if (!options?.skipSync && boardId && boardId !== 'new') {
        if (debouncedUpdates.current[id]) {
          (debouncedUpdates.current[id] as any).cancel();
        }
        debouncedUpdates.current[id] = debounce((itemIdToSave: string, itemToSave: BoardItem) => {
          saveItemToServer(itemIdToSave, 'update', itemToSave);
        }, 500);
        debouncedUpdates.current[id](id, updatedItem);
      }
      return updatedBoard;
    });
  }, [boardId, saveItemToServer, simpleDeepCompare]); // Ensure simpleDeepCompare is a dependency if it's not from a higher scope or imported globally

  // Add a connection between items
  const addConnection = useCallback((fromId: string, toId: string, options?: { skipSync?: boolean }): Connection => {
    // Create a new connection with UUID
    const newConnection: Connection = {
      id: uuidv4(),
      fromId,
      toId
    };
    
    // Add operation to history for undo/redo
    const operation = new AddConnectionOperation({ connection: newConnection });
    const updatedBoard = operation.execute(board);
    
    operationHistory.addOperation(operation);
    setBoard(updatedBoard);
    
    // Auto-save the change if we have a boardId and sync is NOT skipped
    if (!options?.skipSync && boardId && boardId !== 'new') {
      boardService.updateConnection(boardId, newConnection, 'add')
        .then(response => {
          // If server returned a new ID, update the connection in the board
          if (response.connection && response.connection.id !== newConnection.id) {
            // Update the connection with the server-generated ID
            const serverConnection = response.connection;
            
            setBoard(currentBoard => ({
              ...currentBoard,
              connections: currentBoard.connections.map(conn => 
                conn.id === newConnection.id 
                  ? { ...conn, id: serverConnection.id }
                  : conn
              )
            }));
          }
        })
        .catch(error => console.error('Error saving new connection:', error));
    }
    
    return newConnection;
  }, [board, boardId]);

  // Add multiple connections in a batch
  const addBatchConnections = useCallback((connections: Array<{fromId: string, toId: string, label?: string}>, options?: { skipSync?: boolean }): Connection[] => {
    if (connections.length === 0) return [];

    // Create connections with UUIDs
    const newConnections: Connection[] = connections.map(conn => ({
      id: uuidv4(),
      fromId: conn.fromId,
      toId: conn.toId,
      label: conn.label
    }));

    // Create a single operation for batch addition (for history/undo only)
    const batchOperation = new BatchConnectionOperation({
      connections: newConnections
    });
    operationHistory.addOperation(batchOperation); // Keep for undo/redo

    // Manually apply the update to the board, ensuring elements are preserved
    setBoard(currentBoard => {
        console.log(`[addBatchConnections] setBoard callback START. Element count before: ${currentBoard.elements.length}, Connection count before: ${currentBoard.connections.length}`);
        const updatedConnections = [...currentBoard.connections, ...newConnections];
        console.log(`[addBatchConnections] setBoard callback FINISHED. Element count after: ${currentBoard.elements.length}, Connection count after: ${updatedConnections.length}`); // Elements should be unchanged here

        // Explicitly return the current elements with the new connections
        return {
            ...currentBoard,
            elements: currentBoard.elements, // Ensure elements are carried over
            connections: updatedConnections
        };
    });

    // Auto-save the changes if we have a boardId and sync is NOT skipped
    if (!options?.skipSync && boardId && boardId !== 'new') {
      try {
        console.log(`[addBatchConnections] Saving ${newConnections.length} connections to server with provided IDs`);
        boardService.updateBatchConnections(boardId, newConnections, 'add')
          .catch(error => {
            console.error('[addBatchConnections] Error saving batch connections to server:', error);
          });
      } catch (error) {
        console.error('[addBatchConnections] Error in batch connections processing:', error);
      }
    }

    return newConnections;
  }, [boardId]); // Removed 'board' from dependency array as we use the updater function now

  // --- Stroke Operations ---
  const addStroke = useCallback((stroke: PenStroke, options?: { skipSync?: boolean }) => {
    setBoard(prevBoard => {
      const updatedBoard = {
        ...prevBoard,
        strokes: [...prevBoard.strokes, stroke]
      };
      
      // Call debounced save if provided, passing ONLY the strokes
      if (!options?.skipSync && debouncedSave) {
        const strokesOnly: FullBoardState = {
          // Only include strokes, not elements or connections
          strokes: updatedBoard.strokes
        };
        debouncedSave(strokesOnly);
      }
      
      // TODO: Integrate with operation history if needed
      return updatedBoard;
    });
  }, [debouncedSave]); // Add debouncedSave to dependency array

  // --- Eraser Operations ---
  const removeStrokesNearPoint = useCallback((point: Position, radius: number, options?: { skipSync?: boolean }) => {
    setBoard(prevBoard => {
      const radiusSq = radius * radius;
      let strokesRemoved = false;
      
      const remainingStrokes = prevBoard.strokes.filter(stroke => {
        // Check if any point in the stroke is within the radius
        const intersects = stroke.points.some(strokePoint => 
            distSq(strokePoint, point) <= radiusSq
        );
        if (intersects) {
          strokesRemoved = true;
          return false; // Filter out intersecting strokes
        }
        return true; // Keep non-intersecting strokes
      });

      // If no strokes were removed, return the previous state to avoid unnecessary updates/saves
      if (!strokesRemoved) {
        return prevBoard;
      }
      
      // Create updated board state
      const updatedBoard = {
        ...prevBoard,
        strokes: remainingStrokes
      };
      
      // Trigger debounced save - ONLY save strokes
      if (!options?.skipSync && debouncedSave) {
        const strokesOnly: FullBoardState = {
          // Only include strokes, not elements or connections
          strokes: updatedBoard.strokes
        };
        debouncedSave(strokesOnly);
      }
      
      return updatedBoard;
    });
  }, [debouncedSave]); // Dependency on debouncedSave

  // Return the hook state and functions
  return {
    board,
    viewState,
    setViewState,
    addItem,
    addBatchItems,
    updateItem,
    deleteItem,
    updateItemPosition,
    updateItemContent,
    selectItem,
    addConnection,
    addBatchConnections,
    addStroke,
    removeStrokesNearPoint,
    setBoard,
    saveItemToServer,
    updateItemPositionByDelta
  };
}; 