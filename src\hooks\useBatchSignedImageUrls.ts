'use client';

import { useState, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { useQuery } from '@tanstack/react-query';
import type { Database } from '@/lib/database.types';

interface BatchSignedUrlsResult {
  signedUrls: Record<string, string>;
  isLoading: boolean;
  error: Error | null;
  refetch: () => void;
}

/**
 * Hook to get batch signed URLs for multiple images
 * This is optimized to reduce API calls when loading multiple images
 */
export function useBatchSignedImageUrls(
  boardId: string,
  originalUrls: (string | null)[],
  loadImmediately = true
): BatchSignedUrlsResult {
  const [publicUrls, setPublicUrls] = useState<Record<string, string>>({});
  const supabase = createClientComponentClient<Database>();
  
  // Extract relative paths and generate public URLs as fallbacks
  useEffect(() => {
    if (!originalUrls.length) {
      setPublicUrls({});
      return;
    }
    
    const newPublicUrls: Record<string, string> = {};
    
    // Process each URL to create public fallbacks
    originalUrls.forEach(url => {
      if (!url) return;
      
      // If it's a relative path, create a public URL
      if (!url.startsWith('http')) {
        try {
          const { data } = supabase.storage.from('images').getPublicUrl(url);
          if (data?.publicUrl) {
            newPublicUrls[url] = data.publicUrl;
          }
        } catch (e) {
          console.error('Error getting public URL:', e);
          newPublicUrls[url] = url; // Use original as fallback
        }
      } else {
        // For full URLs, use as is
        newPublicUrls[url] = url;
      }
    });
    
    setPublicUrls(newPublicUrls);
  }, [originalUrls, supabase]);
  
  // Helper to extract relative path from URL for mapping
  const extractRelativePath = (url: string | null): string | null => {
    if (!url) return null;
    
    try {
      // If it's already a relative path
      if (!url.startsWith('http')) {
        return url.includes('/') ? url : null;
      }
      
      // Parse the URL
      const parsedUrl = new URL(url);
      const pathSegments = parsedUrl.pathname.split('/');
      
      // Find the bucket name ('images')
      const bucketIndex = pathSegments.indexOf('images');
      
      if (bucketIndex !== -1 && bucketIndex < pathSegments.length - 1) {
        return pathSegments.slice(bucketIndex + 1).join('/');
      }
      
      return null;
    } catch (error) {
      console.error(`Error parsing URL: ${url}`, error);
      return null;
    }
  };
  
  // Fetch signed URLs for all images in a single API call
  const {
    data: signedUrlsData,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['batchSignedImageUrls', boardId, originalUrls],
    queryFn: async () => {
      if (!boardId || originalUrls.length === 0) {
        return {};
      }
      
      // Filter out null values and duplicates
      const uniqueUrls = [...new Set(originalUrls.filter(Boolean))];
      
      try {
        const response = await fetch('/api/image/signed-url-batch', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            boardId,
            imageUrls: uniqueUrls,
          }),
        });
        
        if (!response.ok) {
          throw new Error('Failed to get batch signed URLs');
        }
        
        const data = await response.json();
        return data.signedUrls || {};
      } catch (e) {
        console.error('Error fetching batch signed URLs:', e);
        return {};
      }
    },
    enabled: !!(boardId && originalUrls.length > 0 && loadImmediately),
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 1000 * 60 * 10,   // 10 minutes
  });
  
  // Combine signed URLs with public URLs as fallbacks
  const combinedSignedUrls: Record<string, string> = {};
  
  // Build the combined result
  if (signedUrlsData) {
    // Map each original URL to its signed version or fallback
    originalUrls.forEach(url => {
      if (!url) return;
      
      const relativePath = extractRelativePath(url);
      if (relativePath && signedUrlsData[relativePath]) {
        combinedSignedUrls[url] = signedUrlsData[relativePath];
      } else if (publicUrls[url]) {
        combinedSignedUrls[url] = publicUrls[url];
      }
    });
  }
  
  return {
    signedUrls: Object.keys(combinedSignedUrls).length > 0 ? combinedSignedUrls : publicUrls,
    isLoading,
    error: error as Error | null,
    refetch
  };
} 