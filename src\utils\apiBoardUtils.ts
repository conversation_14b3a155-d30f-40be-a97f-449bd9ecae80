import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import type { Database, Tables } from '@/lib/database.types';
import type { User } from '@supabase/supabase-js';

// ---- SHARED TYPES ----
export type TransformedElement = {
  id: string;
  type: string;
  title: string;
  content: string;
  url: string;
  file_url?: string;
  website_url?: string;
  position: { x: number; y: number };
  width: number;
  height: number;
  isAiGenerated: boolean;
  imageUrl?: string;
};

export type TransformedConnection = {
  id: string;
  fromId: string;
  toId: string;
  type: string;
  label: string;
  isAiGenerated: boolean;
};

// ---- SHARED HELPER FUNCTIONS ----

export async function getPublicSignedUrlFromEdgeFunction(
    supabase: ReturnType<typeof createRouteHandlerClient<Database>>,
    boardId: string,
    filePath: string,
    currentUser: User | null,
    urlCache: Map<string, string | undefined>
): Promise<string | null> {
    const cacheKey = `edge_${filePath}`;
    if (urlCache.has(cacheKey)) return urlCache.get(cacheKey) || null;
    try {
        const { data, error } = await supabase.functions.invoke('get-public-image-url', {
            body: { boardId, filePath, userId: currentUser?.id },
        });
        if (error) { 
            console.error(`Edge func invoke failed for ${filePath}:`, error.message);
            urlCache.set(cacheKey, undefined);
            return null; 
        }
        if (data?.signedUrl) { 
            urlCache.set(cacheKey, data.signedUrl);
            return data.signedUrl; 
        } 
        else { 
            console.warn(`Edge func ${filePath} no signedUrl.`);
            urlCache.set(cacheKey, undefined);
            return null; 
        }
    } catch (e: any) { 
        console.error(`Edge func invoke exception ${filePath}:`, e.message);
        urlCache.set(cacheKey, undefined);
        return null; 
    }
}

export async function processElementsInBatches(
    elements: Tables<'elements'>[],
    supabase: ReturnType<typeof createRouteHandlerClient<Database>>,
    boardId: string, 
    isPublic: boolean, 
    currentUser: User | null, 
    ownerID: string,
    batchSize = 15, 
    skipImageProcessing = false
): Promise<TransformedElement[]> {
    const results: TransformedElement[] = [];
    const urlCache = new Map<string, string | undefined>(); // Instance of cache per batch processing call

    for (let i = 0; i < elements.length; i += batchSize) {
        const batch = elements.slice(i, i + batchSize);
        const batchPromises = batch.map(async (element): Promise<TransformedElement> => {
            const baseElement: Omit<TransformedElement, 'imageUrl'> & { imageUrl?: string } = {
                id: element.id, 
                type: element.element_type, 
                title: element.title,
                content: element.text_content ?? '', 
                url: '',
                file_url: element.file_url ?? undefined, 
                website_url: element.website_url ?? undefined,
                position: { x: element.position_x, y: element.position_y }, 
                isAiGenerated: element.is_ai_generated,
                width: element.width ?? (element.element_type.startsWith('sticky') ? 256 : element.element_type === 'text' ? 200 : element.element_type === 'article' ? 400 : (element.element_type === 'image' || element.element_type === 'image-invisible') ? 400 : 0),
                height: element.height ?? (element.element_type.startsWith('sticky') ? 256 : element.element_type === 'text' ? 100 : element.element_type === 'article' ? 220 : (element.element_type === 'image' || element.element_type === 'image-invisible') ? 300 : 0),
            };
            let derivedImageUrl: string | undefined = undefined;
            const isImageOrInvisible = element.element_type === 'image' || element.element_type === 'image-invisible';
            const isArticleWithFile = element.element_type === 'article' && element.file_url;

            if (!skipImageProcessing && (isImageOrInvisible || isArticleWithFile) && element.file_url) {
                const fullPathInBucket = element.file_url;
                if (urlCache.has(fullPathInBucket)) {
                    derivedImageUrl = urlCache.get(fullPathInBucket);
                } else {
                    if (isPublic) {
                        const edgeUrl = await getPublicSignedUrlFromEdgeFunction(supabase, boardId, fullPathInBucket, currentUser, urlCache);
                        if (edgeUrl) derivedImageUrl = edgeUrl;
                        else {
                            try {
                                const { data: publicUrlData } = supabase.storage.from('images').getPublicUrl(fullPathInBucket);
                                if (publicUrlData?.publicUrl) derivedImageUrl = publicUrlData.publicUrl;
                                else console.error(`Failed to get public URL for ${fullPathInBucket}.`);
                            } catch (e: any) { console.error(`Public URL fallback exception for ${fullPathInBucket}:`, e.message); }
                        }
                    } else if (currentUser) {
                        if (currentUser.id === ownerID) {
                            try {
                                const { data: signedUrlData, error: signedUrlError } = await supabase.storage.from('images').createSignedUrl(fullPathInBucket, 3600);
                                if (signedUrlError) console.error(`Owner signed URL error: ${signedUrlError.message}`);
                                else if (signedUrlData?.signedUrl) derivedImageUrl = signedUrlData.signedUrl;
                            } catch (e: any) { console.error(`Owner signed URL exception: ${e.message}`); }
                        } else {
                            const edgeUrl = await getPublicSignedUrlFromEdgeFunction(supabase, boardId, fullPathInBucket, currentUser, urlCache);
                            if (edgeUrl) derivedImageUrl = edgeUrl; 
                            else console.warn(`Edge func fail for shared image ${element.id}`);
                        }
                    } else {
                         console.warn(`No session for image ${element.id} on non-public board.`);
                    }
                    urlCache.set(fullPathInBucket, derivedImageUrl);
                }
            }
            if (element.element_type === 'article') {
                baseElement.url = element.website_url || '';
                if (derivedImageUrl && isArticleWithFile) baseElement.imageUrl = derivedImageUrl;
            } else if (isImageOrInvisible) {
                if (derivedImageUrl) { 
                    baseElement.imageUrl = derivedImageUrl; 
                    baseElement.url = derivedImageUrl; 
                } else {
                     baseElement.url = '';
                }
            } else {
                baseElement.url = baseElement.url || '';
            }
            return baseElement as TransformedElement;
        });
        results.push(...await Promise.all(batchPromises));
    }
    return results;
} 