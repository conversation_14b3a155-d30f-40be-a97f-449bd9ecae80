{"name": "detective-board", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -H 0.0.0.0", "build": "next build", "start": "next start", "lint": "next lint", "grant-permissions": "node scripts/grant-permissions.js", "prisma:generate": "npx prisma generate", "prisma:push": "npx prisma db push", "prisma:pull": "npx prisma db pull", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:profile": "node scripts/test-profile-optimization.js", "test:profile:unit": "node scripts/test-profile-optimization.js --unit", "test:profile:integration": "node scripts/test-profile-optimization.js --integration", "test:profile:performance": "node scripts/test-profile-optimization.js --performance", "test:profile:coverage": "node scripts/test-profile-optimization.js --coverage"}, "dependencies": {"@headlessui/react": "^2.2.0", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.5.0", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/supabase-js": "^2.39.0", "@tanstack/react-query": "^5.76.1", "@types/bcrypt": "^5.0.2", "@types/bcryptjs": "^2.4.6", "@types/uuid": "^10.0.0", "@use-gesture/react": "^10.3.1", "@vercel/analytics": "^1.5.0", "axios": "^1.8.4", "bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "cheerio": "^1.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "12.6.2", "heroicons": "^2.2.0", "html2canvas": "^1.4.1", "lucide-react": "^0.330.0", "next": "^14.2.26", "next-auth": "^4.24.11", "punycode": "^2.3.1", "punycode2": "^1.0.1", "puppeteer": "^24.8.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-window": "^1.8.11", "react-window-infinite-loader": "^1.0.10", "sonner": "^2.0.3", "supabase": "^2.20.12", "tailwind-merge": "^3.0.2", "uuid": "^11.1.0"}, "devDependencies": {"@tailwindcss/typography": "^0.5.16", "@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/jest": "^29.5.8", "@types/ms": "^2.1.0", "@types/node": "^20.17.28", "@types/phoenix": "^1.6.6", "@types/react": "^18.3.20", "@types/react-dom": "^18.2.0", "@types/react-window": "^1.8.8", "@types/react-window-infinite-loader": "^1.0.9", "@types/ws": "^8.18.0", "autoprefixer": "^10.4.21", "eslint": "^8.56.0", "eslint-config-next": "14.1.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.5.3", "prisma": "^6.5.0", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "ts-node": "^10.9.2", "typescript": "^5.3.3"}, "overrides": {"framer-motion": "12.6.2"}}