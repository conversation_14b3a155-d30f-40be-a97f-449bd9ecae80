# AI Tools for Detective Board

This document describes the AI tools system that gives the AI assistant direct access to manipulate the detective board.

## Overview

The AI assistant now has access to a set of tools that allow it to:
- Read the current board state
- Create and modify board elements
- Move the user's viewport
- Search and analyze board content
- Connect related elements

## Architecture

### Components

1. **AI Tools API** (`/api/ai/tools`) - Handles tool execution
2. **Enhanced AI Route** (`/api/ai`) - Supports tool calling with OpenRouter
3. **AIChatSidebar** - Handles client-side event dispatching
4. **DetectiveBoard** - Listens for viewport movement events

### Tool Execution Flow

1. User sends message to AI
2. AI decides whether to use tools based on context
3. AI calls tools via OpenRouter's function calling
4. Tools execute server-side operations
5. Tool results are returned to AI
6. AI formulates response based on tool results
7. Client-side events (like viewport movement) are dispatched

## Available Tools

### 1. read_board
**Purpose**: Read the current state of the detective board

```json
{
  "board_id": "string",
  "explanation": "string"
}
```

**Returns**: Board data including elements, connections, strokes, and statistics.

**Usage**: Always start with this tool to understand what's currently on the board.

### 2. create_element
**Purpose**: Create a new element on the board

```json
{
  "board_id": "string",
  "type": "sticky-yellow|sticky-red|sticky-blue|text|article|image",
  "position": {"x": number, "y": number},
  "content": "string",
  "title": "string (optional)",
  "url": "string (optional)",
  "explanation": "string"
}
```

**Special handling for image elements:**
- When creating an image element, provide the image URL in the `url` parameter
- The `content` parameter should contain the caption/description for the image
- The system will automatically download the image from the URL and store it in our storage
- The image will be displayed using our secure storage system, similar to how article images work
```

**Element Types**:
- `sticky-yellow`: General notes and observations
- `sticky-red`: Important/urgent information  
- `sticky-blue`: Questions and hypotheses
- `text`: Longer form text content
- `article`: Detailed content with title and URL
- `image`: Visual content with image URL

**Positioning**:
- Board center: `x: 9500, y: 4635`
- Use 20px grid increments
- Maintain 300px spacing between element clusters
- Minimum 20px spacing between adjacent elements

### 3. create_connection
**Purpose**: Create a connection between two elements

```json
{
  "board_id": "string",
  "from_element_id": "string",
  "to_element_id": "string", 
  "label": "string (optional)",
  "explanation": "string"
}
```

**Usage**: Connect related elements to show relationships, timelines, or logical flows.

### 4. move_viewport
**Purpose**: Move the user's view to focus on specific areas or elements

```json
{
  "board_id": "string",
  "target_position": {"x": number, "y": number} (optional),
  "zoom_level": number (optional, 0.1-3.0),
  "element_id": "string (optional)",
  "explanation": "string"
}
```

**Usage**: Guide user attention to relevant areas after making changes. Use either `target_position` OR `element_id`.

### 5. update_element
**Purpose**: Modify an existing board element

```json
{
  "board_id": "string",
  "element_id": "string",
  "updates": {
    "content": "string (optional)",
    "title": "string (optional)", 
    "url": "string (optional)",
    "position": {"x": number, "y": number} (optional)
  },
  "explanation": "string"
}
```

### 6. delete_element
**Purpose**: Remove an element from the board

```json
{
  "board_id": "string",
  "element_id": "string",
  "explanation": "string"
}
```

### 7. search_elements
**Purpose**: Find elements by content, type, or keywords

```json
{
  "board_id": "string",
  "query": "string",
  "element_type": "sticky-yellow|sticky-red|sticky-blue|text|article|image (optional)",
  "explanation": "string"
}
```

### 8. get_board_stats
**Purpose**: Get overview statistics about the board

```json
{
  "board_id": "string",
  "explanation": "string"
}
```

**Returns**: Element counts by type, total connections, average position, etc.

## Best Practices for AI

### 1. Always Start with read_board
Before making any changes, understand the current board state:
```
I'll start by reading your current board to see what we're working with.
```

### 2. Be Strategic About Element Placement
- Group related elements together
- Use consistent spacing
- Consider the user's current viewport
- Place important elements near existing content

### 3. Use Appropriate Element Types
- **Sticky notes**: Quick facts, names, dates
- **Articles**: Detailed research, news articles, reports  
- **Text**: Longer explanations or summaries
- **Images**: Photos, diagrams, evidence

### 4. Create Meaningful Connections
- Connect cause and effect
- Show timelines with chronological connections
- Link people to events or organizations
- Connect questions to potential answers

### 5. Guide User Attention
Use `move_viewport` after making significant changes:
```
Let me focus your view on the new timeline I created.
```

### 6. Explain Your Actions
Always explain what you're doing and why:
```
I'm creating a red sticky note for this urgent information since it requires immediate attention.
```

## Example AI Interaction

```
User: "Research the Kennedy assassination and create a timeline"

AI: "I'll help you research the Kennedy assassination and create a visual timeline. Let me start by checking your current board.

[Uses read_board tool]

I can see you have an empty board. I'll create a comprehensive timeline of key events. Let me start with the main event and work outward:

[Uses create_element to add JFK assassination sticky note]
[Uses create_element to add timeline elements]
[Uses create_connection to link chronological events]
[Uses move_viewport to focus on the timeline]

I've created a timeline with the key events surrounding the Kennedy assassination. The red sticky note marks the assassination itself, with yellow notes for the events leading up to it and blue notes for the aftermath and investigation. The connections show the chronological flow of events.

Would you like me to add more details about any specific aspect?"
```

## Technical Implementation

### Authentication
All tools require user authentication via the existing auth system.

### Rate Limiting
Tool usage follows the same rate limiting as AI chat (5 messages per board per day).

### Error Handling
Tools include comprehensive error handling and user feedback via toasts.

### Real-time Sync
Tool changes are immediately synced to other users via the existing real-time system.

### Viewport Movement
Client-side events trigger smooth viewport transitions with visual feedback.

## Future Enhancements

Potential additions:
- Batch operations for multiple elements
- Template-based element creation
- Advanced search with filters
- Export tools for sharing board state
- Integration with external data sources
- Voice-to-board transcription tools

## Troubleshooting

### Tool Not Working
1. Check browser console for errors
2. Verify board ID is valid
3. Ensure user has board access permissions
4. Check rate limiting status

### Viewport Not Moving
1. Verify element ID exists on board
2. Check browser console for event dispatch logs
3. Ensure coordinates are within board bounds

### Elements Not Appearing
1. Check if element was created successfully in tool response
2. Verify position coordinates are reasonable
3. Check for real-time sync issues 