import React, { useState, useRef, useEffect } from 'react';
import { motion } from '../../../../utils/MotionWrapper';
import { Plus, StickyNote, Type, Pin, Newspaper, X as CloseIcon, Image, Pencil, Minus, Palette, Eraser, Move, Trash2, <PERSON>rkles, Share2, Square, Download, ChevronRight } from 'lucide-react';
import { Plus as PlusIcon } from 'lucide-react';

import ToolButton from './ToolButton';
import ColorPicker from './ColorPicker';
import { Position } from '../../../../types';
import ShareModal from '../../board/ShareModal';
import { useMediaQuery } from '../../../../hooks/useMediaQuery';

// Define Props Interface
interface ToolbarProps {
  onAddItem: (type: string, position?: Position, props?: Record<string, any>) => Promise<any>;
  onToggleConnectMode: () => void;
  isConnectMode: boolean;
  onSave?: () => void;
  lastSaved?: Date | null;
  boardName?: string;
  onTogglePenMode: () => void;
  isPenModeActive: boolean;
  openArticleForm: () => void;
  openImageUploadModal: () => void;
  penColor: string;
  penStrokeWidth: number;
  onPenColorChange: (color: string) => void;
  onPenStrokeWidthChange: (width: number) => void;
  isErasing: boolean;
  onToggleEraserMode: () => void;
  onGrabModeToggle: () => void;
  isPublic?: boolean;
  onPublicStatusChange?: (isPublic: boolean) => void;
  boardId?: string | null;
  onToggleSelectionMode: () => void;
  isSelectionModeActive: boolean;
  onDownloadBoardData: () => void;
}

// Preset colors for the pen
const penColors = ['#000000', '#FF0000', '#0000FF', '#008000', '#FFFF00', '#FFFFFF']; // Black, Red, Blue, Green, Yellow, White
// Preset color names for sticky notes
const stickyColorNames = ['yellow', 'red', 'blue']; 
// Mapping for sticky note *display* colors
const stickyDisplayColors: { [key: string]: string } = {
  yellow: '#D1BCAA',
  red: '#d63031',
  blue: '#00ADB5' // Default blue used before
};

const strokeWidthOptions = [1, 2, 4, 8, 12];

const Toolbar: React.FC<ToolbarProps> = ({
  onAddItem,
  onToggleConnectMode,
  isConnectMode,
  onSave,
  lastSaved,
  boardName,
  onTogglePenMode,
  isPenModeActive,
  openArticleForm,
  openImageUploadModal,
  penColor,
  penStrokeWidth,
  onPenColorChange,
  onPenStrokeWidthChange,
  isErasing,
  onToggleEraserMode,
  onGrabModeToggle,
  isPublic = false,
  onPublicStatusChange,
  boardId = null,
  onToggleSelectionMode,
  isSelectionModeActive,
  onDownloadBoardData
}) => {
  const [isToolbarOpen, setIsToolbarOpen] = useState(true);
  const [currentStickyColorName, setCurrentStickyColorName] = useState('yellow'); // State tracks name
  const [isShareModalOpen, setIsShareModalOpen] = useState(false);
  const isMobile = useMediaQuery('(max-width: 768px)');
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  
  useEffect(() => {
    // If we switch to desktop view, close the mobile overflow menu
    if (!isMobile) {
      setIsMobileMenuOpen(false);
    }
  }, [isMobile]);
  
  // Debug logging for isPublic prop
  useEffect(() => {
    console.log("Toolbar isPublic prop:", isPublic);
  }, [isPublic]);
  
  // Color options for sticky notes
  const stickyColors = ['yellow', 'red', 'blue'];

  // Calculate display colors for sticky notes
  const stickyPickerDisplayColors = stickyColorNames.map(name => stickyDisplayColors[name] || '#ccc'); // Fallback grey
  // Get the display color corresponding to the current sticky name
  const currentStickyDisplayColor = stickyDisplayColors[currentStickyColorName];

  // Handle adding a new sticky note
  const handleAddSticky = () => {
    if (isPenModeActive) return;
    // Use the *name* when adding the item
    onAddItem('sticky', undefined, { color: currentStickyColorName }); 
  };

  // Handle adding a new text note
  const handleAddText = () => {
    if (isPenModeActive) return;
    onAddItem('text');
  };

  // Handle adding a new article
  const handleAddArticle = () => {
    if (isPenModeActive) return;
    openArticleForm();
  };

  // Handle adding a new image
  const handleAddImage = () => {
    if (isPenModeActive) return;
    openImageUploadModal();
  };

  // Handle toggling connect mode
  const handleToggleConnect = () => {
    if (isPenModeActive) return;
    onToggleConnectMode();
  };
  
  // Handle toggling selection mode
  const handleToggleSelectionMode = () => {
    // Ensure other modes are off, or handle conflicts as needed
    if (isPenModeActive || isConnectMode) {
      // Optionally, turn off pen/connect mode here or disable selection mode button
      console.warn("Selection mode cannot be activated while pen or connect mode is active.");
      return;
    }
    onToggleSelectionMode();
  };

  // Handle toggling pen mode
  const handleTogglePen = () => {
    if (isConnectMode) return;
    onTogglePenMode();
  };

  // Handlers for stroke width buttons
  const increaseStrokeWidth = () => {
    const currentIndex = strokeWidthOptions.indexOf(penStrokeWidth);
    const nextIndex = Math.min(currentIndex + 1, strokeWidthOptions.length - 1);
    onPenStrokeWidthChange(strokeWidthOptions[nextIndex]);
  };

  const decreaseStrokeWidth = () => {
    const currentIndex = strokeWidthOptions.indexOf(penStrokeWidth);
    const nextIndex = Math.max(currentIndex - 1, 0);
    onPenStrokeWidthChange(strokeWidthOptions[nextIndex]);
  };

  // Toggle Eraser handler
  const handleToggleEraser = () => {
    onToggleEraserMode();
    // Potentially turn off color/width selection UI if needed
  };

  // Handle share MODAL toggle
  const handleToggleShareModal = () => {
    setIsShareModalOpen(!isShareModalOpen);
  };

  // Async wrapper for onPublicStatusChange to match ShareModal prop type
  const handlePublicStatusChange = async (newStatus: boolean) => {
    if (onPublicStatusChange) {
      onPublicStatusChange(newStatus);
    }
    // Optionally, close the modal after the status changes
    // setIsShareModalOpen(false);
  };

  // Handle downloading board data
  const handleDownloadBoardData = () => {
    if (isPenModeActive || isConnectMode) {
      console.warn("Cannot download board data while pen or connect mode is active.");
      return;
    }
    onDownloadBoardData();
  };

  const renderPenToolbar = () => {
    const desktopTools = (
      <>
        {/* Grab/Pan Button (Exits Pen Mode) */}
        <ToolButton
          icon={<Move size={20} />} 
          label={"Pan Board"} 
          onClick={onGrabModeToggle}
          isActive={false}
        />
        <div className="h-6 w-px bg-white/20" /> 

        {/* Pen Button */}
        <ToolButton
          icon={<Pencil size={20} />} 
          label={"Draw"}
          onClick={handleTogglePen}
          isActive={!isErasing}
          disabled={false}
        />
        
        {/* Eraser Button (changed icon) */}
        <ToolButton
          icon={<Trash2 size={20} />}
          label={isErasing ? "Stop Deleting" : "Delete Strokes"}
          onClick={handleToggleEraser}
          isActive={isErasing} 
          disabled={false}
        />
        
        {/* Pen Color Picker (Disabled when erasing) */}
        <div className="h-6 w-px bg-white/20" />
        <ColorPicker 
          colors={penColors} 
          currentColor={penColor} 
          onColorChange={onPenColorChange} 
          disabled={isErasing}
        />
        
        {/* Pen Width Controls (Disabled when erasing) */}
        <div className="h-6 w-px bg-white/20" />
        <ToolButton 
          icon={<Minus size={16} />}
          label="Decrease Width"
          onClick={decreaseStrokeWidth}
          disabled={isErasing || penStrokeWidth === strokeWidthOptions[0]}
        />
        <span className="text-white/70 text-xs w-4 text-center">{penStrokeWidth}</span>
        <ToolButton 
          icon={<PlusIcon size={16} />} 
          label="Increase Width"
          onClick={increaseStrokeWidth}
          disabled={isErasing || penStrokeWidth === strokeWidthOptions[strokeWidthOptions.length - 1]}
        />
      </>
    );

    const mobilePrimaryTools = (
       <>
        <ToolButton
          icon={<Move size={20} />} 
          label={"Pan Board"} 
          onClick={onGrabModeToggle}
          isActive={false}
        />
        <ToolButton
          icon={<Pencil size={20} />} 
          label={"Draw"}
          onClick={handleTogglePen}
          isActive={!isErasing}
        />
        <ToolButton
          icon={<Trash2 size={20} />}
          label={"Delete"}
          onClick={handleToggleEraser}
          isActive={isErasing} 
        />
       </>
    );

    return isMobile ? mobilePrimaryTools : desktopTools;
  };
  
  const renderMainToolbar = () => {
    const desktopTools = (
      <>
        <ToolButton
          icon={<StickyNote size={20} />}
          label="Add Sticky"
          onClick={handleAddSticky}
          disabled={isConnectMode}
        />

        <ColorPicker 
          colors={stickyPickerDisplayColors}
          currentColor={currentStickyDisplayColor}
          onColorChange={(displayColor) => {
            const index = stickyPickerDisplayColors.indexOf(displayColor);
            if (index !== -1) {
              setCurrentStickyColorName(stickyColorNames[index]);
            }
          }} 
          disabled={isConnectMode}
        />

        <div className="h-6 w-px bg-white/20" />

        <ToolButton icon={<Type size={20} />} label="Add Text" onClick={handleAddText} disabled={isConnectMode} />
        <ToolButton icon={<Newspaper size={20} />} label="Add Article" onClick={handleAddArticle} disabled={isConnectMode} />
        <ToolButton icon={<Image size={20} />} label="Add Image" onClick={handleAddImage} disabled={isConnectMode} />
        <div className="h-6 w-px bg-white/20" />
        <ToolButton icon={<Pencil size={20} />} label="Draw Mode" onClick={handleTogglePen} disabled={isSelectionModeActive} />
        <ToolButton icon={<Pin size={20} />} label={isConnectMode ? "Cancel Connection" : "Connect Items"} onClick={handleToggleConnect} isActive={isConnectMode} disabled={isSelectionModeActive} />
        <ToolButton icon={<Square size={20} />} label={isSelectionModeActive ? "Cancel Selection" : "Select Items"} onClick={handleToggleSelectionMode} isActive={isSelectionModeActive} disabled={isPenModeActive || isConnectMode} />
        <div className="h-6 w-px bg-white/20" />
        <ToolButton icon={<Share2 size={20} />} label="Share Board" onClick={handleToggleShareModal} disabled={isConnectMode} />
      </>
    );
    
    const mobilePrimaryTools = (
      <>
        <ToolButton icon={<StickyNote size={20} />} label="Add Sticky" onClick={handleAddSticky} disabled={isConnectMode} />
        <ToolButton icon={<Type size={20} />} label="Add Text" onClick={handleAddText} disabled={isConnectMode} />
        <ToolButton icon={<Image size={20} />} label="Add Image" onClick={handleAddImage} disabled={isConnectMode} />
        <ToolButton icon={<Pencil size={20} />} label="Draw" onClick={handleTogglePen} disabled={isSelectionModeActive} />
      </>
    );

    return isMobile ? mobilePrimaryTools : desktopTools;
  }
  
  const renderMobileSecondaryToolbar = () => {
    if (!isMobile || !isMobileMenuOpen) return null;

    const penSecondaryTools = (
      <>
        <div className="h-6 w-px bg-white/20" />
        <ColorPicker 
          colors={penColors} 
          currentColor={penColor} 
          onColorChange={onPenColorChange} 
          disabled={isErasing}
        />
        <div className="h-6 w-px bg-white/20" />
        <ToolButton 
          icon={<Minus size={16} />}
          label="Decrease Width"
          onClick={decreaseStrokeWidth}
          disabled={isErasing || penStrokeWidth === strokeWidthOptions[0]}
        />
        <span className="text-white/70 text-xs w-4 text-center">{penStrokeWidth}</span>
        <ToolButton 
          icon={<PlusIcon size={16} />} 
          label="Increase Width"
          onClick={increaseStrokeWidth}
          disabled={isErasing || penStrokeWidth === strokeWidthOptions[strokeWidthOptions.length - 1]}
        />
      </>
    );
    
    const mainSecondaryTools = (
      <>
        <ColorPicker 
          colors={stickyPickerDisplayColors}
          currentColor={currentStickyDisplayColor}
          onColorChange={(displayColor) => {
            const index = stickyPickerDisplayColors.indexOf(displayColor);
            if (index !== -1) {
              setCurrentStickyColorName(stickyColorNames[index]);
            }
          }} 
          disabled={isConnectMode}
        />
        <div className="h-6 w-px bg-white/20" />
        <ToolButton icon={<Newspaper size={20} />} label="Add Article" onClick={handleAddArticle} disabled={isConnectMode} />
        <ToolButton icon={<Pin size={20} />} label="Connect" onClick={handleToggleConnect} isActive={isConnectMode} disabled={isSelectionModeActive} />
        <ToolButton icon={<Square size={20} />} label="Select" onClick={handleToggleSelectionMode} isActive={isSelectionModeActive} disabled={isPenModeActive || isConnectMode} />
        <ToolButton icon={<Share2 size={20} />} label="Share" onClick={handleToggleShareModal} disabled={isConnectMode} />
      </>
    );

    return (
      <motion.div
        className="flex items-center justify-center space-x-3 px-2 mb-2"
        data-nodrag="true"
        data-toolbar="true"
        initial={{ y: 10, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        exit={{ y: 10, opacity: 0 }}
        transition={{ duration: 0.2 }}
      >
        {isPenModeActive ? penSecondaryTools : mainSecondaryTools}
      </motion.div>
    );
  };

  return (
    <>
      <div
        className="fixed bottom-8 left-1/2 transform -translate-x-1/2 z-50 p-2 glass-morphism rounded-xl flex flex-col items-center"
        data-nodrag="true"
        data-toolbar="true"
      >
        {renderMobileSecondaryToolbar()}

        <motion.div
          className="flex items-center space-x-3 px-2"
          data-nodrag="true"
          data-toolbar="true"
          initial={{ y: 50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.2, duration: 0.3 }}
        >
          {isPenModeActive ? renderPenToolbar() : (isToolbarOpen && renderMainToolbar())}
          
          {isMobile && isToolbarOpen && (
            <>
              <div className="h-6 w-px bg-white/20" />
              <ToolButton
                icon={isMobileMenuOpen ? <CloseIcon size={20} /> : <ChevronRight size={20} />}
                label={isMobileMenuOpen ? 'Less' : 'More'}
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              />
            </>
          )}
        </motion.div>
      </div>

      {/* Conditionally render the ShareModal */}
      {isShareModalOpen && boardId && ( // Ensure boardId exists before rendering
        <ShareModal
          isOpen={isShareModalOpen}
          onClose={() => setIsShareModalOpen(false)} // Provide a way to close the modal
          boardId={boardId}
          isPublic={isPublic}
          onPublicStatusChange={handlePublicStatusChange} // Pass the async handler
        />
      )}
    </>
  );
};

export default Toolbar; 