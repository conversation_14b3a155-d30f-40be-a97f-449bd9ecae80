.boardCanvas {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  touch-action: none; /* Disable browser's default touch handling */
  will-change: transform; /* Performance hint for browsers */
  -webkit-user-select: none; /* Prevent text selection on mobile */
  user-select: none;
  -webkit-touch-callout: none; /* Prevent callout to copy image, etc on iOS */
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0); /* Remove tap highlight on mobile */

  /* Mobile-specific optimizations */
  -webkit-overflow-scrolling: touch;
  -webkit-transform: translate3d(0, 0, 0); /* Force hardware acceleration */
  transform: translate3d(0, 0, 0);

  /* Prevent zoom on double-tap for mobile browsers */
  -ms-touch-action: none;
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  user-drag: none;
}

.boardMotionContainer {
  position: absolute;
  width: 100%; 
  height: 100%;
  transform-origin: 0 0; /* Set the transform origin to top-left */
  will-change: transform, scale; /* Optimize for transforms and scale changes */
  -webkit-backface-visibility: hidden; /* Prevent flickering on some browsers */
  backface-visibility: hidden;
}

/* For iOS momentum scrolling prevention */
@supports (-webkit-overflow-scrolling: touch) {
  .boardCanvas {
    overscroll-behavior: none;
  }
}

/* For devices that support it, enable hardware acceleration */
.boardMotionContainer {
  transform: translate3d(0, 0, 0);
}

/* Optimize for touch devices */
@media (pointer: coarse) {
  .boardCanvas {
    cursor: grab;
  }
  
  .boardCanvas:active {
    cursor: grabbing;
  }
} 