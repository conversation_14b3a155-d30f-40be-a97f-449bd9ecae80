import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import type { Database } from '@/lib/database.types';

import { getAuthenticatedUser } from '@/utils/authUtils';
export async function POST(request: NextRequest) {
  try {
    const { boardId, makePublic = true } = await request.json();
    
    if (!boardId) {
      return NextResponse.json({ error: 'Board ID is required' }, { status: 400 });
    }

    const { user, error: authError, supabase } = await getAuthenticatedUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if the user owns the board
    const { data: board, error: boardError } = await supabase
      .from('boards')
      .select('id, user_id')
      .eq('id', boardId)
      .eq('user_id', user.id)
      .maybeSingle();

    if (boardError || !board) {
      return NextResponse.json({ 
        error: 'Board not found or you do not have permission to update its visibility' 
      }, { status: 403 });
    }

    // Check if a sharing record already exists
    const { data: existingSharing } = await supabase
      .from('board_sharing')
      .select('id')
      .eq('board_id', boardId)
      .maybeSingle();

    let result;
    
    if (existingSharing) {
      // Update existing record
      result = await supabase
        .from('board_sharing')
        .update({
          public_board: makePublic,
          updated_at: new Date().toISOString()
        })
        .eq('id', existingSharing.id)
        .select();
    } else if (makePublic) {
      // Only create a new record if we're making the board public
      result = await supabase
        .from('board_sharing')
        .insert({
          board_id: boardId,
          public_board: true,
          likes: 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select();
    } else {
      // If trying to make private but no record exists, that's already the default state
      return NextResponse.json({ 
        success: true, 
        message: 'Board is already private (no sharing record exists)'
      }, { status: 200 });
    }

    if (result.error) {
      return NextResponse.json({ error: result.error.message }, { status: 500 });
    }

    return NextResponse.json({ 
      success: true, 
      message: makePublic ? 'Board is now public' : 'Board is now private',
      data: result.data[0]
    }, { status: 200 });
    
  } catch (error) {
    console.error('Error updating board visibility:', error);
    return NextResponse.json({ 
      error: 'An unexpected error occurred' 
    }, { status: 500 });
  }
} 