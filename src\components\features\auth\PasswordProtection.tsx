'use client';

import React, { useState } from 'react';
import { Loader2 } from 'lucide-react';

interface PasswordProtectionProps {
  onAuthenticated: () => void;
}

const PasswordProtection: React.FC<PasswordProtectionProps> = ({ onAuthenticated }) => {
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    
    // Simple password check for development environment
    const correctPassword = process.env.NEXT_PUBLIC_DEV_PASSWORD || 'detective';
    
    if (password === correctPassword) {
      localStorage.setItem('dev_password_auth', 'true');
      onAuthenticated();
    } else {
      setError('Incorrect password');
      setLoading(false);
    }
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-gradient-to-br from-noir to-noir-800 p-4 text-white">
      <div className="w-full max-w-md space-y-8 bg-noir-700 p-8 rounded-lg shadow-lg border border-white/10">
        <div className="text-center">
          <h1 className="text-4xl font-bold mb-2">
            <span className="text-noir-accent">DETECTIVE</span> BOARD
          </h1>
          <p className="text-sm text-gray-400">Development Environment</p>
        </div>
        
        <form onSubmit={handleSubmit} className="mt-8 space-y-6">
          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-300 mb-2">
              Password
            </label>
            <input
              id="password"
              name="password"
              type="password"
              required
              className="appearance-none relative block w-full px-3 py-2 border border-white/10 rounded-md bg-black/20 text-gray-300 focus:outline-none focus:ring-2 focus:ring-noir-accent focus:border-transparent"
              placeholder="Enter password"
              value={password}
              onChange={(e) => {
                setPassword(e.target.value);
                setError('');
              }}
            />
            {error && <p className="mt-2 text-sm text-red-500">{error}</p>}
          </div>

          <div>
            <button
              type="submit"
              disabled={loading}
              className="group relative flex w-full justify-center rounded-md border border-transparent bg-noir-accent px-4 py-2 text-sm font-medium text-white hover:bg-noir-accent/80 focus:outline-none focus:ring-2 focus:ring-noir-accent focus:ring-offset-2 disabled:opacity-50"
            >
              {loading ? (
                <Loader2 className="h-5 w-5 animate-spin" />
              ) : (
                'Unlock'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default PasswordProtection; 