'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Search, Heart, MessageSquare, Clock, Loader2 } from 'lucide-react';
import PopularBoards from '@/components/features/dashboard/PopularBoards';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { PublicBoard } from '@/hooks/usePublicBoards';
import { useQueryClient } from '@tanstack/react-query';

interface PublicBoardsClientProps {
  initialData?: {
    boards: PublicBoard[];
    pagination: {
      total: number;
      offset: number;
      limit: number;
      hasMore: boolean;
    };
  };
  initialSearchQuery?: string;
  initialSortBy?: string;
  initialSortOrder?: string;
  initialTimePeriod?: string;
}

export function PublicBoardsClient({
  initialData,
  initialSearchQuery = '',
  initialSortBy = 'likes',
  initialSortOrder = 'desc',
  initialTimePeriod = 'allTime'
}: PublicBoardsClientProps) {
  const router = useRouter();
  const queryClient = useQueryClient();
  
  const [searchQuery, setSearchQuery] = useState(initialSearchQuery);
  const [sortBy, setSortBy] = useState(initialSortBy);
  const [sortOrder, setSortOrder] = useState(initialSortOrder);
  const [timePeriod, setTimePeriod] = useState(initialTimePeriod);
  const [isTimePeriodOpen, setIsTimePeriodOpen] = useState(false);
  const [showPopular, setShowPopular] = useState(false);
  const [searchFocused, setSearchFocused] = useState(false);
  const [isNavigatingHome, setIsNavigatingHome] = useState(false);

  const handleSearch = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    // Update the URL with query parameters
    const params = new URLSearchParams();
    if (searchQuery) params.set('search', searchQuery);
    if (sortBy !== 'likes') params.set('sortBy', sortBy);
    if (sortOrder !== 'desc') params.set('sortOrder', sortOrder);
    if (timePeriod !== 'allTime') params.set('timePeriod', timePeriod);
    
    const newUrl = `/public-boards${params.toString() ? `?${params.toString()}` : ''}`;
    router.push(newUrl);
    
    // Invalidate queries to refetch with new parameters
    queryClient.invalidateQueries({
      queryKey: ['publicBoards']
    });
  };

  const handleNavigateHome = () => {
    setIsNavigatingHome(true);
    router.push('/');
  };

  const getSortByIcon = () => {
    switch (sortBy) {
      case 'likes':
        return <Heart size={16} />;
      case 'comments':
        return <MessageSquare size={16} />;
      case 'recent':
        return <Clock size={16} />;
      default:
        return <Heart size={16} />;
    }
  };
  
  const handleTimeFilter = (value: string) => {
    setTimePeriod(value);
    setIsTimePeriodOpen(false);
    
    // Update URL
    const params = new URLSearchParams();
    if (searchQuery) params.set('search', searchQuery);
    if (sortBy !== 'likes') params.set('sortBy', sortBy);
    if (sortOrder !== 'desc') params.set('sortOrder', sortOrder);
    if (value !== 'allTime') params.set('timePeriod', value);
    
    const newUrl = `/public-boards${params.toString() ? `?${params.toString()}` : ''}`;
    router.push(newUrl);
    
    // Invalidate queries
    queryClient.invalidateQueries({
      queryKey: ['publicBoards']
    });
  };

  return (
    <div className="w-full min-h-screen px-4 py-12 text-white">
      <div className="max-w-6xl mx-auto animate-fade-in">
        <div className="flex items-center mb-2">
          <Button 
            variant="ghost" 
            className="mr-4 text-white"
            onClick={handleNavigateHome}
            disabled={isNavigatingHome}
          >
            {isNavigatingHome ? (
              <span className="flex items-center justify-center">
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Navigating...
              </span>
            ) : (
              <span className="flex items-center">
                <ArrowLeft className="mr-2" size={20} />
                Back to Home
              </span>
            )}
          </Button>
        </div>

        {/* Updated Header with two-tone treatment */}
        <div className="mb-8 text-center">
          <h1 className="text-5xl md:text-7xl font-bold mb-4 tracking-tight detective-text animate-fade-up">
            <span className="text-white">PUBLIC</span> <span className="text-noir-accent">BOARDS</span>
          </h1>
          <div className="w-32 h-1 bg-noir-accent mx-auto mb-8"></div>
          <p className="text-gray-400 text-lg">
            Explore detective boards shared by the community.
          </p>
        </div>
        
        {/* Updated Search and Filters - Unified Controls Bar with sticky positioning */}
        <div className="sticky top-0 z-10 mb-8 bg-black/60 backdrop-blur-sm p-5 rounded-lg border border-white/10 shadow-lg">
          <form onSubmit={handleSearch} className="flex flex-col md:flex-row gap-4">
            <div className="relative flex-grow group">
              <Input
                type="text"
                placeholder="Search for board by name..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onFocus={() => setSearchFocused(true)}
                onBlur={() => setSearchFocused(false)}
                className={`pl-10 h-10 w-full bg-white/5 border-white/15 text-white rounded-lg 
                            placeholder:text-white/50 transition-all duration-200
                            focus:outline-none focus:border-noir-accent focus:ring-2 focus:ring-noir-accent/40
                            ${searchFocused ? 'border-noir-accent' : 'border-white/15'}`}
              />
              <Search 
                className={`absolute left-3 top-1/2 transform -translate-y-1/2 transition-colors duration-200
                            ${searchFocused ? 'text-noir-accent' : 'text-white/70'} 
                            group-hover:text-noir-accent`} 
                size={16} 
              />
            </div>
            {/* Time Period Filter - Custom */}
            <div className="relative group w-full md:w-[180px]">
              <SelectTrigger 
                onClick={() => setIsTimePeriodOpen(!isTimePeriodOpen)}
                className="h-10 bg-white/5 border-white/15 text-white rounded-lg 
                             placeholder:text-white/50 transition-all duration-200 flex items-center justify-between px-3
                             focus:outline-none focus:border-noir-accent focus:ring-2 focus:ring-noir-accent/40
                             hover:border-noir-accent group-hover:border-noir-accent cursor-pointer"
              >
                <SelectValue>
                  {timePeriod === 'allTime' ? 'All Time' :
                   timePeriod === 'last7days' ? 'Last 7 Days' :
                   'Last 30 Days'}
                </SelectValue>
              </SelectTrigger>
              {isTimePeriodOpen && (
                <SelectContent 
                  className="absolute top-full mt-1 w-full bg-black/80 backdrop-blur-md border-white/20 text-white rounded-md shadow-lg z-20 py-1"
                >
                  <SelectItem 
                    value="allTime" 
                    onClick={() => handleTimeFilter('allTime')}
                    className="px-3 py-2 hover:bg-noir-accent/80 focus:bg-noir-accent/90 cursor-pointer transition-colors duration-150"
                  >
                    All Time
                  </SelectItem>
                  <SelectItem 
                    value="last7days" 
                    onClick={() => handleTimeFilter('last7days')}
                    className="px-3 py-2 hover:bg-noir-accent/80 focus:bg-noir-accent/90 cursor-pointer transition-colors duration-150"
                  >
                    Last 7 Days
                  </SelectItem>
                  <SelectItem 
                    value="last30days" 
                    onClick={() => handleTimeFilter('last30days')}
                    className="px-3 py-2 hover:bg-noir-accent/80 focus:bg-noir-accent/90 cursor-pointer transition-colors duration-150"
                  >
                    Last 30 Days
                  </SelectItem>
                </SelectContent>
              )}
            </div>
            <Button 
              type="submit" 
              className="bg-noir-accent hover:bg-noir-accent/90 ml-auto md:ml-0"
            >
              Search
            </Button>
          </form>
        </div>
        
        {/* Main board listing */}
        <div className="p-8 rounded-lg neo-blur bg-white/5 animate-fade-up" style={{ animationDelay: '0.2s' }}>
          <h2 className="text-3xl font-bold mb-6 flex items-center">
            {getSortByIcon()}
            <span className="ml-3">
              {sortBy === 'likes' ? 'Detective Boards' : 
               sortBy === 'comments' ? 'Most Commented Boards' : 'Recently Shared Boards'}
            </span>
          </h2>
          <div className="w-24 h-1 bg-noir-accent mb-8"></div>
          <PopularBoards 
            limit={12} 
            searchQuery={searchQuery}
            sortBy={sortBy}
            sortOrder={sortOrder}
            timePeriod={timePeriod}
            initialData={initialData}
          />
        </div>
      </div>
    </div>
  );
} 