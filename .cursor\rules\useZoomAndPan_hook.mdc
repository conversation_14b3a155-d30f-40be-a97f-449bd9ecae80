---
description: overview of useZoomAndPan Hook
globs: 
alwaysApply: false
---
# useZoomAndPan Hook

This file contains the `useZoomAndPan` custom React hook, responsible for managing the zoom (scale) and pan (position) state of the main detective board canvas.

Find the hook implementation here: [src/hooks/useZoomAndPan.ts](mdc:src/hooks/useZoomAndPan.ts)

## Key Functionalities

- **Zooming:** <PERSON>les zooming via the mouse wheel (`handleWheel`). Zooming is centered around the cursor position and constrained between `MIN_SCALE` and `MAX_SCALE`.
- **Panning:** Enables panning the canvas by clicking and dragging the background (`handleCanvasPointerDown`, `handleCanvasPointerMove`, `handleCanvasPointerUp`). Panning is intentionally prevented if the drag interaction starts on an element with the class `.board-item-draggable`.
- **Constraints:** Utilizes the `constrainPosition` function to keep the board within predefined virtual boundaries (`BOARD_WIDTH`, `BOARD_HEIGHT`, `BOARD_PADDING`), ensuring the user cannot pan excessively far off the main content area.
- **Initialization & Reset:** Calculates an initial centered position (`calculateCenteredPosition`) on mount and provides a `resetView` function to return to this default state.

## Returned Values & Functions

The hook returns an object containing:
- `scale`: Current zoom level.
- `position`: Current `{ x, y }` position of the board.
- `isGrabbing`: Boolean indicating if a pan operation is active (useful for cursor styling).
- `setScale`: Function to manually set the zoom level.
- `setPosition`: Function to manually set the board position.
- `handleWheel`: Event handler for zoom functionality.
- `handleCanvasPointerDown`: Event handler to initiate panning.
- `resetView`: Function to reset zoom and pan to the initial state.
- `boardDimensions`: Object containing the configured `BOARD_WIDTH` and `BOARD_HEIGHT`.

