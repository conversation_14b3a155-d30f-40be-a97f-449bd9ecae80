'use client';

import Script from 'next/script';
import React from 'react';

export function GoogleGsiClientLoader() {
  return (
    <Script
      src="https://accounts.google.com/gsi/client"
      strategy="afterInteractive"
      onLoad={() => {
        (window as any).googleGsiClientLoaded = true;
        console.log('Google GSI client loaded via client component.');
      }}
      onError={(e) => {
        console.error('Error loading Google GSI client:', e);
        (window as any).googleGsiClientLoaded = false;
      }}
    />
  );
} 