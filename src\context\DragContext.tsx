import React, { createContext, useContext, useState, useCallback } from 'react';
import { Position } from '../types/board';

interface DragState {
  // Maps item IDs to their real-time positions during drag
  dragPositions: Record<string, Position>;
  // Whether an item is currently being dragged
  isDragging: Record<string, boolean>;
}

interface DragContextValue extends DragState {
  // Update position for an item being dragged
  updateDragPosition: (id: string, position: Position) => void;
  // Set whether an item is being dragged or not
  setItemDragging: (id: string, isDragging: boolean) => void;
  // Get the current position of an item (with fallback)
  getItemPosition: (id: string, fallbackPosition: Position) => Position;
  // Check if an item is being dragged
  isItemDragging: (id: string) => boolean;
  // Check if any item is currently being dragged
  isAnyItemDragging: () => boolean;
}

const initialDragState: DragState = {
  dragPositions: {},
  isDragging: {}
};

const DragContext = createContext<DragContextValue | undefined>(undefined);

export function useDrag() {
  const context = useContext(DragContext);
  if (!context) {
    throw new Error('useDrag must be used within a DragProvider');
  }
  return context;
}

export const DragProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, setState] = useState<DragState>(initialDragState);

  // Update position for an item being dragged
  const updateDragPosition = useCallback((id: string, position: Position) => {
    setState(prev => ({
      ...prev,
      dragPositions: {
        ...prev.dragPositions,
        [id]: position
      }
    }));
  }, []);

  // Set whether an item is being dragged or not
  const setItemDragging = useCallback((id: string, isDragging: boolean) => {
    setState(prev => {
      // When drag ends, clean up the position data
      const newDragPositions = { ...prev.dragPositions };
      const newIsDragging = { ...prev.isDragging };
      
      if (!isDragging) {
        delete newDragPositions[id];
      }
      
      newIsDragging[id] = isDragging;
      
      return {
        dragPositions: newDragPositions,
        isDragging: newIsDragging
      };
    });
  }, []);

  // Get the current position of an item (with fallback)
  const getItemPosition = useCallback((id: string, fallbackPosition: Position): Position => {
    return state.dragPositions[id] || fallbackPosition;
  }, [state.dragPositions]);

  // Check if an item is being dragged
  const isItemDragging = useCallback((id: string): boolean => {
    return !!state.isDragging[id];
  }, [state.isDragging]);

  // Check if any item is currently being dragged
  const isAnyItemDragging = useCallback((): boolean => {
    return Object.values(state.isDragging).some(isDragging => isDragging);
  }, [state.isDragging]);

  const value: DragContextValue = {
    ...state,
    updateDragPosition,
    setItemDragging,
    getItemPosition,
    isItemDragging,
    isAnyItemDragging
  };

  return (
    <DragContext.Provider value={value}>
      {children}
    </DragContext.Provider>
  );
}; 