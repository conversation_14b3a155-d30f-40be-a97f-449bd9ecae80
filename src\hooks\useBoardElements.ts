'use client';

import { useState, useCallback } from 'react';
import { useApi } from './useApi';
import { BoardElement, BoardConnection } from './useBoards';

interface ElementPosition {
  x: number;
  y: number;
}

interface ElementSize {
  width: number;
  height: number;
}

interface UseBoardElements {
  elements: BoardElement[];
  connections: BoardConnection[];
  loading: boolean;
  error: Error | null;
  fetchElements: (boardId: string) => Promise<void>;
  addElement: (
    boardId: string,
    type: BoardElement['type'],
    content: string,
    position: ElementPosition,
    size?: ElementSize,
    color?: string
  ) => Promise<BoardElement | null>;
  updateElement: (
    boardId: string,
    elementId: string,
    updates: Partial<Omit<BoardElement, 'id' | 'createdAt' | 'updatedAt'>>
  ) => Promise<BoardElement | null>;
  deleteElement: (boardId: string, elementId: string) => Promise<boolean>;
  addConnection: (
    boardId: string,
    sourceId: string,
    targetId: string,
    label?: string,
    color?: string
  ) => Promise<BoardConnection | null>;
  updateConnection: (
    boardId: string,
    connectionId: string,
    updates: Partial<Omit<BoardConnection, 'id' | 'sourceId' | 'targetId' | 'createdAt' | 'updatedAt'>>
  ) => Promise<BoardConnection | null>;
  deleteConnection: (boardId: string, connectionId: string) => Promise<boolean>;
}

/**
 * Hook for managing board elements and connections
 */
export function useBoardElements(): UseBoardElements {
  const [elements, setElements] = useState<BoardElement[]>([]);
  const [connections, setConnections] = useState<BoardConnection[]>([]);
  const api = useApi<any>();

  /**
   * Fetch all elements and connections for a board
   */
  const fetchElements = useCallback(
    async (boardId: string) => {
      const result = await api.fetch(`/api/board/${boardId}/elements`);
      if (result) {
        setElements(result.elements || []);
        setConnections(result.connections || []);
      }
    },
    [api]
  );

  /**
   * Add a new element to the board
   */
  const addElement = useCallback(
    async (
      boardId: string,
      type: BoardElement['type'],
      content: string,
      position: ElementPosition,
      size?: ElementSize,
      color?: string
    ): Promise<BoardElement | null> => {
      const result = await api.fetch(`/api/board/${boardId}/elements`, {
        method: 'POST',
        body: {
          type,
          content,
          position,
          size,
          color,
        },
      });

      if (result) {
        setElements((prev) => [...prev, result]);
        return result;
      }

      return null;
    },
    [api]
  );

  /**
   * Update an existing element
   */
  const updateElement = useCallback(
    async (
      boardId: string,
      elementId: string,
      updates: Partial<Omit<BoardElement, 'id' | 'createdAt' | 'updatedAt'>>
    ): Promise<BoardElement | null> => {
      const result = await api.fetch(`/api/board/${boardId}/elements/${elementId}`, {
        method: 'PATCH',
        body: updates,
      });

      if (result) {
        setElements((prev) =>
          prev.map((element) => {
            if (element.id === elementId) {
              return { ...element, ...result };
            }
            return element;
          })
        );
        return result;
      }

      return null;
    },
    [api]
  );

  /**
   * Delete an element from the board
   */
  const deleteElement = useCallback(
    async (boardId: string, elementId: string): Promise<boolean> => {
      const result = await api.fetch(`/api/board/${boardId}/elements/${elementId}`, {
        method: 'DELETE',
      });

      if (result && result.success) {
        // Remove element
        setElements((prev) => prev.filter((element) => element.id !== elementId));
        
        // Also remove any connections that use this element
        setConnections((prev) =>
          prev.filter(
            (connection) =>
              connection.sourceId !== elementId && connection.targetId !== elementId
          )
        );
        
        return true;
      }

      return false;
    },
    [api]
  );

  /**
   * Add a connection between two elements
   */
  const addConnection = useCallback(
    async (
      boardId: string,
      sourceId: string,
      targetId: string,
      label?: string,
      color?: string
    ): Promise<BoardConnection | null> => {
      const result = await api.fetch(`/api/board/${boardId}/connections`, {
        method: 'POST',
        body: {
          sourceId,
          targetId,
          label,
          color,
        },
      });

      if (result) {
        setConnections((prev) => [...prev, result]);
        return result;
      }

      return null;
    },
    [api]
  );

  /**
   * Update an existing connection
   */
  const updateConnection = useCallback(
    async (
      boardId: string,
      connectionId: string,
      updates: Partial<Omit<BoardConnection, 'id' | 'sourceId' | 'targetId' | 'createdAt' | 'updatedAt'>>
    ): Promise<BoardConnection | null> => {
      const result = await api.fetch(`/api/board/${boardId}/connections/${connectionId}`, {
        method: 'PATCH',
        body: updates,
      });

      if (result) {
        setConnections((prev) =>
          prev.map((connection) => {
            if (connection.id === connectionId) {
              return { ...connection, ...result };
            }
            return connection;
          })
        );
        return result;
      }

      return null;
    },
    [api]
  );

  /**
   * Delete a connection
   */
  const deleteConnection = useCallback(
    async (boardId: string, connectionId: string): Promise<boolean> => {
      const result = await api.fetch(`/api/board/${boardId}/connections/${connectionId}`, {
        method: 'DELETE',
      });

      if (result && result.success) {
        setConnections((prev) =>
          prev.filter((connection) => connection.id !== connectionId)
        );
        return true;
      }

      return false;
    },
    [api]
  );

  return {
    elements,
    connections,
    loading: api.loading,
    error: api.error,
    fetchElements,
    addElement,
    updateElement,
    deleteElement,
    addConnection,
    updateConnection,
    deleteConnection,
  };
} 