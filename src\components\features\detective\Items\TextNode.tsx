import React, { useState, useRef } from 'react';
import BoardItem, { BoardItemProps } from '../BoardItem';
import { Trash2 } from 'lucide-react';
import AutoFitText from '@/components/ui/AutoFitText';

interface TextNodeProps extends Omit<BoardItemProps, 'type' | 'children'> {
  onDelete: (id: string) => void;
  onContentChange: (id: string, content: string) => void;
  scale: number;
  connectMode?: boolean;
  connectStart?: string | null;
  onConnectionStart?: (id: string) => void;
  onConnectionComplete?: (id: string) => void;
  isMultiSelected?: boolean;
}

const TextNode: React.FC<TextNodeProps> = ({
  id,
  content,
  position,
  width,
  height,
  onSizeChange,
  onPositionChange,
  onSelect,
  isSelected,
  onDelete,
  onContentChange,
  scale,
  connectMode = false,
  connectStart = null,
  onConnectionStart,
  onConnectionComplete,
  isMultiSelected,
  ...rest
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [textContent, setTextContent] = useState(content);
  const originalContentRef = useRef<string | null>(null);

  const handleDoubleClick = (e: React.MouseEvent) => {
    if (connectMode) {
      e.preventDefault();
      return;
    }
    originalContentRef.current = content; // Store original content
    setIsEditing(true);
  };

  const handleBlur = () => {
    const wasEditing = isEditing; // Check if we were editing *before* setting state
    setIsEditing(false);
    
    // Only compare and update content if we were actually editing AND content changed
    if (wasEditing && textContent !== content) {
        onContentChange(id, textContent);
    }
    // Always clear the ref on blur
    originalContentRef.current = null;
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setTextContent(e.target.value);
  };

  // Auto-select all text when the textarea is focused
  const handleTextareaFocus = (e: React.FocusEvent<HTMLTextAreaElement>) => {
    e.target.select();
  };

  const preventScrollPropagation = (e: React.UIEvent) => {
    e.stopPropagation();
  };

  return (
    <BoardItem
      id={id}
      type="text"
      content={content}
      position={position}
      width={width}
      height={height}
      onSizeChange={onSizeChange}
      onPositionChange={onPositionChange}
      onSelect={onSelect}
      isSelected={isSelected}
      scale={scale}
      isEditMode={isEditing}
      connectMode={connectMode}
      connectStart={connectStart}
      onConnectionStart={onConnectionStart}
      onConnectionComplete={onConnectionComplete}
      {...rest}
    >
      <div className="w-full h-full p-2">
        <div className={`flex flex-col h-full w-full`}>
          {isSelected && !isMultiSelected && !connectMode && (
            <button
              onClick={() => onDelete(id)}
              className="absolute -top-8 right-0 text-white/50 hover:text-noir-accent transition-colors bg-noir-300 rounded-full p-1"
              data-nodrag="true"
            >
              <Trash2 size={18} />
            </button>
          )}
          {isEditing ? (
            <textarea
              autoFocus
              className="flex-1 w-full h-full bg-transparent resize-none outline-none text-white p-2 handwritten-font"
              value={textContent}
              onChange={handleInputChange}
              onBlur={handleBlur}
              onFocus={handleTextareaFocus}
              onWheel={preventScrollPropagation}
              data-nodrag="true"
            />
          ) : (
            <div
              className="flex-1 text-white handwritten-font overflow-hidden cursor-text"
              onDoubleClick={handleDoubleClick}
              onWheel={preventScrollPropagation}
              data-nodrag="true"
            >
              <AutoFitText 
                mode="multi"
                className="handwritten-font"
                style={{ width: '100%', height: '100%' }}
                parentWidth={width}
                parentHeight={height}
              >
                {textContent}
              </AutoFitText>
            </div>
          )}
        </div>
      </div>
    </BoardItem>
  );
};

export default TextNode;