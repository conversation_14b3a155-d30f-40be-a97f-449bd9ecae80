"use client"

import * as React from "react"
import { cn } from "@/lib/utils"

interface SliderProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'value' | 'defaultValue' | 'onChange'> {
  value?: number[];
  onValueChange?: (value: number[]) => void;
  defaultValue?: number[];
  min?: number;
  max?: number;
  step?: number;
}

const Slider = React.forwardRef<HTMLInputElement, SliderProps>(
  ({ className, value, onValueChange, defaultValue, min = 0, max = 100, step = 1, ...props }, ref) => {
    // Handle internal state for uncontrolled usage
    const [internalValue, setInternalValue] = React.useState<number>(
      value ? value[0] : defaultValue ? defaultValue[0] : min
    );

    // Update internal value when external value changes
    React.useEffect(() => {
      if (value) {
        setInternalValue(value[0]);
      }
    }, [value]);

    // Calculate percentage for styling
    const percentage = ((internalValue - min) / (max - min)) * 100;

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = Number(e.target.value);
      setInternalValue(newValue);
      
      if (onValueChange) {
        onValueChange([newValue]);
      }
    };

    return (
      <div className={cn('relative flex w-full touch-none select-none items-center', className)}>
        <div className="relative h-1.5 w-full overflow-hidden rounded-full bg-noir-700">
          <div 
            className="absolute h-full bg-noir-accent" 
            style={{ width: `${percentage}%` }}
          />
        </div>
        <input
          type="range"
          ref={ref}
          min={min}
          max={max}
          step={step}
          value={internalValue}
          onChange={handleChange}
          className="absolute inset-0 h-full w-full cursor-pointer opacity-0"
          {...props}
        />
        <div 
          className="absolute h-4 w-4 rounded-full border border-noir-900 bg-white shadow-lg pointer-events-none"
          style={{ left: `calc(${percentage}% - 0.5rem)` }}
        />
      </div>
    );
  }
);

Slider.displayName = 'Slider';

export { Slider } 