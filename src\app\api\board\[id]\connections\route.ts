import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import type { Database, Tables } from '@/lib/database.types';

// Import shared utility type
import { TransformedConnection } from '@/utils/apiBoardUtils';

// Define the type for a transformed connection
// This should match the type used in your main board fetching logic
// type TransformedConnection = {
//   id: string;
//   fromId: string;
//   toId: string;
//   type: string; // Or a more specific literal type if applicable
//   label: string;
//   isAiGenerated: boolean;
// };

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const cookieStore = cookies();
  const supabase = createRouteHandlerClient<Database>({ cookies: () => cookieStore });
  const boardId = params.id;

  if (!boardId) {
    return NextResponse.json({ message: 'Board ID is required' }, { status: 400 });
  }

  try {
    const { data: connections, error: connectionsError } = await supabase
      .from('connections')
      .select('*')
      .eq('board_id', boardId);

    if (connectionsError) {
      console.error(`Error fetching connections for board ${boardId}:`, connectionsError);
      return NextResponse.json({ message: 'Failed to fetch connections' }, { status: 500 });
    }

    if (!connections) {
      // Should not happen if the board exists, but handle gracefully
      return NextResponse.json({ connections: [] }, { status: 200 });
    }

    // Use TransformedConnection type from utils
    const transformedConnections: TransformedConnection[] = connections.map((connection: Tables<'connections'>) => ({
      id: connection.id,
      fromId: connection.from_element_id,
      toId: connection.to_element_id,
      type: connection.connection_type,
      label: connection.label ?? '',
      isAiGenerated: connection.is_ai_generated ?? false, // Default to false if null
    }));

    return NextResponse.json(transformedConnections, { status: 200 });

  } catch (error: any) {
    console.error(`Unexpected error fetching connections for board ${boardId}:`, error);
    return NextResponse.json({ message: 'Failed to fetch connections', error: error.message }, { status: 500 });
  }
} 