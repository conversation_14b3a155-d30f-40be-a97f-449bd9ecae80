/**
 * Uploads a board preview image to storage
 * @param blob The image blob to upload
 * @param boardId The ID of the board
 * @returns Promise that resolves to the uploaded file path
 */
export const uploadBoardPreview = async (blob: Blob, boardId: string): Promise<string | null> => {
    try {
      // Create a FormData object
      const formData = new FormData();
      const timestamp = Date.now();
      
      // Use a more descriptive filename format consistent with the server-side path
      formData.append('file', blob, `preview-${boardId}-${timestamp}.jpg`);
      formData.append('boardId', boardId);
      
      // Upload the preview image
      console.log('Uploading board preview to storage...');
      const response = await fetch('/api/storage/upload-preview', {
        method: 'POST',
        body: formData,
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        console.error(`Upload preview failed with status ${response.status}:`, errorData);
        throw new Error(errorData.message || `Failed to upload preview image (${response.status})`);
      }
      
      const result = await response.json();
      console.log('Preview upload success:', result.path);
      return result.path;
    } catch (error) {
      console.error('Error uploading board preview:', error);
      return null;
    }
  }; 