import { BoardOperation, OperationHistory, Board } from '../types';

/**
 * Implementation of the OperationHistory interface
 */
export class OperationHistoryImpl implements OperationHistory {
  private operations: BoardOperation[] = [];
  private currentIndex: number = -1;
  private maxHistorySize: number = 100;

  /**
   * Add a new operation to the history
   */
  addOperation(operation: BoardOperation): void {
    // Remove any future operations if we're in the middle of the history
    if (this.currentIndex < this.operations.length - 1) {
      this.operations = this.operations.slice(0, this.currentIndex + 1);
    }

    // Add the new operation
    this.operations.push(operation);
    this.currentIndex = this.operations.length - 1;

    // Trim history if it's too long
    if (this.operations.length > this.maxHistorySize) {
      this.operations = this.operations.slice(this.operations.length - this.maxHistorySize);
      this.currentIndex = this.operations.length - 1;
    }
  }

  /**
   * Undo the last operation
   */
  undo(): BoardOperation | undefined {
    if (!this.canUndo()) {
      return undefined;
    }

    const operation = this.operations[this.currentIndex];
    this.currentIndex--;
    return operation;
  }

  /**
   * Redo a previously undone operation
   */
  redo(): BoardOperation | undefined {
    if (!this.canRedo()) {
      return undefined;
    }

    this.currentIndex++;
    return this.operations[this.currentIndex];
  }

  /**
   * Check if undo is available
   */
  canUndo(): boolean {
    return this.currentIndex >= 0;
  }

  /**
   * Check if redo is available
   */
  canRedo(): boolean {
    return this.currentIndex < this.operations.length - 1;
  }

  /**
   * Clear the history
   */
  clear(): void {
    this.operations = [];
    this.currentIndex = -1;
  }
}

/**
 * Singleton instance of the operation history
 */
export const operationHistory = new OperationHistoryImpl();

/**
 * Command implementations for specific board operations
 */

/**
 * Base abstract class for board operations
 */
export abstract class BaseBoardOperation implements BoardOperation {
  abstract type: string;
  abstract payload: any;
  abstract execute(boardState: Board): Board;
  abstract undo(boardState: Board): Board;
}

/**
 * Operation for adding an item to the board
 */
export class AddItemOperation extends BaseBoardOperation {
  type = 'ADD_ITEM';
  
  constructor(public payload: { item: any }) {
    super();
  }

  execute(boardState: Board): Board {
    return {
      ...boardState,
      elements: [...boardState.elements, this.payload.item]
    };
  }

  undo(boardState: Board): Board {
    return {
      ...boardState,
      elements: boardState.elements.filter(item => item.id !== this.payload.item.id)
    };
  }
}

/**
 * Operation for removing an item from the board
 */
export class DeleteItemOperation extends BaseBoardOperation {
  type = 'DELETE_ITEM';
  
  constructor(public payload: { itemId: string, item: any }) {
    super();
  }

  execute(boardState: Board): Board {
    return {
      ...boardState,
      elements: boardState.elements.filter(item => item.id !== this.payload.itemId)
    };
  }

  undo(boardState: Board): Board {
    return {
      ...boardState,
      elements: [...boardState.elements, this.payload.item]
    };
  }
}

/**
 * Operation for updating an item on the board
 */
export class UpdateItemOperation extends BaseBoardOperation {
  type = 'UPDATE_ITEM';
  
  constructor(public payload: { itemId: string, updates: any, previousItem: any }) {
    super();
  }

  execute(boardState: Board): Board {
    return {
      ...boardState,
      elements: boardState.elements.map(item => 
        item.id === this.payload.itemId 
          ? { ...item, ...this.payload.updates }
          : item
      )
    };
  }

  undo(boardState: Board): Board {
    return {
      ...boardState,
      elements: boardState.elements.map(item => 
        item.id === this.payload.itemId 
          ? this.payload.previousItem
          : item
      )
    };
  }
}

/**
 * Operation for adding a connection to the board
 */
export class AddConnectionOperation extends BaseBoardOperation {
  type = 'ADD_CONNECTION';
  
  constructor(public payload: { connection: any }) {
    super();
  }

  execute(boardState: Board): Board {
    return {
      ...boardState,
      connections: [...boardState.connections, this.payload.connection]
    };
  }

  undo(boardState: Board): Board {
    return {
      ...boardState,
      connections: boardState.connections.filter(conn => conn.id !== this.payload.connection.id)
    };
  }
}

/**
 * Operation for adding multiple connections to the board at once
 */
export class BatchConnectionOperation extends BaseBoardOperation {
  type = 'BATCH_CONNECTION';
  
  constructor(public payload: { connections: any[] }) {
    super();
  }

  execute(boardState: Board): Board {
    return {
      ...boardState,
      connections: [...boardState.connections, ...this.payload.connections]
    };
  }

  undo(boardState: Board): Board {
    // Filter out all connections that were part of this batch operation
    const connectionIds = new Set(this.payload.connections.map(conn => conn.id));
    return {
      ...boardState,
      connections: boardState.connections.filter(conn => !connectionIds.has(conn.id))
    };
  }
}

/**
 * Operation for removing a connection from the board
 */
export class DeleteConnectionOperation extends BaseBoardOperation {
  type = 'DELETE_CONNECTION';
  
  constructor(public payload: { connectionId: string, connection: any }) {
    super();
  }

  execute(boardState: Board): Board {
    return {
      ...boardState,
      connections: boardState.connections.filter(conn => conn.id !== this.payload.connectionId)
    };
  }

  undo(boardState: Board): Board {
    return {
      ...boardState,
      connections: [...boardState.connections, this.payload.connection]
    };
  }
} 