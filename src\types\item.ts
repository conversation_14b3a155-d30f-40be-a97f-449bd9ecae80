/**
 * Types related to board items
 */

import { Position } from './board';

/**
 * Represents the different types of items that can be placed on the board
 */
export type ItemType = 'sticky' | 'sticky-yellow' | 'sticky-red' | 'sticky-blue' | 'text' | 'image' | 'evidence' | 'article' | 'image-invisible';

/**
 * Base interface for all board items
 */
export interface BoardItem {
  id: string;
  type: ItemType;
  content: string;
  position: Position;
  width?: number;
  height?: number;
  color?: string;
  title?: string;
  url?: string;
  website_url?: string;
  imageUrl?: string;
  alt?: string;
  file_url?: string;
}

/**
 * Interface for sticky note items
 */
export interface StickyNoteItem extends BoardItem {
  type: 'sticky' | 'sticky-yellow' | 'sticky-red' | 'sticky-blue';
  color: string;
}

/**
 * Interface for text items
 */
export interface TextItem extends BoardItem {
  type: 'text';
}

/**
 * Interface for article items
 */
export interface ArticleItem extends BoardItem {
  type: 'article';
  title: string;
  url: string;
  website_url?: string;
}

/**
 * Interface for image items
 */
export interface ImageItem extends BoardItem {
  type: 'image' | 'image-invisible';
  imageUrl?: string;
  alt?: string;
  file_url?: string;
}

/**
 * Common props for all item components
 */
export interface BaseItemProps {
  id: string;
  position: Position;
  onPositionChange: (id: string, position: Position) => void;
  onSelect: (id: string) => void;
  isSelected: boolean;
  onDelete: (id: string) => void;
  onContentChange: (id: string, content: string) => void;
}

/**
 * Props for the sticky note component
 */
export interface StickyNoteProps extends BaseItemProps {
  content: string;
  color: string;
}

/**
 * Props for the text node component
 */
export interface TextNodeProps extends BaseItemProps {
  content: string;
}

/**
 * Props for the article node component
 */
export interface ArticleNodeProps extends BaseItemProps {
  title: string;
  url: string;
  website_url?: string;
  content: string;
}

/**
 * Props for the image component
 */
export interface ImageNodeProps extends BaseItemProps {
  imageUrl?: string;
  alt?: string;
  content: string;
}

/**
 * Type guard to check if an item is a sticky note
 */
export function isStickyNote(item: BoardItem): item is StickyNoteItem {
  return item.type === 'sticky' || 
         item.type === 'sticky-yellow' || 
         item.type === 'sticky-red' || 
         item.type === 'sticky-blue';
}

/**
 * Type guard to check if an item is a text node
 */
export function isTextNode(item: BoardItem): item is TextItem {
  return item.type === 'text';
}

/**
 * Type guard to check if an item is an article
 */
export function isArticleNode(item: BoardItem): item is ArticleItem {
  return item.type === 'article';
}

/**
 * Type guard to check if an item is an image
 */
export function isImageNode(item: BoardItem): item is ImageItem {
  return item.type === 'image' || item.type === 'image-invisible';
} 