import React, { useState, useRef, useEffect, useCallback } from 'react';
import { motion, useMotionValue } from '../../../utils/MotionWrapper';
import { useDrag } from '../../../context/DragContext';
import { useBoard } from '../../../context/BoardContext';

export interface BoardItemProps {
  id: string;
  type: 'sticky' | 'text' | 'image' | 'evidence' | 'article' | 'image-invisible';
  content: string;
  position: { x: number; y: number };
  width?: number;
  height?: number;
  color?: string;
  onPositionChange: (id: string, position: { x: number; y: number }) => void;
  onSelect: (id: string) => void;
  isSelected: boolean;
  scale: number;
  onSizeChange?: (id: string, width: number, height: number) => void;
  children?: React.ReactNode;
  isEditMode?: boolean;
  connectMode?: boolean;
  connectStart?: string | null;
  connections?: Array<{
    id: string;
    fromId?: string;
    toId?: string;
    from?: { id: string };
    to?: { id: string };
  }>;
  onConnectionStart?: (id: string) => void;
  onConnectionComplete?: (id: string) => void;
  presentationMode?: boolean;
  isMultiSelected?: boolean;
  isSelectionModeActive?: boolean;
  isInContextGroup?: boolean;
  contextGroupColor?: string;
  contextGroupId?: string;
  onHighlightContextGroup?: (contextId: string) => void;
}

const BoardItem: React.FC<BoardItemProps> = ({
  id,
  type,
  content,
  position,
  width,
  height,
  onPositionChange,
  onSelect,
  isSelected,
  scale,
  onSizeChange,
  children,
  isEditMode = false,
  connectMode = false,
  connectStart = null,
  connections = [],
  onConnectionStart,
  onConnectionComplete,
  presentationMode = false,
  isMultiSelected = false,
  isSelectionModeActive = false,
  isInContextGroup = false,
  contextGroupColor,
  contextGroupId,
  onHighlightContextGroup
}) => {
  // Use motion values for smooth animation/updates
  const motionX = useMotionValue(position.x);
  const motionY = useMotionValue(position.y);
  const motionWidth = useMotionValue(width ?? 0);
  const motionHeight = useMotionValue(height ?? 0);

  // Access the drag context for real-time position updates
  const { updateDragPosition, setItemDragging } = useDrag();

  // Get the moveSelectedItemsByDelta function from BoardContext
  const { moveSelectedItemsByDelta, selectedItemIds } = useBoard();

  // State for drag tracking using refs to avoid stale closures
  const isDraggingRef = useRef(false);
  const dragStartPositionRef = useRef({ x: 0, y: 0 });
  const dragStartPointerPositionRef = useRef({ x: 0, y: 0 });
  const lastPointerPositionRef = useRef({ x: 0, y: 0 });

  // Resize state
  const isResizingRef = useRef(false);
  const resizeStartPointerRef = useRef<{ x: number; y: number }>({ x: 0, y: 0 });
  const resizeStartSizeRef = useRef<{ w: number; h: number }>({ w: 0, h: 0 });

  // Add hover state to show resize handles
  const [isHovered, setIsHovered] = useState(false);

  // Check if a connection already exists between this item and the start item
  const hasConnectionWithStart = useCallback(() => {
    if (!connectStart || !connections || connections.length === 0) return false;
    
    // Check for connection using different possible field names
    const hasConnection = connections.some(conn => {
      // Check first pattern: fromId/toId
      if (conn.fromId !== undefined && conn.toId !== undefined) {
        return (conn.fromId === connectStart && conn.toId === id) || 
               (conn.fromId === id && conn.toId === connectStart);
      }
      // Check second pattern: from.id/to.id (if connections are stored in a different format)
      else if (conn.from?.id !== undefined && conn.to?.id !== undefined) {
        return (conn.from.id === connectStart && conn.to.id === id) ||
               (conn.from.id === id && conn.to.id === connectStart);
      }
      return false;
    });
    
    return hasConnection;
  }, [id, connectStart, connections]);

  // Sync motion values if position prop changes from outside, but not during drag
  useEffect(() => {
    if (!isDraggingRef.current) { 
      // Added console.log for debugging non-primary item movement
      console.log(`BoardItem ${id}: useEffect detected position change. IsDraggingSelf: ${isDraggingRef.current}. New Pos: x=${position.x}, y=${position.y}. Applying to motionX/Y.`);
      motionX.set(position.x);
      motionY.set(position.y);
    }
  }, [id, position.x, position.y]); // isDraggingRef is not and should not be a dependency here

  // Sync width/height if props change (when not resizing)
  useEffect(() => {
    if (!isResizingRef.current && width !== undefined) motionWidth.set(width);
  }, [width, motionWidth]);
  useEffect(() => {
    if (!isResizingRef.current && height !== undefined) motionHeight.set(height);
  }, [height, motionHeight]);

  // === Resize handlers ===
  const handleResizePointerMove = useCallback((event: PointerEvent) => {
    if (!isResizingRef.current) return;
    
    const resizeHandle = document.querySelector('[data-resizing-from]') as HTMLElement;
    if (!resizeHandle) return;
    
    const corner = resizeHandle.getAttribute('data-resizing-from');
    const dx = event.clientX - resizeStartPointerRef.current.x;
    const dy = event.clientY - resizeStartPointerRef.current.y;
    
    // Scale the deltas
    const dxScaled = dx / scale;
    const dyScaled = dy / scale;
    
    let newW = resizeStartSizeRef.current.w;
    let newH = resizeStartSizeRef.current.h;
    let newX = dragStartPositionRef.current.x;
    let newY = dragStartPositionRef.current.y;
    
    // Handle each corner differently
    switch (corner) {
      case 'se': // Bottom-right
        newW = Math.max(resizeStartSizeRef.current.w + dxScaled, 20);
        newH = Math.max(resizeStartSizeRef.current.h + dyScaled, 20);
        break;
      case 'sw': // Bottom-left
        newW = Math.max(resizeStartSizeRef.current.w - dxScaled, 20);
        newH = Math.max(resizeStartSizeRef.current.h + dyScaled, 20);
        newX = dragStartPositionRef.current.x + (resizeStartSizeRef.current.w - newW);
        break;
      case 'ne': // Top-right
        newW = Math.max(resizeStartSizeRef.current.w + dxScaled, 20);
        newH = Math.max(resizeStartSizeRef.current.h - dyScaled, 20);
        newY = dragStartPositionRef.current.y + (resizeStartSizeRef.current.h - newH);
        break;
      case 'nw': // Top-left
        newW = Math.max(resizeStartSizeRef.current.w - dxScaled, 20);
        newH = Math.max(resizeStartSizeRef.current.h - dyScaled, 20);
        newX = dragStartPositionRef.current.x + (resizeStartSizeRef.current.w - newW);
        newY = dragStartPositionRef.current.y + (resizeStartSizeRef.current.h - newH);
        break;
    }
    
    // Update size
    motionWidth.set(newW);
    motionHeight.set(newH);
    
    // Update position
    motionX.set(newX);
    motionY.set(newY);
    
  }, [scale, motionWidth, motionHeight, motionX, motionY]);

  const handleResizePointerUp = useCallback((event: PointerEvent) => {
    if (!isResizingRef.current) return;
    isResizingRef.current = false;
    
    // Remove the data attribute
    const resizeHandle = document.querySelector('[data-resizing-from]') as HTMLElement;
    if (resizeHandle) {
      resizeHandle.removeAttribute('data-resizing-from');
    }
    
    window.removeEventListener('pointermove', handleResizePointerMove);
    window.removeEventListener('pointerup', handleResizePointerUp);
    
    if (onSizeChange) {
      onSizeChange(id, motionWidth.get(), motionHeight.get());
    }
    
    // Update position if it changed during resize
    onPositionChange(id, { x: motionX.get(), y: motionY.get() });
    
  }, [handleResizePointerMove, onSizeChange, onPositionChange, id, motionWidth, motionHeight, motionX, motionY]);

  // === Start: Manual Drag Handlers ===
  const handlePointerMove = useCallback((event: PointerEvent) => {
    if (!isDraggingRef.current) return;

    // Calculate screen delta from start (for the primary item's total potential move)
    const totalDeltaXScreen = event.clientX - dragStartPointerPositionRef.current.x;
    const totalDeltaYScreen = event.clientY - dragStartPointerPositionRef.current.y;

    // Target absolute position for the primary dragged item
    const targetX = dragStartPositionRef.current.x + (totalDeltaXScreen / scale);
    const targetY = dragStartPositionRef.current.y + (totalDeltaYScreen / scale);

    if (isMultiSelected && selectedItemIds.includes(id)) {
      // For multi-drag, calculate the incremental delta from the *last pointer position*
      const incrementalDeltaXScreen = event.clientX - lastPointerPositionRef.current.x;
      const incrementalDeltaYScreen = event.clientY - lastPointerPositionRef.current.y;

      const incrementalDx = incrementalDeltaXScreen / scale;
      const incrementalDy = incrementalDeltaYScreen / scale;

      if (incrementalDx !== 0 || incrementalDy !== 0) {
        // Move all selected items in context by this incremental delta
        moveSelectedItemsByDelta({ dx: incrementalDx, dy: incrementalDy });
      }
      
      // Directly update the motion values for the PRIMARY DRAGGED ITEM for immediate visual feedback.
      // The other selected items will update via their useEffect syncing from the position prop
      // once moveSelectedItemsByDelta updates their state in the context.
      motionX.set(targetX);
      motionY.set(targetY);

    } else {
      // Original single item drag logic: update only this item's motion values
      motionX.set(targetX);
      motionY.set(targetY);
      updateDragPosition(id, { x: targetX, y: targetY }); // For DragContext, if used elsewhere
    }

    // Update the last pointer position for the next incremental calculation
    lastPointerPositionRef.current = { x: event.clientX, y: event.clientY };

  }, [
    isMultiSelected, selectedItemIds, id, scale, moveSelectedItemsByDelta, 
    motionX, motionY, updateDragPosition, 
    dragStartPositionRef, dragStartPointerPositionRef, lastPointerPositionRef
  ]);

  const handlePointerUp = useCallback((event: PointerEvent) => {
    if (!isDraggingRef.current) return;

    (event.target as Element)?.releasePointerCapture(event.pointerId);
    window.removeEventListener('pointermove', handlePointerMove);
    window.removeEventListener('pointerup', handlePointerUp);

    isDraggingRef.current = false;
    setItemDragging(id, false);

    // For single item drags, the onPositionChange based on final target is still needed.
    // For multi-item drags, items were moved incrementally. The final positions are already set.
    if (!isMultiSelected) {
      const totalDeltaXScreen = event.clientX - dragStartPointerPositionRef.current.x;
      const totalDeltaYScreen = event.clientY - dragStartPointerPositionRef.current.y;
      const finalTargetX = dragStartPositionRef.current.x + (totalDeltaXScreen / scale);
      const finalTargetY = dragStartPositionRef.current.y + (totalDeltaYScreen / scale);
      onPositionChange(id, { x: finalTargetX, y: finalTargetY });
    }
    // No special action for multi-drag needed here as items are moved continuously in handlePointerMove
    // via moveSelectedItemsByDelta. The batch event is also handled there.

  }, [
    isMultiSelected, scale, onPositionChange, id, handlePointerMove, setItemDragging, 
    dragStartPositionRef, dragStartPointerPositionRef
  ]);

  const handlePointerDown = useCallback((event: React.PointerEvent<HTMLDivElement>) => {
    // If in presentation mode, disable all edit interactions
    if (presentationMode) {
      event.stopPropagation(); // Stop propagation to allow underlying canvas panning
      return;
    }
    
    // If in connect mode, handle connection logic instead of dragging
    if (connectMode) {
      event.preventDefault();
      event.stopPropagation();
      
      // Prevent connections on text elements
      if (type === 'text') {
        return;
      }
      
      // If connection already started, check if we can complete it
      if (onConnectionComplete && connectStart) {
        // Don't create connections to self
        if (connectStart === id) {
          return;
        }
        
        // Don't create duplicate connections
        if (hasConnectionWithStart()) {
          return;
        }
        
        // Otherwise, proceed with the normal connection completion
        onConnectionComplete(id);
      }
      // Otherwise start a new connection
      else if (onConnectionStart) {
        onConnectionStart(id);
      }
      return;
    }

    // If in edit mode, don't start dragging
    if (isEditMode) {
      return;
    }

    // Check if the target or any of its parents has a data-nodrag attribute
    let target = event.target as HTMLElement;
    while (target && target !== event.currentTarget) {
      if (target.getAttribute('data-nodrag') === 'true') {
        return; // Don't start drag if clicked on a no-drag element
      }
      target = target.parentElement as HTMLElement;
    }

    // Prevent defaults and stop propagation immediately
    event.preventDefault();
    event.stopPropagation();

    // --- Capture necessary event data synchronously ---
    const targetElement = event.currentTarget; // Get the element reference now
    const pointerId = event.pointerId;       // Get the pointerId now
    const startClientX = event.clientX;      // Get coordinates now
    const startClientY = event.clientY;      // Get coordinates now

    // Call onSelect immediately to trigger the state change in the parent
    onSelect(id);

    // --- Delay Drag Setup ---
    requestAnimationFrame(() => {
        // Check if the element still exists (belt-and-suspenders)
        if (!targetElement) return;

        // Use the captured element reference and pointerId
        targetElement.setPointerCapture(pointerId);

        // Set dragging state and record positions using captured coordinates
        isDraggingRef.current = true;
        dragStartPositionRef.current = { x: motionX.get(), y: motionY.get() };
        dragStartPointerPositionRef.current = { x: startClientX, y: startClientY };
        lastPointerPositionRef.current = { x: startClientX, y: startClientY };

        // Update drag status in context
        setItemDragging(id, true);
        updateDragPosition(id, { x: motionX.get(), y: motionY.get() });

        // Add listeners to the window
        window.addEventListener('pointermove', handlePointerMove);
        window.addEventListener('pointerup', handlePointerUp);
    });

  }, [onSelect, id, motionX, motionY, handlePointerMove, handlePointerUp, isEditMode, connectMode, onConnectionStart, onConnectionComplete, connectStart, setItemDragging, updateDragPosition]);

  // Cleanup listeners on unmount
  useEffect(() => {
    // Return cleanup function
    return () => {
        // If component unmounts *while* dragging, remove listeners to prevent memory leaks
        if (isDraggingRef.current) {
            window.removeEventListener('pointermove', handlePointerMove);
            window.removeEventListener('pointerup', handlePointerUp);
            // Note: Pointer capture is automatically released on unmount/element removal
        }
    };
  }, [handlePointerMove, handlePointerUp]);
  // === End: Manual Drag Handlers ===

  // Get the appropriate cursor style based on current state
  const getCursorStyle = () => {
    if (presentationMode) {
      return 'default'; // Use default cursor in presentation mode
    }
    
    if (isEditMode) {
      return 'text';
    }
    
    if (connectMode) {
      if (type === 'text') {
        return 'not-allowed'; // Text elements can't be connected to
      }
      
      if (connectStart === id) {
        return 'not-allowed'; // Can't connect to self
      }
      
      if (connectStart) {
        const existingConnection = hasConnectionWithStart();
        if (existingConnection) {
          return 'not-allowed'; // Can't duplicate connections
        }
        
        return 'crosshair'; // Potential target for connection
      }
      
      return 'crosshair'; // In connect mode but no source selected yet
    }
    
    return 'move'; // Default for regular draggable items
  };

  // Handle starting resize from different corners
  const startResize = useCallback((e: React.PointerEvent, corner: string) => {
    e.stopPropagation();
    e.preventDefault();
    isResizingRef.current = true;
    resizeStartPointerRef.current = { x: e.clientX, y: e.clientY };
    resizeStartSizeRef.current = {
      w: motionWidth.get(),
      h: motionHeight.get()
    };
    
    // Store the original position for top/left resizing
    dragStartPositionRef.current = { x: motionX.get(), y: motionY.get() };
    
    // Store which corner we're resizing from
    (e.currentTarget as HTMLElement).setAttribute('data-resizing-from', corner);
    
    window.addEventListener('pointermove', handleResizePointerMove);
    window.addEventListener('pointerup', handleResizePointerUp);
  }, [motionWidth, motionHeight, motionX, motionY]);

  return (
    <motion.div
      className={`board-item-draggable absolute ${isSelected || isMultiSelected ? 'z-10' : 'z-0'}`}
      data-selectable-item="true"
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{
        opacity: 1,
        scale: 1,
        boxShadow: isMultiSelected
          ? '0 0 0 3px rgba(59, 130, 246, 0.8), 0 0 20px rgba(59, 130, 246, 0.6), 0 4px 20px rgba(0, 0, 0, 0.3)'
          : isSelected
            ? '0 0 0 3px rgba(214, 173, 96, 0.8), 0 0 20px rgba(214, 173, 96, 0.6), 0 4px 20px rgba(0, 0, 0, 0.3)'
            : connectMode
              ? type === 'text'
                ? 'none'
                : connectStart === id
                  ? '0 0 0 2px rgba(178, 34, 34, 0.7), 0 4px 10px rgba(0, 0, 0, 0.3)'
                  : '0 0 0 2px rgba(96, 165, 214, 0.7)'
              : isInContextGroup && contextGroupColor
                ? `0 0 0 2px ${contextGroupColor}80, inset 0 0 0 1px ${contextGroupColor}40`
                : 'none'
      }}
      transition={{
        duration: 0.3,
        boxShadow: {
          duration: isSelected || isMultiSelected ? 0.5 : 0.3,
          repeat: isSelected || isMultiSelected ? 3 : 0,
          repeatType: "reverse" as const
        }
      }}
      style={{
        x: motionX,
        y: motionY,
        width: motionWidth,
        height: motionHeight,
        touchAction: "none",
        cursor: getCursorStyle(),
        opacity: 1, // Always fully visible
        userSelect: isSelectionModeActive ? 'none' : 'auto' // Control text selection
      }}
      onPointerDown={handlePointerDown}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      title={isInContextGroup ? "This item is part of a context group and will move with related items" : undefined}
    >
      {/* Context group indicator */}
      {isInContextGroup && contextGroupColor && !presentationMode && (
        <div
          onClick={(e) => {
            e.stopPropagation();
            if (onHighlightContextGroup && contextGroupId) {
              onHighlightContextGroup(contextGroupId);
            }
          }}
          style={{
            position: 'absolute',
            top: '-8px',
            right: '-8px',
            width: '16px',
            height: '16px',
            borderRadius: '50%',
            backgroundColor: contextGroupColor,
            border: '2px solid white',
            boxShadow: '0 1px 3px rgba(0,0,0,0.3)',
            zIndex: 10,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '10px',
            color: 'white',
            fontWeight: 'bold',
            cursor: 'pointer',
            transition: 'transform 0.2s',
          }}
          onMouseEnter={(e) => {
            (e.target as HTMLElement).style.transform = 'scale(1.2)';
          }}
          onMouseLeave={(e) => {
            (e.target as HTMLElement).style.transform = 'scale(1)';
          }}
          title="Click to highlight all items in this context group"
        >
          🔗
        </div>
      )}

      {/* Resize handles for all corners, visible on hover */}
      {!presentationMode && !isEditMode && onSizeChange && (isHovered || isSelected) && (
        <>
          {/* Bottom-right */}
          <div
            data-resize-handle="se"
            onPointerDown={(e) => startResize(e, 'se')}
            style={{
              position: 'absolute',
              right: 0,
              bottom: 0,
              width: 12,
              height: 12,
              cursor: 'nwse-resize',
              zIndex: 20
            }}
            className="bg-amber-400/50 hover:bg-amber-400 rounded-bl rounded-tr"
          />
          
          {/* Bottom-left */}
          <div
            data-resize-handle="sw"
            onPointerDown={(e) => startResize(e, 'sw')}
            style={{
              position: 'absolute',
              left: 0,
              bottom: 0,
              width: 12,
              height: 12,
              cursor: 'nesw-resize',
              zIndex: 20
            }}
            className="bg-amber-400/50 hover:bg-amber-400 rounded-br rounded-tl"
          />
          
          {/* Top-right */}
          <div
            data-resize-handle="ne"
            onPointerDown={(e) => startResize(e, 'ne')}
            style={{
              position: 'absolute',
              right: 0,
              top: 0,
              width: 12,
              height: 12,
              cursor: 'nesw-resize',
              zIndex: 20
            }}
            className="bg-amber-400/50 hover:bg-amber-400 rounded-tr rounded-bl"
          />
          
          {/* Top-left */}
          <div
            data-resize-handle="nw"
            onPointerDown={(e) => startResize(e, 'nw')}
            style={{
              position: 'absolute',
              left: 0,
              top: 0,
              width: 12,
              height: 12,
              cursor: 'nwse-resize',
              zIndex: 20
            }}
            className="bg-amber-400/50 hover:bg-amber-400 rounded-tl rounded-br"
          />
        </>
      )}
      {children}
    </motion.div>
  );
};

export default BoardItem;
