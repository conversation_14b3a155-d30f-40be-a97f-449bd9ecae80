import React, { useEffect, useRef, useCallback, useMemo, useState } from 'react';
import { <PERSON>Left, Loader2, Heart, MessageSquare, X, ThumbsUp, ThumbsDown, ChevronUp, ChevronDown, MessageCircle, MinusSquare, PlusSquare, ArrowUp, ArrowDown } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { v4 as uuidv4 } from 'uuid';
import styles from '@/styles/Comment.module.css';
import { motion } from 'framer-motion';

import { useAuth } from '@/context/AuthContext';
import { SignInModal } from '@/components/features/auth/SignInModal';
import { SignUpModal } from '@/components/features/auth/SignUpModal';
import { VerificationModal } from '@/components/features/auth/VerificationModal';
import { UsernameSetupModal } from '@/components/features/auth/UsernameSetupModal';
import { useBoard, BoardProvider } from '../../../context/BoardContext';
import PublicBoardCanvas from './../detective/Board/PublicBoardCanvas';
import TextNodeReadOnly from './TextNodeReadOnly';
import StickyNoteReadOnly from './StickyNoteReadOnly';
import ArticleNodeReadOnly from './ArticleNodeReadOnly';
import ImageNodeReadOnly from './ImageNodeReadOnly';
import ConnectionReadOnly from './ConnectionReadOnly';
import StrokeRenderer from '../detective/Strokes/StrokeRenderer';
import { Position, Board, BoardSharingRecord } from '../../../types/board';
import { BoardItem, isImageNode } from '../../../types/item';
import { PenStroke } from '../../../types';
import RemoteCursors from '../detective/Board/RemoteCursors';
import { ZoomSlider } from '../detective/ZoomControl';
import { MIN_SCALE, MAX_SCALE } from '../../../hooks/useZoomAndPan';

// Custom view state for public board that doesn't include connection-related properties
interface PublicBoardViewState {
  scale: number;
  position: Position;
  selectedItemId: string | null;
  isGrabbing: boolean;
  presentationMode: boolean;
}

// Add types for comment reactions
interface Comment {
  id: string;
  text: string;
  timestamp: string;
  score: number;
  userVote?: 'like' | 'dislike' | null;
  parent_comment_id: string | null;
  replies?: Comment[];
  is_edited?: boolean;
  username?: string;
  user_id?: string;
}

// Add interface for the API comment format (Adjusted Assumption)
interface ApiComment {
  id: string;
  content: string;
  created_at: string;
  likes?: number;
  dislikes?: number;
  user_id: string;
  board_id: string;
  is_edited?: boolean;
  parent_comment_id: string | null;
  updated_at: string;
  username?: string;
  userVote?: 'like' | 'dislike' | null;
}

// Add formatRelativeTime helper function
const formatRelativeTime = (timestamp: string): string => {
  const now = new Date();
  const date = new Date(timestamp);
  const seconds = Math.floor((now.getTime() - date.getTime()) / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);
  const weeks = Math.floor(days / 7);
  const months = Math.floor(days / 30);
  const years = Math.floor(days / 365);

  if (seconds < 30) return 'a couple seconds ago';
  if (seconds < 60) return 'less than a minute ago';
  if (minutes === 1) return '1 minute ago';
  if (minutes < 60) return `${minutes} minutes ago`;
  if (hours === 1) return '1 hour ago';
  if (hours < 24) return `${hours} hours ago`;
  if (days === 1) return '1 day ago';
  if (days < 7) return `${days} days ago`;
  if (weeks === 1) return '1 week ago';
  if (weeks < 4) return `${weeks} weeks ago`;
  if (months === 1) return '1 month ago';
  if (months < 12) return `${months} months ago`;
  if (years === 1) return '1 year ago';
  return `${years} years ago`;
};

// --- ADD HELPER FUNCTION HERE ---
// Helper function for Reddit Hot Score
const calculateHotScore = (likes: number, dislikes: number, timestamp: string): number => {
  const score = (likes || 0) - (dislikes || 0);
  const order = Math.log10(Math.max(Math.abs(score), 1));
  const sign = score > 0 ? 1 : score < 0 ? -1 : 0;
  const baseTimeSeconds = 1134028003; 
  const seconds = (new Date(timestamp).getTime() / 1000) - baseTimeSeconds;
  return sign * order + seconds / 45000; 
};
// --- END HELPER FUNCTION ---

// Add types for CommentComponent props
interface CommentComponentProps {
  comment: Comment;
  level?: number;
  replyingTo: string | null;
  replyText: string;
  setReplyText: (text: string) => void;
  handleVote: (commentId: string, reactionType: 'like' | 'dislike') => void;
  setReplyingTo: (id: string | null) => void;
  handleReplySubmit: (parentCommentId: string) => void;
  isNewComment: boolean;
  currentNewCommentId: string | null;
  handleEditSave: (commentId: string, newText: string) => Promise<void>;
  isUserLoggedIn: boolean;
  userId: string | undefined;
  isBoardPublic: boolean;
}

// Moved CommentComponent outside PublicBoardView
const CommentComponent: React.FC<CommentComponentProps> = React.memo(({ 
  comment, 
  level = 0,
  replyingTo,
  replyText,
  setReplyText,
  handleVote,
  setReplyingTo,
  handleReplySubmit,
  isNewComment,
  currentNewCommentId,
  handleEditSave,
  isUserLoggedIn,
  userId,
  isBoardPublic
}) => {
  const isReplyOpen = replyingTo === comment.id;
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editText, setEditText] = useState(comment.text);

  // Refs and state for SVG line
  const nestedCommentsRef = useRef<HTMLDivElement>(null);
  const [nestedHeight, setNestedHeight] = useState(0);

  const hasReplies = comment.replies && comment.replies.length > 0;

  // Effect to measure height for SVG line
  useEffect(() => {
    const targetElement = nestedCommentsRef.current;
    if (!targetElement || !hasReplies || isCollapsed) {
      // No need to measure if no replies or collapsed
      return;
    }

    const resizeObserver = new ResizeObserver(entries => {
      for (let entry of entries) {
        // Add small buffer to height to prevent line ending slightly short sometimes
        setNestedHeight(entry.contentRect.height + 5); 
      }
    });

    resizeObserver.observe(targetElement);

    // Initial height check
    setNestedHeight(targetElement.getBoundingClientRect().height + 5);

    return () => {
      resizeObserver.unobserve(targetElement);
    };
    // Rerun if collapsed state changes or replies potentially change
  }, [hasReplies, isCollapsed, comment.replies]); 

  const toggleCollapse = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsCollapsed(!isCollapsed);
  };

  const handleStartEdit = () => {
    setEditText(comment.text);
    setIsEditing(true);
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
  };

  const handleSaveEditClick = async () => {
    if (!editText.trim() || editText.trim() === comment.text) {
        setIsEditing(false);
        return;
    }
    await handleEditSave(comment.id, editText.trim());
    setIsEditing(false);
  };

  return (
    <div className={`${styles.comment} ${isNewComment ? styles['new-comment'] : ''}`}>
      <p className={styles['comment-username']}>{comment.username || 'Anonymous'}</p>
      <p className={styles['comment-time']}>
        {formatRelativeTime(comment.timestamp)}
        {comment.is_edited && <span className={styles.editedIndicator}> (edited)</span>}
      </p>

      {isEditing ? (
        <div className={styles.editContainer}>
          <textarea
            value={editText}
            onChange={(e) => setEditText(e.target.value)}
            className={styles['add-comment-input']}
            rows={3}
            autoFocus
          />
          <div className="flex gap-2 mt-2">
            <button
              onClick={handleSaveEditClick}
              disabled={!editText.trim()}
              className={styles.commentSubmitButton}
            >
              Save
            </button>
            <button
              onClick={handleCancelEdit}
              className={styles.commentSubmitButton}
              style={{ backgroundColor: '#eee', borderColor: '#ddd' }}
            >
              Cancel
            </button>
          </div>
        </div>
      ) : (
        <p className={styles['comment-body']}>{comment.text}</p>
      )}

      <div className={styles['comment-actions']}>
        {isBoardPublic && (
          <div className={styles['vote-container']}>
            <button 
              onClick={() => handleVote(comment.id, 'like')}
              className={`${styles['vote-button']} ${comment.userVote === 'like' ? styles.upvoted : ''}`}
            >
              <ArrowUp 
                size={16} 
                fill={comment.userVote === 'like' ? '#00A7B1' : 'none'}
              />
            </button>
            <span className={styles['vote-score']}>{comment.score || 0}</span>
            <button 
              onClick={() => handleVote(comment.id, 'dislike')}
              className={`${styles['vote-button']} ${comment.userVote === 'dislike' ? styles.downvoted : ''}`}
            >
              <ArrowDown 
                size={16} 
                fill={comment.userVote === 'dislike' ? '#b52a2b' : 'none'}
              />
            </button>
          </div>
        )}
        {isBoardPublic && (
          <button 
            className={styles['reply-button']}
            onClick={() => setReplyingTo(isReplyOpen ? null : comment.id)}
          >
            <MessageCircle size={14} />
            Reply
          </button>
        )}
        {isBoardPublic && isUserLoggedIn && comment.user_id === userId && !isEditing && (
          <button 
            className={styles['edit-button']}
            onClick={handleStartEdit}
          >
            Edit
          </button>
        )}
      </div>
      
      {isReplyOpen && (
        <div className={styles['reply-input-container']}>
          <textarea
            value={replyText}
            onChange={(e) => setReplyText(e.target.value)}
            placeholder="Write your reply..."
            className={styles['add-comment-input']}
            rows={2}
            autoFocus
          />
          <div className="flex gap-2 mt-2">
            <button
              onClick={() => handleReplySubmit(comment.id)}
              disabled={!replyText.trim()}
              className={styles.commentSubmitButton}
            >
              Reply
            </button>
            <button
              onClick={() => {
                setReplyingTo(null);
                setReplyText('');
              }}
              className={styles.commentSubmitButton}
              style={{ backgroundColor: '#eee', borderColor: '#ddd' }}
            >
              Cancel
            </button>
          </div>
        </div>
      )}
      
      {hasReplies && (
        <button 
          onClick={toggleCollapse}
          className={`${styles.collapseButton} ${isCollapsed ? styles.collapsed : ''}`}
          aria-label={isCollapsed ? "Expand replies" : "Collapse replies"}
          style={{ 
            marginTop: '0.5rem',
            marginBottom: isCollapsed ? '0' : '0.5rem'
           }}
        >
          {isCollapsed ? '[+]' : '[-]'}
        </button>
      )}

      {/* Container for SVG line and nested comments */}
      {hasReplies && !isCollapsed && (
        <div style={{ position: 'relative' }}>
          {/* SVG Connector Line */}
          <svg 
            style={{ 
              position: 'absolute', 
              left: '10px', // Position line slightly left of the collapse button
              top: '10px', // Start line slightly below the button
              height: nestedHeight, 
              width: '10px', // SVG width (path is thinner)
              pointerEvents: 'none', 
              overflow: 'visible' 
            }}
          >
            {/* Local Filter Definition (copied from Connection.tsx) */}
            <defs>
              <filter id="yarnTexture">
                <feTurbulence type="fractalNoise" baseFrequency="0.6" numOctaves="3" result="noise" />
                <feDisplacementMap in="SourceGraphic" in2="noise" scale="4" />
              </filter>
              <filter id="yarnWithShadow">
                <feTurbulence type="fractalNoise" baseFrequency="0.6" numOctaves="3" result="noise" />
                <feDisplacementMap in="SourceGraphic" in2="noise" scale="4" result="textured" />
                <feDropShadow dx="3" dy="4" stdDeviation="3.5" floodColor="rgba(0, 0, 0, 0.5)" />
              </filter>
            </defs>

            <path
              d={`M 3 0 L 3 ${nestedHeight - 10}`} // Vertical line (x=3), adjusted height
              stroke="#C0392B"
              strokeWidth="2"
              strokeLinecap="round"
              fill="none"
              filter="url(#yarnWithShadow)" // Reference the local filter
            />

            {/* Start Pin (at top) */}
            <g pointerEvents="none">
              <motion.circle
                cx={3} // Align with line x-coordinate
                cy={0}  // Position at the top of the line
                r={4}
                initial={{ opacity: 0, strokeWidth: 1 }}
                animate={{ opacity: 1, fill: '#B22222', stroke: '#990000', strokeWidth: 1 }}
                transition={{ duration: 0.2 }}
                style={{ filter: 'drop-shadow(0px 2px 2px rgba(0,0,0,0.5))' }}
              />
              <motion.circle
                cx={3} // Align with line x-coordinate
                cy={-2} // Position slightly above the main pin circle
                r={2}
                initial={{ opacity: 0, strokeWidth: 0.5 }}
                animate={{ opacity: 1, fill: '#ffcccc', stroke: '#990000', strokeWidth: 0.5 }}
                transition={{ duration: 0.2 }}
              />
            </g>

            {/* End Pin (at bottom) */}
            <g pointerEvents="none">
              <motion.circle
                cx={3} // Align with line x-coordinate
                cy={nestedHeight - 10} // Position at the bottom of the line
                r={4}
                initial={{ opacity: 0, strokeWidth: 1 }}
                animate={{ opacity: 1, fill: '#B22222', stroke: '#990000', strokeWidth: 1 }}
                transition={{ duration: 0.2 }}
                style={{ filter: 'drop-shadow(0px 2px 2px rgba(0,0,0,0.5))' }}
              />
              <motion.circle
                cx={3} // Align with line x-coordinate
                cy={nestedHeight - 10 - 2} // Position slightly above the main pin circle
                r={2}
                initial={{ opacity: 0, strokeWidth: 0.5 }}
                animate={{ opacity: 1, fill: '#ffcccc', stroke: '#990000', strokeWidth: 0.5 }}
                transition={{ duration: 0.2 }}
              />
            </g>

          </svg>

          {/* Nested Comments Content */}
          <div ref={nestedCommentsRef} className={styles['nested-comments']}>
            {comment.replies && comment.replies.map(reply => (
              <CommentComponent 
                key={reply.id} 
                comment={reply} 
                level={level + 1} 
                replyingTo={replyingTo}
                replyText={replyText}
                setReplyText={setReplyText}
                handleVote={handleVote}
                setReplyingTo={setReplyingTo}
                handleReplySubmit={handleReplySubmit}
                currentNewCommentId={currentNewCommentId}
                isNewComment={reply.id === currentNewCommentId}
                handleEditSave={handleEditSave}
                isUserLoggedIn={isUserLoggedIn}
                userId={userId}
                isBoardPublic={isBoardPublic}
              />
            ))}
          </div>
        </div>
      )}
    </div>
  );
});

CommentComponent.displayName = 'CommentComponent';

/**
 * Read-only public view of a detective board
 */
const PublicBoardView: React.FC = () => {
  const router = useRouter();
  const boardContainerRef = useRef<HTMLDivElement>(null);
  const [isBackLoading, setIsBackLoading] = useState<boolean>(false);
  const [likeCount, setLikeCount] = useState<number>(0);
  const [hasLiked, setHasLiked] = useState<boolean>(false);
  const [isCommentSidebarOpen, setIsCommentSidebarOpen] = useState<boolean>(false);
  const [commentText, setCommentText] = useState<string>('');
  const [comments, setComments] = useState<Comment[]>([]);
  const [sidebarWidth, setSidebarWidth] = useState<number>(320);
  const [isResizing, setIsResizing] = useState<boolean>(false);
  const sidebarRef = useRef<HTMLDivElement>(null);
  const resizeHandleRef = useRef<HTMLDivElement>(null);
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [replyText, setReplyText] = useState<string>('');
  
  // Add loading states to match DetectiveBoard
  const [isBoardLoading, setIsBoardLoading] = useState<boolean>(true);
  const [loadedItemIds, setLoadedItemIds] = useState<Set<string>>(new Set());
  const [isInitialLoad, setIsInitialLoad] = useState<boolean>(true);
  const [loadingBatch, setLoadingBatch] = useState<boolean>(false);
  const [loadingProgress, setLoadingProgress] = useState(0);
  
  const { 
    board, 
    boardId,
    scale, 
    position,
    setScale,
    selectedItemId,
    selectItem,
    connections,
    handleWheel,
    handleCanvasPointerDown: originalHandleCanvasPointerDown,
    presentationMode,
    isGrabbing,
    boardDimensions,
    setBoard,
    remoteCursors,
  } = useBoard();

  // Add a ref to track new comments
  const newCommentRef = useRef<string | null>(null);

  // --- Add Auth and Modal State ---
  const { isAuthenticated, user, isUsernameSetupRequired, completeUserProfile } = useAuth();
  const [isSignInOpen, setIsSignInOpen] = useState(false);
  const [isSignUpOpen, setIsSignUpOpen] = useState(false);
  const [isVerificationOpen, setIsVerificationOpen] = useState(false);
  const [isUsernameSetupOpen, setIsUsernameSetupOpen] = useState(false);
  const [verificationEmail, setVerificationEmail] = useState('');
  const [verificationUserId, setVerificationUserId] = useState<string | undefined>(undefined);
  const [verificationSentAt, setVerificationSentAt] = useState<string | undefined>();
  // --- End Auth and Modal State ---

  // --- LOADING SCREEN COMPONENT ---
  const LoadingOverlay = () => {
    if (!isBoardLoading && loadingProgress >= 100) return null;
    
    return (
      <div className="fixed inset-0 flex flex-col items-center justify-center bg-noir-900 z-[100]">
        <Loader2 className="h-16 w-16 animate-spin text-noir-accent mb-6" />
        <p className="text-white text-xl font-medium mb-2">Loading detective board...</p>
        <p className="text-gray-400 text-sm">
          {loadingProgress < 30 
            ? "Preparing your investigation..." 
            : loadingProgress < 60 
            ? "Loading elements..." 
            : loadingProgress < 90 
            ? "Arranging connections..." 
            : "Almost ready..."}
        </p>
        
        {/* Simple progress bar */}
        <div className="mt-8 w-64 bg-gray-700 rounded-full h-2 overflow-hidden">
          <div 
            className="bg-noir-accent h-2 rounded-full transition-all duration-300 ease-out"
            style={{ width: `${loadingProgress}%` }}
          ></div>
        </div>
      </div>
    );
  };
  
  // Add a smaller loading indicator for when elements are still loading
  const LoadingIndicator = () => {
    const totalItems = board.elements.length;
    const loadedItems = loadedItemIds.size;
    
    // If everything is loaded, don't show indicator
    if (loadedItems >= totalItems && !isBoardLoading) return null;
    
    return (
      <div className="absolute bottom-4 right-4 glass-morphism px-3 py-2 rounded-md text-white text-sm z-50 flex items-center space-x-2">
        <div className="w-4 h-4 relative">
          <Loader2 className="w-4 h-4 animate-spin absolute" />
        </div>
        <div className="flex flex-col">
          <span>Loading {loadedItems}/{totalItems} items</span>
          <div className="w-full bg-gray-600 rounded-full h-1.5 mt-1 overflow-hidden">
            <div 
              className="bg-blue-500 h-1.5 rounded-full transition-all duration-300 ease-out"
              style={{ width: `${(loadedItems / Math.max(totalItems, 1)) * 100}%` }}
            ></div>
          </div>
        </div>
      </div>
    );
  };

  // Function to get items in current viewport - used for loading prioritization
  const getItemsInViewport = useCallback(() => {
    if (!boardContainerRef.current) return [];
    
    const containerRect = boardContainerRef.current.getBoundingClientRect();
    const viewportX = -position.x / scale;
    const viewportY = -position.y / scale;
    const viewportWidth = containerRect.width / scale;
    const viewportHeight = containerRect.height / scale;
    
    // Create a slightly larger viewport for pre-loading nearby items
    const expandedViewport = {
      minX: viewportX - viewportWidth * 0.5,
      minY: viewportY - viewportHeight * 0.5,
      maxX: viewportX + viewportWidth * 1.5,
      maxY: viewportY + viewportHeight * 1.5
    };
    
    return board.elements.filter(item => {
      if (!item.position) return false;
      
      const itemRight = item.position.x + (item.width || 0);
      const itemBottom = item.position.y + (item.height || 0);
      
      return (
        item.position.x < expandedViewport.maxX &&
        itemRight > expandedViewport.minX &&
        item.position.y < expandedViewport.maxY &&
        itemBottom > expandedViewport.minY
      );
    });
  }, [board.elements, position, scale]);

  // Progressive loading effect - similar to DetectiveBoard
  useEffect(() => {
    if (board.elements.length === 0) {
      // Empty board or still fetching
      setLoadingProgress(10);
      return;
    }
    
    // On initial load, only load items in the viewport
    if (isInitialLoad && board.elements.length > 0) {
      setLoadingProgress(20);
      const itemsInView = getItemsInViewport();
      const initialItems = itemsInView.length > 0 
        ? itemsInView 
        : board.elements.slice(0, Math.min(15, board.elements.length));
      
      setLoadedItemIds(new Set(initialItems.map(item => item.id)));
      setIsInitialLoad(false);
      setLoadingProgress(50);
      
      // Start a timer to simulate progress towards 90%
      const timer = setTimeout(() => {
        setLoadingProgress(90);
      }, 300);
      
      return () => clearTimeout(timer);
    }
    
    // After initial load, keep loading more items in batches
    if (!loadingBatch && loadedItemIds.size < board.elements.length) {
      setLoadingBatch(true);
      
      setTimeout(() => {
        // Prioritize items in viewport that aren't loaded yet
        const itemsInView = getItemsInViewport()
          .filter(item => !loadedItemIds.has(item.id));
        
        // If no items in view need loading, load some off-screen items
        const itemsToLoad = itemsInView.length > 0 
          ? itemsInView 
          : board.elements
              .filter(item => !loadedItemIds.has(item.id))
              .slice(0, 10); // Load 10 at a time
        
        if (itemsToLoad.length > 0) {
          const newLoadedIds = new Set(loadedItemIds);
          itemsToLoad.forEach(item => newLoadedIds.add(item.id));
          setLoadedItemIds(newLoadedIds);
          
          // Calculate new progress
          const newProgress = Math.min(95, 50 + (newLoadedIds.size / board.elements.length * 50));
          setLoadingProgress(newProgress);
        } else {
          // All items loaded
          setLoadingProgress(100);
          setTimeout(() => setIsBoardLoading(false), 500);
        }
        
        setLoadingBatch(false);
      }, 100); // Short delay to not block rendering
    } else if (loadedItemIds.size >= board.elements.length) {
      // All items loaded
      setLoadingProgress(100);
      setTimeout(() => setIsBoardLoading(false), 500);
    }
  }, [board.elements, loadedItemIds, isInitialLoad, loadingBatch, getItemsInViewport]);

  // Update loaded items when viewport changes
  useEffect(() => {
    if (isInitialLoad) return;
    
    // When user navigates, prioritize loading items in the new viewport
    const itemsInView = getItemsInViewport();
    const unloadedViewportItems = itemsInView.filter(item => !loadedItemIds.has(item.id));
    
    if (unloadedViewportItems.length > 0) {
      const newLoadedIds = new Set(loadedItemIds);
      unloadedViewportItems.forEach(item => newLoadedIds.add(item.id));
      setLoadedItemIds(newLoadedIds);
    }
  }, [position, scale, isInitialLoad, getItemsInViewport, loadedItemIds]);

  // --- Initialize like count and status from board data fetched by context ---
  useEffect(() => {
    // Add logging to see when this runs and what data it gets
    console.log("Running like state initialization useEffect. Board data:", board);

    let initialLikeCount = 0;
    let initialLikedStatus = false; // Default to false

    // Check for sharing data to get the total count
    if (board?.sharing && Array.isArray(board.sharing) && board.sharing.length > 0) {
      const likesValue = board.sharing[0].likes;
      console.log("Extracted likes value:", likesValue);
      // Ensure likesValue is a number before setting
      if (typeof likesValue === 'number') {
        initialLikeCount = likesValue;
      } else {
        console.warn("Likes value from board.sharing is not a number:", likesValue);
      }
    } else {
      console.log("Board sharing data not found or empty.");
    }

    // Check for the specific user's like status from the API response
    const boardLikedStatus = (board as any)?.liked;
    console.log("Extracted liked status:", boardLikedStatus);
    if (typeof boardLikedStatus === 'boolean') { 
        initialLikedStatus = boardLikedStatus;
    }

    // Update state based on fetched data
    console.log("Setting likeCount state to:", initialLikeCount);
    setLikeCount(initialLikeCount);
    console.log("Setting hasLiked state to:", initialLikedStatus);
    setHasLiked(initialLikedStatus); // Directly set based on API or default

  // Depend on the board object reference itself. If it changes, the effect runs.
  }, [board, setLikeCount, setHasLiked]); 
  // --- End Initialize State Hook ---

  // Modified pointer down handler to include background deselection
  const handleBoardPointerDown = useCallback((event: React.PointerEvent<Element>) => {
    // Skip if the event target has data-nodrag attribute
    if (event.target instanceof HTMLElement && 
        (event.target.getAttribute('data-nodrag') === 'true' || 
         event.target.closest('[data-nodrag="true"]'))) {
      event.stopPropagation();
      return;
    }
    
    if (event.target === event.currentTarget ||
        (event.target instanceof HTMLElement && event.target.closest('[data-testid="board-motion-div"]') !== event.currentTarget.querySelector('[data-testid="board-motion-div"]'))
       ) {
      if (document.activeElement instanceof HTMLElement) {
          document.activeElement.blur();
      }
      window.getSelection()?.removeAllRanges();
      selectItem(null);
    }
    originalHandleCanvasPointerDown(event);
  }, [originalHandleCanvasPointerDown, selectItem]);

  // Handle back button click
  const handleBackClick = () => {
    setIsBackLoading(true);
    // Determine where we should return to
    let destination = '/public-boards';
    
    // Always navigate directly without confirmation
    router.push(destination);
  };

  // Handle sidebar resize
  const startResizing = useCallback((e: MouseEvent) => {
    e.preventDefault();
    setIsResizing(true);
  }, []);

  const stopResizing = useCallback(() => {
    setIsResizing(false);
  }, []);

  const resize = useCallback((e: MouseEvent) => {
    if (!isResizing) return;
    
    const newWidth = window.innerWidth - e.clientX;
    if (newWidth >= 320 && newWidth <= 600) {
      setSidebarWidth(newWidth);
    }
  }, [isResizing]);

  useEffect(() => {
    if (isResizing) {
      window.addEventListener('mousemove', resize);
      window.addEventListener('mouseup', stopResizing);
    }

    return () => {
      window.removeEventListener('mousemove', resize);
      window.removeEventListener('mouseup', stopResizing);
    };
  }, [isResizing, resize, stopResizing]);

  // Handle board items rendering with connection properties
  const renderBoardItems = useCallback(() => {
    // Only render items that have been marked as loaded
    return board.elements
      .filter(item => loadedItemIds.has(item.id))
      .map((item: BoardItem) => {
      // Create basic props that all read-only components need
      const baseProps = {
        id: item.id,
        content: item.content,
        position: item.position,
        scale: scale,
        width: item.width, // This can be undefined
        height: item.height // This can be undefined
      };

      if (item.type === 'sticky' || item.type.startsWith('sticky-')) {
        // Extract color from type if it's in the format sticky-{color}
        let color = item.color || 'yellow';
        if (item.type.startsWith('sticky-')) {
          color = item.type.split('-')[1];
        }
        
        return (
          <StickyNoteReadOnly
            key={item.id}
            {...baseProps}
            color={color}
          />
        );
      } else if (item.type === 'text') {
        return (
          <TextNodeReadOnly
            key={item.id}
            {...baseProps}
          />
        );
      } else if (item.type === 'article' && item.title && item.url) {
        return (
          <ArticleNodeReadOnly
            key={item.id}
            {...baseProps}
            width={baseProps.width ?? 300} // Provide default if undefined
            height={baseProps.height ?? 200} // Provide default if undefined
            title={item.title}
            url={item.url}
            website_url={item.website_url}
            imageUrl={item.imageUrl}
          />
        );
      } else if (isImageNode(item)) {
        // For image nodes, distinguish between the image source and the caption text.
        const imageSource = item.imageUrl || item.file_url; // This is the actual path to the image
        const captionText = item.content; // This is the text to be displayed as caption
        const altText = item.alt || item.title || 'Image'; // Provide alt text

        return (
          <ImageNodeReadOnly
            key={item.id}
            {...baseProps}
            imageUrl={imageSource} // Pass the correct image source
            content={captionText}  // Pass the caption text to the 'content' prop
            alt={altText}          // Pass alt text for accessibility
          />
        );
      }
      return null;
    });
  }, [board.elements, loadedItemIds, scale]);

  // Function to render connections - modified to check for loaded items
  const renderConnections = useCallback(() => {
    if (!connections || connections.length === 0 || !board.elements) return null;

    // Determine simplification level based on total number of connections
    // rather than just visible connections
    let simplificationLevel = 0;
    const totalConnections = connections.length;
    
    if (totalConnections > 50) {
      simplificationLevel = 2; // Highest simplification for very large boards
    } else if (totalConnections > 30) {
      simplificationLevel = 1; // Moderate simplification
    }

    // Filter to only show connections where both items are loaded
    return connections
      .filter(connection => {
        return loadedItemIds.has(connection.fromId) && loadedItemIds.has(connection.toId);
      })
      .map(connection => {
        const fromElement = board.elements.find((el: BoardItem) => el.id === connection.fromId);
        const toElement = board.elements.find((el: BoardItem) => el.id === connection.toId);

        if (!fromElement || !toElement) {
          console.warn(`[PublicBoardView] Could not find elements for connection ${connection.id}. From: ${connection.fromId}, To: ${connection.toId}`);
          return null;
        }

        // Prepare props for ConnectionReadOnly, now including width/height
        const fromProp = { 
          id: fromElement.id, 
          position: fromElement.position, 
          width: fromElement.width, 
          height: fromElement.height 
        };
        const toProp = { 
          id: toElement.id, 
          position: toElement.position, 
          width: toElement.width, 
          height: toElement.height 
        };

        return (
          <ConnectionReadOnly
            key={connection.id}
            id={connection.id}
            from={fromProp}
            to={toProp}
            simplificationLevel={simplificationLevel}
          />
        );
    });
  }, [connections, board.elements, loadedItemIds, scale, presentationMode]);

  // --- RENDER STROKES using StrokeRenderer ---
  const renderStrokes = useCallback(() => {
    // Pass the entire strokes array to StrokeRenderer
    // StrokeRenderer likely handles iterating and drawing each path
    return <StrokeRenderer strokes={board.strokes || []} scale={scale} />;
  }, [board.strokes, scale]);
  // --- END RENDER STROKES FUNCTION ---

  // Prepare view state object for BoardCanvas
  const boardViewState: PublicBoardViewState = useMemo(() => ({
    scale,
    position,
    selectedItemId: null,
    isGrabbing,
    presentationMode
  }), [scale, position, isGrabbing, presentationMode]);

  // Check if the board is public
  const isBoardPublic = useMemo(() => {
    
    if (board?.sharing && Array.isArray(board.sharing) && board.sharing.length > 0) {
      
      // Check if any sharing record has public_board set to true
      const isPublic = board.sharing.some(share => {
        // Check the exact property
        return share.public_board === true;
      });
      
      return isPublic;
    }
    return false;
  }, [board.sharing]);

  // Log the public status for debugging
  useEffect(() => {
    console.log("Board public status:", {
      isBoardPublic,
      sharingRecords: board?.sharing || []
    });
  }, [isBoardPublic, board.sharing]);

  // --- Add Modal Handlers ---
  const handleVerificationNeeded = useCallback((email: string, userId?: string) => {
    setVerificationEmail(email);
    setVerificationUserId(userId);
    setVerificationSentAt(new Date().toISOString());
    setIsSignUpOpen(false);
    setIsSignInOpen(false);
    setIsVerificationOpen(true);
  }, []);

  // --- Define fetchComments first as it's needed by handleAuthSuccess ---
  const fetchComments = useCallback(async () => {
    if (!boardId) return;

    try {
      const response = await fetch(`/api/board/comment?boardId=${boardId}`);
      if (!response.ok) throw new Error('Failed to fetch comments');
      const data = await response.json();
      if (data.success && Array.isArray(data.comments)) {
        // --- FIX: Pass raw API data directly to organizeComments --- 
        // Remove the premature mapping to formattedComments here.
        // const formattedComments: Comment[] = data.comments.map(/* ... */); 

        // organizeComments handles the conversion internally.
        const organizedComments = organizeComments(data.comments as ApiComment[]); 
        setComments(organizedComments);
        // --- END FIX ---
      } else if (data.success && data.comments.length === 0) {
        setComments([]);
      } else {
        console.error("Failed to fetch comments: Invalid data format", data);
        toast.error("Failed to load comments (invalid format).");
      }
    } catch (error) {
      console.error('Error fetching comments:', error);
      toast.error("An error occurred while loading comments.");
    }
  }, [boardId]);

  const handleAuthSuccess = useCallback(async () => {
    // Close all modals on successful sign-in/sign-up/verification
    setIsSignInOpen(false);
    setIsSignUpOpen(false);
    setIsVerificationOpen(false);

    // Check if username setup is required
    if (isUsernameSetupRequired) {
      setIsUsernameSetupOpen(true);
    } else {
      // Re-fetch comments to get user-specific data like votes
      if (boardId) {
        await fetchComments(); // Call the defined fetchComments
      }
      // Optionally re-fetch board details if needed
    }
  }, [boardId, fetchComments, isUsernameSetupRequired]);

  // Function to wrap actions requiring authentication
  const requireAuth = useCallback((action: () => void) => {
    if (!isAuthenticated) {
      setIsSignInOpen(true); // Open sign-in modal if not logged in
    } else {
      action(); // Execute the action if logged in
    }
  }, [isAuthenticated]);
  // --- End Modal Handlers ---

  // Handle like button click
  const handleLike = async () => {
    requireAuth(() => {
      if (!boardId) return;
      
      try {
        if (hasLiked) {
          // Unlike flow (optimistic UI + API call)
          setLikeCount(prev => Math.max(prev - 1, 0));
          setHasLiked(false);
          
          fetch(`/api/board/like?boardId=${boardId}`, { method: 'DELETE' })
            .then(response => {
              if (!response.ok) throw new Error('Unlike failed');
            })
            .catch(() => { // Revert on failure
              setLikeCount(prev => prev + 1);
              setHasLiked(true);
              toast.error('Failed to unlike board');
            });
        } else {
          // Like flow (optimistic UI + API call)
          setLikeCount(prev => prev + 1);
          setHasLiked(true);
          
          fetch(`/api/board/like`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ boardId })
          })
            .then(response => {
              if (!response.ok) throw new Error('Like failed');
            })
            .catch(() => { // Revert on failure
              setLikeCount(prev => Math.max(prev - 1, 0));
              setHasLiked(false);
              toast.error('Failed to like board');
            });
        }
      } catch (error) { // Catch synchronous errors if any setup fails
        console.error('Error initiating like/unlike:', error);
        toast.error('Failed to update like status');
      }
    });
  };

  // Toggle comment sidebar
  const toggleCommentSidebar = () => {
    setIsCommentSidebarOpen(prev => !prev);
  };

  // Update handleCommentSubmit
  const handleCommentSubmit = async () => {
    requireAuth(async () => {
      if (!commentText.trim() || !boardId) return;

      const optimisticComment = {
        id: uuidv4(), // Temporary ID
        text: commentText.trim(),
        timestamp: new Date().toISOString(),
        score: 0,
        parent_comment_id: null,
        username: user?.username || 'You', // Use authenticated username if available
        is_edited: false,
        userVote: null,
        user_id: user?.id // Include user ID
      };

      const tempId = optimisticComment.id;
      newCommentRef.current = tempId;

      try {
        setComments(prev => [optimisticComment, ...prev]);
        setCommentText('');

        const response = await fetch('/api/board/comment', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            boardId,
            comment: optimisticComment.text
          })
        });

        const data = await response.json();

        if (!response.ok) {
          setComments(prev => prev.filter(comment => comment.id !== tempId));
          toast.error(data.error || 'Failed to add comment');
        } else {
          if (data.comment) {
            const serverComment = data.comment;
            const likes = serverComment.likes || 0;
            const dislikes = serverComment.dislikes || 0;
            const finalComment = {
              id: serverComment.id,
              text: serverComment.content,
              timestamp: serverComment.created_at,
              score: likes - dislikes,
              parent_comment_id: serverComment.parent_comment_id,
              username: serverComment.username || 'Anonymous',
              is_edited: serverComment.is_edited || false,
              userVote: null,
              user_id: serverComment.user_id // Ensure API returns user_id
            };
            setComments(prev => [finalComment, ...prev.filter(comment => comment.id !== tempId)]);
          } else {
             setComments(prev => prev.filter(comment => comment.id !== tempId));
          }
        }
      } catch (error) {
        setComments(prev => prev.filter(comment => comment.id !== tempId));
        toast.error('Failed to add comment');
      } finally {
        setTimeout(() => { newCommentRef.current = null; }, 300);
      }
    });
  };

  // Helper function to update a comment recursively within the tree
  const updateCommentInTree = useCallback((
    commentList: Comment[],
    commentId: string,
    updateFn: (comment: Comment) => Comment
  ): Comment[] => {
    return commentList.map(comment => {
      if (comment.id === commentId) {
        return updateFn(comment);
      } else if (comment.replies && comment.replies.length > 0) {
        return { ...comment, replies: updateCommentInTree(comment.replies, commentId, updateFn) };
      }
      return comment;
    });
  }, []);

  // --- Helper function to add or update a vote via POST ---
  const addOrUpdateVote = useCallback(async (commentId: string, reactionType: 'like' | 'dislike', optimisticUserVote: 'like' | 'dislike' | null) => {
    // This function assumes optimistic update HAS ALREADY HAPPENED in handleVote
    // It handles the API call and the final state update/rollback
    const response = await fetch('/api/board/comment/reaction', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ commentId, type: reactionType })
    });

    const data = await response.json();

    if (!response.ok) {
      // Throw an error to be caught by handleVote for rollback
      throw new Error(data.error || `Failed to ${reactionType} comment`);
    }

    // --- Final Update from POST Response ---
    // Trust the optimisticUserVote from handleVote as the source of truth for user's intent
    // Score is handled by DB trigger.
    setComments(prev => updateCommentInTree(prev, commentId, c => ({
        ...c,
        userVote: optimisticUserVote 
    })));
    // --- End Final Update ---
  }, [updateCommentInTree]);

  // --- REFACTORED handleVote ---
  const handleVote = async (commentId: string, reactionType: 'like' | 'dislike') => {
    requireAuth(async () => {
      const originalComments = [...comments];
      let optimisticUserVote: 'like' | 'dislike' | null = reactionType;
      let scoreAdjustment = 0;
      const originalComment = findCommentById(originalComments, commentId);
      const originalUserVote = originalComment?.userVote;

      const findAndUpdateOptimistically = (list: Comment[]): Comment[] => {
          return list.map(c => {
              if (c.id === commentId) {
                  const currentVote = c.userVote;
                  let newScore = c.score;
                  if (currentVote === reactionType) {
                      optimisticUserVote = null;
                      scoreAdjustment = reactionType === 'like' ? -1 : 1;
                  } else {
                      optimisticUserVote = reactionType;
                      scoreAdjustment = reactionType === 'like' ? 1 : -1;
                      if (currentVote) {
                          scoreAdjustment += (currentVote === 'like' ? -1 : 1);
                      }
                  }
                  newScore += scoreAdjustment;
                  return { ...c, score: newScore, userVote: optimisticUserVote };
              } else if (c.replies) {
                  return { ...c, replies: findAndUpdateOptimistically(c.replies) };
              }
              return c;
          });
      };
      setComments(findAndUpdateOptimistically(comments));

      try {
        if (originalUserVote === reactionType) {
          await removeVote(commentId, true);
        } else if (originalUserVote && originalUserVote !== reactionType) {
          await removeVote(commentId, true);
          await addOrUpdateVote(commentId, reactionType, optimisticUserVote);
        } else {
          await addOrUpdateVote(commentId, reactionType, optimisticUserVote);
        }
      } catch (error: any) {
        console.error(`Error voting on comment:`, error);
        toast.error(error.message || `Failed to vote on comment`);
        setComments(originalComments);
      }
    });
  };

  // --- REFACTORED removeVote ---
  // Added skipOptimistic flag because handleVote now calls this after its own optimistic update
  const removeVote = useCallback(async (commentId: string, skipOptimistic: boolean = false) => {
      let originalCommentsForRemove = [...comments];

      if (!skipOptimistic) {
         const findAndUpdateOptimistically = (list: Comment[]): Comment[] => {
             return list.map(c => {
                 if (c.id === commentId) {
                     const currentVote = c.userVote;
                     if (!currentVote) return c;
                     const scoreAdjustment = currentVote === 'like' ? -1 : 1;
                     return { ...c, score: c.score + scoreAdjustment, userVote: null };
                 } else if (c.replies) {
                     return { ...c, replies: findAndUpdateOptimistically(c.replies) };
                 }
                 return c;
             });
         };
         setComments(findAndUpdateOptimistically(comments));
      }

      try {
        const response = await fetch(`/api/board/comment/reaction?commentId=${commentId}`, { method: 'DELETE' });
        const data = await response.json();
        if (!response.ok) throw new Error(data.error || 'Failed to remove vote');

        setComments(prev => updateCommentInTree(prev, commentId, c => ({ ...c, userVote: null })));

      } catch (error) {
        console.error('Error removing vote:', error);
        throw error;
      }
  }, [comments, updateCommentInTree]);

  // Helper to find a comment by ID recursively (needed for optimistic check in handleVote)
  const findCommentById = useCallback((commentList: Comment[], commentId: string): Comment | null => {
      for (const comment of commentList) {
          if (comment.id === commentId) {
              return comment;
          }
          if (comment.replies) {
              const foundInReply = findCommentById(comment.replies, commentId);
              if (foundInReply) {
                  return foundInReply;
              }
          }
      }
      return null;
  }, []);

  // --- Function to organize comments (definition needed before fetchComments) ---
  const organizeComments = useCallback((apiComments: ApiComment[]): Comment[] => {
    // Keep Comment interface clean, no need for hotScore property here
    const commentMap = new Map<string, Comment & { user_id?: string }>(); 
    const rootComments: Comment[] = [];

    // Map API data to internal Comment structure (without hotScore)
    const formattedComments: (Comment & { user_id?: string })[] = apiComments.map(c => {
        const likes = c.likes || 0;
        const dislikes = c.dislikes || 0;
        const score = likes - dislikes;

        return {
            id: c.id,
            text: c.content,
            timestamp: c.created_at,
            score: score,
            parent_comment_id: c.parent_comment_id,
            userVote: c.userVote || null,
            is_edited: c.is_edited || false,
            username: c.username || 'Anonymous',
            user_id: c.user_id,
            // hotScore property removed
            replies: []
        };
    });

    formattedComments.forEach(comment => {
      commentMap.set(comment.id, { ...comment, replies: [] });
    });

    formattedComments.forEach(comment => {
      const processedComment = commentMap.get(comment.id)!;
      if (comment.parent_comment_id) {
        const parent = commentMap.get(comment.parent_comment_id);
        if (parent) {
          parent.replies = parent.replies || [];
          parent.replies.push(processedComment);
        } else {
           rootComments.push(processedComment);
        }
      } else {
        rootComments.push(processedComment);
      }
    });

    // --- Sort root comments by hot score descending (calculate score inline) ---
    rootComments.sort((a, b) => {
        const scoreA = calculateHotScore(a.score > 0 ? a.score : 0, a.score < 0 ? -a.score : 0, a.timestamp);
        const scoreB = calculateHotScore(b.score > 0 ? b.score : 0, b.score < 0 ? -b.score : 0, b.timestamp);
        return scoreB - scoreA; // Descending order
    });
    
    // --- Recursively sort replies by hot score descending (calculate score inline) ---
    const sortReplies = (comments: Comment[]) => {
        comments.forEach(c => {
            if (c.replies && c.replies.length > 0) {
                c.replies.sort((a, b) => {
                    const scoreA = calculateHotScore(a.score > 0 ? a.score : 0, a.score < 0 ? -a.score : 0, a.timestamp);
                    const scoreB = calculateHotScore(b.score > 0 ? b.score : 0, b.score < 0 ? -b.score : 0, b.timestamp);
                    return scoreB - scoreA; // Descending order
                });
                sortReplies(c.replies);
            }
        });
    };
    sortReplies(rootComments);

    return rootComments;
  }, []);

  // Update handleReplySubmit
  const handleReplySubmit = async (parentCommentId: string) => {
    requireAuth(async () => {
      if (!replyText.trim() || !boardId) return;

      const tempId = uuidv4();
      newCommentRef.current = tempId;
      const replyContent = replyText.trim();
      
      setReplyText('');
      setReplyingTo(null);

      try {
        const response = await fetch('/api/board/comment', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            boardId,
            comment: replyContent,
            parentCommentId
          })
        });

        const data = await response.json();

        if (!response.ok) {
          toast.error(data.error || 'Failed to add reply');
          setReplyText(replyContent);
          setReplyingTo(parentCommentId);
        } else {
          await fetchComments();
        }
      } catch (error) {
        console.error('Error adding reply:', error);
        toast.error('Failed to add reply');
        setReplyText(replyContent);
        setReplyingTo(parentCommentId);
      } finally {
        setTimeout(() => { newCommentRef.current = null; }, 300);
      }
    });
  };

  // Function to handle saving an edited comment
  const handleEditSave = async (commentId: string, newText: string) => {
    requireAuth(async () => {
      try {
        const response = await fetch('/api/board/comment', {
          method: 'PATCH',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ commentId, content: newText }),
        });
        const data = await response.json();
        if (!response.ok) {
          toast.error(data.error || 'Failed to update comment');
          if (response.status === 401 || response.status === 403) {
              toast.error('You do not have permission to edit this comment.');
          }
        }
        await fetchComments();
      } catch (error) {
        console.error('Error updating comment:', error);
        toast.error('Failed to update comment');
        await fetchComments();
      }
    });
  };

  // Fetch comments when the component mounts or boardId changes
  useEffect(() => {
    if (boardId) {
      fetchComments();
    }
  }, [boardId, fetchComments]);

  // --- Fetch comments on mount/boardId change ---
  useEffect(() => { if (boardId) fetchComments(); }, [boardId, fetchComments]);

  // --- Record board view on mount (MODIFIED) --- 
  useEffect(() => {
    // Only run if we have a valid boardId AND the loaded board's ID matches
    // This ensures the main board data fetch has completed successfully.
    if (boardId && boardId !== 'new' && board.id === boardId) {
      const storageKey = `viewed_board_${boardId}`;
      
      // Check session storage to see if we already recorded a view this session
      const hasViewedThisSession = sessionStorage.getItem(storageKey);

      if (!hasViewedThisSession) {
        // If not viewed this session, call the API and set the flag
        console.log(`Attempting to record view for board ${boardId} (Board loaded: ${board.id})`); // Add log
        fetch('/api/board/view', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ boardId })
        })
        .then(async response => { // Make async to potentially read body
          if (response.ok) {
            // Mark as viewed in session storage upon successful API call
            sessionStorage.setItem(storageKey, 'true');
            console.log(`Recorded view for board ${boardId}`);
          } else {
            // Log the status and potentially error message from API
            const errorData = await response.json().catch(() => ({})); // Try to get error details
            console.error(`Failed to record board view via API for ${boardId}. Status: ${response.status}`, errorData);
          }
        })
        .catch(error => {
          console.error(`Error calling /api/board/view for ${boardId}:`, error);
        });
      } else {
         console.log(`View already recorded this session for board ${boardId}`); // Add log
      }
    }
    // --- Dependencies: Run when boardId changes OR when the loaded board.id changes ---
  }, [boardId, board.id]); 

  // --- Calculate total comment count ---
  const countTotalComments = useCallback((list: Comment[]): number => list.reduce((sum, c) => sum + 1 + (c.replies ? countTotalComments(c.replies) : 0), 0), []);

  // Calculate total comment count using useMemo
  const totalCommentCount = useMemo(() => {
    return countTotalComments(comments);
  }, [comments]);

  // Effect to add/remove body class for no-scroll styling and apply dynamic styles
  useEffect(() => {
    document.body.classList.add('body-no-scroll-for-board');

    const adjustBodyStylesForBoard = () => {
      if (typeof window !== 'undefined') {
        document.body.style.height = `${window.innerHeight}px`;
        document.body.style.overflow = 'hidden';
        document.body.style.position = 'fixed';
        document.body.style.top = '0';
        document.body.style.left = '0';
        document.body.style.width = '100%';
        document.documentElement.style.overflow = 'hidden';
      }
    };

    adjustBodyStylesForBoard(); // Initial adjustment

    window.addEventListener('resize', adjustBodyStylesForBoard);
    window.addEventListener('orientationchange', adjustBodyStylesForBoard);

    return () => {
      console.log('[PublicBoardView] Unmounting and removing anti-scroll styles.');
      document.body.classList.remove('body-no-scroll-for-board');

      // Remove inline styles that were added by adjustBodyStylesForBoard
      document.body.style.removeProperty('height');
      document.body.style.removeProperty('overflow');
      document.body.style.removeProperty('position');
      document.body.style.removeProperty('top');
      document.body.style.removeProperty('left');
      document.body.style.removeProperty('width');
      document.documentElement.style.removeProperty('overflow');

      window.removeEventListener('resize', adjustBodyStylesForBoard);
      window.removeEventListener('orientationchange', adjustBodyStylesForBoard);
    };
  }, []); // Empty dependency array means this runs on mount and unmount

  return (
    <>
      <div className="relative h-screen w-screen overflow-hidden bg-gray-100 dark:bg-gray-900 flex flex-col">
        {/* Loading overlay */}
        <LoadingOverlay />
        
        <div className="absolute top-4 left-4 z-50">
           <button
              onClick={handleBackClick}
              className="p-2 glass-morphism rounded-full text-white hover:bg-noir-50 transition-colors"
              aria-label="Back to dashboard"
              disabled={isBackLoading}
           >
              {isBackLoading ? (
                <Loader2 size={20} className="animate-spin" />
              ) : (
                <ArrowLeft size={20} />
              )}
           </button>
        </div>

        {/* Zoom Slider */}
        {setScale && (
          <div
            className="z-50"
            style={{
              position: 'fixed',
              top: '50%',
              right: isCommentSidebarOpen ? `${sidebarWidth + 16}px` : '1rem',
              transform: 'translateY(-50%)'
            }}
          >
            <ZoomSlider
              scale={scale}
              setScale={setScale}
              minScale={MIN_SCALE}
              maxScale={MAX_SCALE}
              presentationMode={presentationMode}
            />
          </div>
        )}

        {isBoardPublic && (
          <div 
            className={`absolute top-4 z-50 flex space-x-2 transition-all duration-300`}
            style={{
              right: isCommentSidebarOpen ? `${sidebarWidth + 16}px` : '1rem'
            }}
          >
            <button
              onClick={handleLike}
              className={`p-2 glass-morphism rounded-full text-white flex items-center gap-1 ${hasLiked ? 'text-red-500 hover:bg-noir-100' : 'hover:bg-noir-50'} transition-colors`}
              aria-label={hasLiked ? "Unlike board" : "Like board"}
            >
              <Heart size={18} className={hasLiked ? 'fill-red-500 text-red-500' : ''} />
              <span className="text-sm font-medium ml-1">{likeCount}</span>
            </button>
            
            <button
              onClick={toggleCommentSidebar}
              className={`p-2 glass-morphism rounded-full text-white hover:bg-noir-50 transition-colors ${isCommentSidebarOpen ? 'bg-noir-50' : ''} flex items-center gap-1`}
              aria-label="Open comments"
            >
              <MessageSquare size={18} />
              {totalCommentCount > 0 && (
                <span className="text-sm font-medium ml-1">{totalCommentCount}</span>
              )}
            </button>
          </div>
        )}

        <div className="flex-1 relative">
          <PublicBoardCanvas
            viewState={boardViewState}
            onWheel={handleWheel}
            onCanvasPointerDown={handleBoardPointerDown}
            containerRef={boardContainerRef}
            boardDimensions={boardDimensions}
          >
            <div data-testid="board-items-container" style={{ position: 'relative', width: '100%', height: '100%' }}>
                {renderBoardItems()}
                {renderConnections()}
                {renderStrokes()}
                <RemoteCursors cursors={remoteCursors} />
            </div>
          </PublicBoardCanvas>
          
          {/* Loading indicator for when elements are still loading */}
          <LoadingIndicator />
        </div>

        <div 
          ref={sidebarRef}
          style={{ 
            width: sidebarWidth,
            transform: isCommentSidebarOpen ? 'translateX(0)' : 'translateX(100%)'
          }}
          className={styles.sidebar}
        >
          <div 
            ref={resizeHandleRef}
            className={`${styles['resize-handle']} ${isResizing ? styles.resizing : ''}`}
            onMouseDown={(e) => startResizing(e as unknown as MouseEvent)}
          />
          <div className={styles['sidebar-content']}>
            <div className="p-4 flex flex-col h-full">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-bold text-white">Comments</h2>
                <button 
                  onClick={toggleCommentSidebar}
                  className="text-white hover:text-gray-300"
                  aria-label="Close comments"
                >
                  <X size={20} />
                </button>
              </div>
              
              <div className="flex-1 overflow-y-auto mb-4">
                {comments.length > 0 ? (
                  <div className="space-y-3">
                    {comments.map(comment => (
                      <CommentComponent 
                        key={comment.id} 
                        comment={comment} 
                        level={0}
                        replyingTo={replyingTo}
                        replyText={replyText}
                        setReplyText={setReplyText}
                        handleVote={handleVote}
                        setReplyingTo={setReplyingTo}
                        handleReplySubmit={handleReplySubmit}
                        currentNewCommentId={newCommentRef.current}
                        isNewComment={comment.id === newCommentRef.current}
                        handleEditSave={handleEditSave}
                        isUserLoggedIn={isAuthenticated}
                        userId={user?.id}
                        isBoardPublic={isBoardPublic}
                      />
                    ))}
                  </div>
                ) : (
                  <div className={styles.noCommentsMessage}>
                    <p>No comments yet. Be the first to comment!</p>
                  </div>
                )}
              </div>
              
              {isBoardPublic && (
                <div className="mt-auto">
                  <textarea
                    value={commentText}
                    onChange={(e) => setCommentText(e.target.value)}
                    placeholder={isAuthenticated ? "Write your comment..." : "Sign in to comment..."}
                    className={styles['add-comment-input']}
                    rows={3}
                    disabled={!isAuthenticated}
                  />
                  <button
                    onClick={handleCommentSubmit}
                    disabled={!commentText.trim() || !isAuthenticated}
                    className={styles.commentSubmitButton}
                  >
                    Add Comment
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      <SignInModal
        isOpen={isSignInOpen}
        onClose={() => setIsSignInOpen(false)}
        onVerificationNeeded={handleVerificationNeeded}
        onSuccess={handleAuthSuccess}
        onSignUpClick={() => {
          setIsSignInOpen(false);
          setIsSignUpOpen(true);
        }}
      />

      <SignUpModal
        isOpen={isSignUpOpen}
        onClose={() => setIsSignUpOpen(false)}
        onSuccess={handleAuthSuccess}
        onSignInClick={() => {
          setIsSignUpOpen(false);
          setIsSignInOpen(true);
        }}
        onVerificationNeeded={handleVerificationNeeded}
      />

      {isVerificationOpen && (
        <VerificationModal
          isOpen={isVerificationOpen}
          onClose={() => setIsVerificationOpen(false)}
          email={verificationEmail}
          userId={verificationUserId}
          verificationSentAt={verificationSentAt}
          onSuccess={handleAuthSuccess}
        />
      )}

      {/* Add UsernameSetupModal */}
      {isUsernameSetupOpen && (
        <UsernameSetupModal />
      )}
    </>
  );
};

export default PublicBoardView; 